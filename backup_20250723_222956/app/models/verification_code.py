from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database import Base
import datetime

class VerificationCode(Base):
    __tablename__ = "verification_codes"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String(100), index=True)
    code: Mapped[str] = mapped_column(String(6))
    code_type: Mapped[str] = mapped_column(String(20), default="reset_password")
    is_used: Mapped[bool] = mapped_column(<PERSON>olean, default=False)
    expires_at: Mapped[datetime.datetime] = mapped_column(DateTime)
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, default=datetime.datetime.utcnow)
    update_time: Mapped[datetime.datetime] = mapped_column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)