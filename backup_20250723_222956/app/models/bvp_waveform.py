from sqlalchemy import Column, Integer, String, Float, Text, DateTime, BigInteger
from sqlalchemy.sql import func
from app.core.database import Base

class BvpWaveform(Base):
    __tablename__ = "bvp_waveform"
    
    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment='主键')
    report_id = Column(String(36), nullable=False, index=True, comment='关联的健康报告ID')
    bvp = Column(Text, nullable=False, comment='BVP波形数组（JSON字符串）')
    timestamps = Column(Text, nullable=False, comment='时间戳数组（JSON字符串）')
    sampling_rate = Column(Float, comment='采样率')
    create_time = Column(DateTime, server_default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
