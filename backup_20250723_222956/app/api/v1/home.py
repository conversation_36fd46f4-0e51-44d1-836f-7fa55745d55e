from fastapi import APIRouter, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
from app.core.auth import extract_token_from_request, get_current_user
from app.crud import family_member as crud_family_member
from app.crud import health_data as crud_health_data
from typing import Optional, Dict, Any
import datetime

router = APIRouter()

@router.get("/{uid}")
@router.get("/{uid}/{fuid}")
async def get_home_data(
    uid: int,
    request: Request,
    fuid: Optional[int] = None
) -> Dict[str, Any]:
    """获取首页数据接口 - 需要token认证"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问其他用户的数据"
            )

        # 初始化默认返回数据（只包含实际存在的字段）
        family_member_data = {
            "fuid": 0,
            "uid": uid,
            "relationship": "",
            "name": "",
            "gender": "",
            "height": 0.0,
            "weight": 0.0,
            "birth_year": 0,
            "age": 0,  # 计算得出的年龄
            "avatar_url": ""
        }
        
        health_report_data = None
        bvp_waveform_data = None

        # 如果指定了fuid，获取家庭成员信息
        if fuid is not None:
            family_member = crud_family_member.get_family_member_by_fuid(db, fuid)
            if family_member and family_member.uid == uid:
                # 计算年龄
                current_year = datetime.datetime.now().year
                age = current_year - family_member.birth_year if family_member.birth_year else 0
                
                family_member_data = {
                    "fuid": family_member.fuid,
                    "uid": family_member.uid,
                    "relationship": family_member.relationship or "",
                    "name": family_member.name or "",
                    "gender": family_member.gender or "",
                    "height": family_member.height or 0.0,
                    "weight": family_member.weight or 0.0,
                    "birth_year": family_member.birth_year or 0,
                    "age": age,
                    "avatar_url": family_member.avatar_url or ""
                }
                
                # 获取该家庭成员的最新健康数据
                health_report_data = crud_health_data.get_latest_health_report_by_fuid(db, uid, fuid)
                if health_report_data:
                    bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        else:
            # fuid为None，获取用户本人的最新健康数据
            health_report_data = crud_health_data.get_latest_health_report_by_uid(db, uid)
            if health_report_data:
                bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        
        return {
            "family_member": family_member_data,
            "health_report": health_report_data,
            "bvp_waveform": bvp_waveform_data,
            "metadata": {
                "uid": uid,
                "fuid": fuid,
                "has_health_data": health_report_data is not None,
                "has_bvp_data": bvp_waveform_data is not None,
                "is_family_member": fuid is not None,
                "query_time": datetime.datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取首页数据失败: {str(e)}"
        )
    finally:
        close_db_session(db)
