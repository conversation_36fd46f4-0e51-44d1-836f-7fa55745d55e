from fastapi import APIRouter, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
import datetime
import json
from typing import List, Optional
from app.schemas.user import User<PERSON><PERSON>, UserLogin, UserResponse
from app.crud import user as crud_user
from app.core.auth import create_access_token, extract_token_from_request, get_current_user
from app.core.rate_limit import login_rate_limiter
from app.schemas.family_member import FamilyMemberCreate, FamilyMemberResponse
from app.crud import family_member as crud_family_member
from app.schemas.password_reset import ForgotPasswordRequest, ResetPasswordRequest, PasswordResetResponse
from app.crud import verification_code as crud_verification_code

router = APIRouter(tags=["users"])

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate):
    """用户注册接口"""
    db = get_db_session()
    try:
        # 检查用户是否已存在
        if crud_user.get_user_by_email(db, user.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 创建用户
        db_user = crud_user.create_user(db, user)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户创建失败"
            )
        
        # 生成token
        token_data = {"sub": str(db_user.uid), "email": db_user.email}
        token = create_access_token(data=token_data)
        
        # 更新用户token
        crud_user.update_user_token(db, db_user.uid, token)
        
        return UserResponse(
            uid=db_user.uid,
            name=db_user.name,
            email=db_user.email,
            token=token
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"注册失败: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.post("/login", response_model=UserResponse)
async def login_user(user_credentials: UserLogin):
    """用户登录接口"""
    db = get_db_session()
    try:
        # 检查暴力破解防护
        is_allowed, remaining_attempts = login_rate_limiter.is_allowed(user_credentials.email)
        if not is_allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"登录尝试次数过多，请{login_rate_limiter.window_minutes}分钟后再试"
            )
        
        # 验证用户
        user = crud_user.authenticate_user(db, user_credentials.email, user_credentials.password)
        if not user:
            # 记录失败的登录尝试
            login_rate_limiter.record_attempt(user_credentials.email)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"邮箱或密码错误，剩余尝试次数: {remaining_attempts - 1}"
            )
        
        # 登录成功，重置尝试记录
        login_rate_limiter.reset_attempts(user_credentials.email)
        
        # 生成新token
        token_data = {"sub": str(user.uid), "email": user.email}
        token = create_access_token(data=token_data)
        
        # 更新用户token
        crud_user.update_user_token(db, user.uid, token)
        
        return UserResponse(
            uid=user.uid,
            name=user.name,
            email=user.email,
            token=token
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"登录失败: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(request: Request):
    """获取当前用户信息 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        user = crud_user.get_user_by_uid(db, int(current_user["sub"]))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse(
            uid=user.uid,
            name=user.name,
            email=user.email,
            token=user.token
        )
    finally:
        close_db_session(db)

@router.post("/addfamily", response_model=FamilyMemberResponse, status_code=status.HTTP_201_CREATED)
async def add_family_member(
    family_member: FamilyMemberCreate,
    request: Request
):
    """添加家庭成员接口 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    # 验证用户权限：确保当前用户只能为自己添加家庭成员
    current_user_id = int(current_user.get("sub", 0))
    if family_member.uid != current_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权为其他用户添加家庭成员"
        )
    
    db = get_db_session()
    try:
        # 创建家庭成员
        db_family_member = crud_family_member.create_family_member(db, family_member)
        if not db_family_member:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="家庭成员创建失败"
            )
        
        return FamilyMemberResponse(
            fuid=db_family_member.fuid,
            uid=db_family_member.uid,
            relationship=db_family_member.relationship,
            name=db_family_member.name,
            gender=db_family_member.gender,
            height=db_family_member.height,
            weight=db_family_member.weight,
            birth_year=db_family_member.birth_year,
            avatar_url=db_family_member.avatar_url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加家庭成员失败: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.get("/{uid}/familylist", response_model=List[FamilyMemberResponse])
async def get_family_list(
    uid: int,
    request: Request
):
    """获取家庭成员列表接口 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    # 验证用户权限：确保当前用户只能查看自己的家庭成员
    current_user_id = int(current_user.get("sub", 0))
    if uid != current_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权查看其他用户的家庭成员"
        )
    
    db = get_db_session()
    try:
        family_members = crud_family_member.get_family_members_by_uid(db, uid)
        return family_members
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家庭成员列表失败: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.post("/forgotpassword", response_model=PasswordResetResponse)
async def forgot_password(request_data: ForgotPasswordRequest):
    """发送找回密码验证码"""
    db = get_db_session()
    try:
        print(f"收到找回密码请求: {request_data.email}")
        
        # 检查邮箱是否存在
        user = crud_user.get_user_by_email(db, request_data.email)
        if not user:
            print(f"邮箱不存在: {request_data.email}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮箱地址不存在"
            )
        
        print(f"邮箱存在，检查发送频率限制")
        
        # 检查发送频率限制
        from app.core.rate_limit import email_rate_limiter
        is_allowed, remaining = email_rate_limiter.is_allowed(request_data.email)
        if not is_allowed:
            print(f"发送频率限制: {request_data.email}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="发送过于频繁，请1分钟后再试"
            )
        
        print(f"创建验证码")
        # 创建验证码
        verification_code = crud_verification_code.create_verification_code(db, request_data.email)
        print(f"验证码创建成功: {verification_code.code}")
        
        # 发送邮件
        print(f"开始发送邮件")
        from app.utils.email_service import email_service
        email_result = email_service.send_verification_code(request_data.email, verification_code.code)
        print(f"邮件发送结果: {email_result}")
        
        if email_result:
            # 记录发送尝试
            email_rate_limiter.record_attempt(request_data.email)
            return PasswordResetResponse(message="验证码已发送到您的邮箱，请查收")
        else:
            print(f"邮件发送失败")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="邮件发送失败，请稍后重试"
            )
    except HTTPException:
        raise
    except Exception as e:
        print(f"找回密码失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.post("/resetpassword", response_model=PasswordResetResponse)
async def reset_password(request_data: ResetPasswordRequest):
    """重置密码"""
    db = get_db_session()
    try:
        # 检查邮箱是否存在
        user = crud_user.get_user_by_email(db, request_data.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮箱地址不存在"
            )
        
        # 验证验证码
        if not crud_verification_code.verify_code(db, request_data.email, request_data.verification_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码错误或已过期"
            )
        
        # 更新密码
        from app.crud.user import get_password_hash
        hashed_password = get_password_hash(request_data.new_password)
        user.password = hashed_password
        db.commit()
        
        return PasswordResetResponse(message="密码重置成功")
    except HTTPException:
        raise
    except Exception as e:
        print(f"重置密码失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
    finally:
        close_db_session(db)
