from sqlalchemy.orm import Session
from app.models.verification_code import VerificationCode
from datetime import datetime, timedelta
import random
import string

def create_verification_code(db: Session, email: str, code_type: str = "reset_password") -> VerificationCode:
    """创建验证码"""
    # 生成6位数字验证码
    code = ''.join(random.choices(string.digits, k=6))
    
    # 设置过期时间（10分钟后）
    expires_at = datetime.utcnow() + timedelta(minutes=10)
    
    # 先将该邮箱的旧验证码标记为已使用
    db.query(VerificationCode).filter(
        VerificationCode.email == email,
        VerificationCode.code_type == code_type,
        VerificationCode.is_used == False
    ).update({"is_used": True})
    
    # 创建新验证码
    db_code = VerificationCode(
        email=email,
        code=code,
        code_type=code_type,
        expires_at=expires_at
    )
    db.add(db_code)
    db.commit()
    db.refresh(db_code)
    return db_code

def verify_code(db: Session, email: str, code: str, code_type: str = "reset_password") -> bool:
    """验证验证码"""
    verification_code = db.query(VerificationCode).filter(
        VerificationCode.email == email,
        VerificationCode.code == code,
        VerificationCode.code_type == code_type,
        VerificationCode.is_used == False,
        VerificationCode.expires_at > datetime.utcnow()
    ).first()
    
    if verification_code:
        # 标记为已使用
        verification_code.is_used = True
        db.commit()
        return True
    return False

def can_send_code(db: Session, email: str, code_type: str = "reset_password") -> bool:
    """检查是否可以发送验证码（1分钟内只能发送一次）"""
    one_minute_ago = datetime.utcnow() - timedelta(minutes=1)
    recent_code = db.query(VerificationCode).filter(
        VerificationCode.email == email,
        VerificationCode.code_type == code_type,
        VerificationCode.create_time > one_minute_ago
    ).first()
    
    return recent_code is None