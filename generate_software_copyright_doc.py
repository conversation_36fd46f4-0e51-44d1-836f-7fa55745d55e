#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软著代码功能介绍文档生成脚本
用于生成软件著作权申请所需的代码功能介绍文档
"""

import os
import datetime
from pathlib import Path

def get_project_info():
    """获取项目基本信息"""
    project_root = "/Users/<USER>/PycharmProjects/FastAPI_v4/VideoProject"
    project_name = "健康检测系统"
    project_description = "基于FastAPI的视频健康检测分析系统"
    
    return {
        "project_root": project_root,
        "project_name": project_name,
        "project_description": project_description,
        "generate_time": datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
    }

def read_code_snippet(file_path, start_line, end_line):
    """读取指定文件的代码片段"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # 转换为0基索引
            start_idx = start_line - 1
            end_idx = end_line
            if start_idx < 0:
                start_idx = 0
            if end_idx > len(lines):
                end_idx = len(lines)

            code_lines = lines[start_idx:end_idx]
            return ''.join(code_lines)
    except Exception as e:
        return f"# 无法读取代码: {str(e)}"

def get_api_functions():
    """获取API接口功能列表"""
    functions = [
        {
            "name": "用户注册功能",
            "path": "app/api/v1/user.py",
            "endpoint": "POST /api/v1/users/register",
            "description": "用户注册接口，支持邮箱注册，自动生成JWT token",
            "key_features": [
                "邮箱唯一性验证",
                "密码加密存储",
                "自动生成访问令牌",
                "用户信息入库"
            ],
            "code_lines": "18-50",
            "start_line": 18,
            "end_line": 50
        },
        {
            "name": "用户登录功能",
            "path": "app/api/v1/user.py",
            "endpoint": "POST /api/v1/users/login",
            "description": "用户登录接口，支持邮箱密码登录，包含防暴力破解机制",
            "key_features": [
                "邮箱密码验证",
                "登录频率限制",
                "JWT token生成",
                "登录状态管理"
            ],
            "code_lines": "64-114",
            "start_line": 64,
            "end_line": 114
        },
        {
            "name": "用户信息获取功能",
            "path": "app/api/v1/user.py",
            "endpoint": "GET /api/v1/users/me",
            "description": "获取当前登录用户信息，需要token认证",
            "key_features": [
                "Token身份验证",
                "用户信息查询",
                "权限验证",
                "数据返回格式化"
            ],
            "code_lines": "116-138",
            "start_line": 116,
            "end_line": 138
        },
        {
            "name": "家庭成员管理功能",
            "path": "app/api/v1/user.py",
            "endpoint": "POST /api/v1/users/addfamily",
            "description": "添加家庭成员信息，支持多成员健康管理",
            "key_features": [
                "家庭成员信息录入",
                "关系类型管理",
                "权限控制验证",
                "成员数据存储"
            ],
            "code_lines": "140-187",
            "start_line": 140,
            "end_line": 187
        },
        {
            "name": "家庭成员列表功能",
            "path": "app/api/v1/user.py",
            "endpoint": "GET /api/v1/users/{uid}/familylist",
            "description": "获取用户的家庭成员列表信息",
            "key_features": [
                "成员列表查询",
                "数据权限控制",
                "信息格式化输出",
                "关联数据获取"
            ],
            "code_lines": "189-220",
            "start_line": 189,
            "end_line": 220
        },
        {
            "name": "首页数据获取功能",
            "path": "app/api/v1/home.py",
            "endpoint": "GET /api/v1/home/<USER>",
            "description": "获取用户首页展示数据，包含健康概览信息",
            "key_features": [
                "用户健康数据汇总",
                "家庭成员健康状态",
                "最新检测报告",
                "数据可视化支持"
            ],
            "code_lines": "12-80",
            "start_line": 12,
            "end_line": 80
        },
        {
            "name": "视频健康分析功能",
            "path": "app/api/v1/health.py",
            "endpoint": "POST /api/v1/health/analyze",
            "description": "核心功能：通过视频分析用户健康指标",
            "key_features": [
                "视频文件上传处理",
                "rPPG算法健康分析",
                "心率血压检测",
                "健康风险评估",
                "分析结果存储"
            ],
            "code_lines": "80-150",
            "start_line": 80,
            "end_line": 150
        },
        {
            "name": "健康报告查询功能",
            "path": "app/api/v1/health.py",
            "endpoint": "GET /api/v1/health/reports/{uid}",
            "description": "查询用户历史健康检测报告",
            "key_features": [
                "报告列表查询",
                "时间范围筛选",
                "详细数据获取",
                "趋势分析支持"
            ],
            "code_lines": "350-400",
            "start_line": 350,
            "end_line": 400
        }
    ]
    return functions

def get_core_modules():
    """获取核心模块信息"""
    modules = [
        {
            "name": "数据库模型层",
            "path": "app/models/",
            "description": "定义用户、健康报告、家庭成员等数据模型",
            "files": ["user.py", "health_report.py", "family_member.py", "bvp_waveform.py"]
        },
        {
            "name": "数据访问层",
            "path": "app/crud/",
            "description": "数据库操作封装，提供增删改查接口",
            "files": ["user.py", "health_data.py", "family_member.py"]
        },
        {
            "name": "身份认证模块",
            "path": "app/core/auth.py",
            "description": "JWT token生成验证，用户身份认证",
            "files": ["auth.py"]
        },
        {
            "name": "健康分析引擎",
            "path": "app/utils/health_analyzer.py",
            "description": "rPPG算法实现，视频健康指标分析",
            "files": ["health_analyzer.py", "rppg_tool.py"]
        },
        {
            "name": "配置管理模块",
            "path": "app/core/config.py", 
            "description": "系统配置参数管理",
            "files": ["config.py", "database.py"]
        }
    ]
    return modules

def generate_document():
    """生成软著代码功能介绍文档"""
    project_info = get_project_info()
    api_functions = get_api_functions()
    core_modules = get_core_modules()
    
    content = f"""# {project_info['project_name']} - 软件著作权代码功能介绍

## 项目基本信息

**项目名称：** {project_info['project_name']}
**项目描述：** {project_info['project_description']}
**项目路径：** {project_info['project_root']}
**文档生成时间：** {project_info['generate_time']}

## 系统架构说明

本系统采用FastAPI框架构建，基于Python 3.9开发，采用分层架构设计：
- API接口层：处理HTTP请求响应
- 业务逻辑层：实现核心业务功能
- 数据访问层：封装数据库操作
- 数据模型层：定义数据结构

## 主要功能模块及代码实现

"""

    # 添加API接口功能
    content += "### API接口功能\n\n"
    for i, func in enumerate(api_functions, 1):
        content += f"#### {i}. {func['name']}\n\n"
        content += f"**接口地址：** `{func['endpoint']}`\n"
        content += f"**代码文件：** `{func['path']}`\n"
        content += f"**代码行数：** {func['code_lines']}\n"
        content += f"**功能描述：** {func['description']}\n\n"
        content += "**核心功能特性：**\n"
        for feature in func['key_features']:
            content += f"- {feature}\n"
        content += "\n"

        # 添加具体代码实现
        if 'start_line' in func and 'end_line' in func:
            content += "**代码实现：**\n\n"
            content += "```python\n"
            code_snippet = read_code_snippet(func['path'], func['start_line'], func['end_line'])
            content += code_snippet
            content += "```\n\n"
    
    # 添加核心模块
    content += "### 核心模块组件\n\n"
    for i, module in enumerate(core_modules, 1):
        content += f"#### {i}. {module['name']}\n\n"
        content += f"**模块路径：** `{module['path']}`\n"
        content += f"**功能描述：** {module['description']}\n"
        content += f"**包含文件：** {', '.join(module['files'])}\n\n"
    
    # 添加技术特色
    content += """### 技术特色与创新点

1. **rPPG视频健康检测技术**
   - 基于远程光电容积脉搏波技术
   - 通过普通摄像头实现非接触式健康检测
   - 支持心率、血压、血氧饱和度等多项指标分析

2. **智能健康风险评估**
   - 心脏风险评估算法
   - 脑血管风险分析
   - 房颤风险检测

3. **多用户家庭健康管理**
   - 支持家庭成员健康数据管理
   - 个性化健康报告生成
   - 历史数据趋势分析

4. **高性能异步处理架构**
   - FastAPI异步框架
   - 并发视频处理能力
   - 数据库连接池优化

### 代码质量保证

- 完整的异常处理机制
- 详细的日志记录系统
- 数据验证与安全防护
- RESTful API设计规范
- 模块化代码组织结构

### 部署与运维

- Docker容器化部署
- 数据库迁移脚本
- 日志轮转配置
- 性能监控机制

---
*本文档由系统自动生成，用于软件著作权申请材料*
"""
    
    return content

def main():
    """主函数"""
    print("正在生成软著代码功能介绍文档...")
    
    # 生成文档内容
    document_content = generate_document()
    
    # 写入文件
    output_file = "a.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(document_content)
    
    print(f"文档已生成完成！")
    print(f"输出文件：{output_file}")
    print(f"文档大小：{len(document_content)} 字符")
    print("\n文档内容预览：")
    print("=" * 50)
    print(document_content[:500] + "...")

if __name__ == "__main__":
    main()
