import os
import requests
from urllib.parse import urlparse

class OssClient:
    """
    阿里云OSS操作工具类，支持下载和删除文件
    """
    def __init__(self):
        # 读取环境变量
        self.access_key_id = os.getenv('OSS_ACCESS_KEY_ID')
        self.access_key_secret = os.getenv('OSS_ACCESS_KEY_SECRET')
        self.region = os.getenv('OSS_REGION')
        self.bucket = os.getenv('OSS_BUCKET')
        self.endpoint = os.getenv('OSS_ENDPOINT')

    def download(self, object_key: str, local_path: str) -> bool:
        """
        下载OSS上的object到本地
        :param object_key: OSS上的文件key（如 video/test.mp4）
        :param local_path: 下载到本地的完整路径
        :return: 下载成功返回True，否则抛异常
        """
        if not all([self.access_key_id, self.access_key_secret, self.region, self.bucket, self.endpoint]):
            raise ValueError('OSS配置不完整，请检查环境变量')
        
        try:
            # 构建OSS URL
            oss_url = f"https://{self.bucket}.{self.endpoint}/{object_key}"
            
            # 使用requests下载，更简单可靠
            response = requests.get(oss_url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            return True
        except Exception as e:
            raise RuntimeError(f'OSS下载失败: {e}')

    def delete(self, object_key: str) -> bool:
        """
        删除OSS上的object
        :param object_key: OSS上的文件key（如 video/test.mp4）
        :return: 删除成功返回True，否则抛异常
        """
        if not all([self.access_key_id, self.access_key_secret, self.region, self.bucket, self.endpoint]):
            raise ValueError('OSS配置不完整，请检查环境变量')
        try:
            import alibabacloud_oss_v2 as oss
            # 配置凭证
            credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()
            cfg = oss.config.load_default()
            cfg.credentials_provider = credentials_provider
            cfg.region = self.region
            cfg.endpoint = self.endpoint
            client = oss.Client(cfg)
            # 删除文件
            client.delete_object(oss.DeleteObjectRequest(
                bucket=self.bucket,
                key=object_key
            ))
            return True
        except ImportError:
            raise ImportError('请先安装alibabacloud-oss-v2: pip install alibabacloud-oss-v2')
        except Exception as e:
            raise RuntimeError(f'OSS删除失败: {e}') 