from pydantic import BaseModel, EmailStr, validator

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    verification_code: str
    new_password: str
    
    @validator('verification_code')
    def validate_verification_code(cls, v):
        if not v or len(v) != 6 or not v.isdigit():
            raise ValueError('验证码必须是6位数字')
        return v
    
    @validator('new_password')
    def validate_password(cls, v):
        if not v or len(v) < 6:
            raise ValueError('密码长度不能少于6位')
        return v

class PasswordResetResponse(BaseModel):
    message: str
    success: bool = True