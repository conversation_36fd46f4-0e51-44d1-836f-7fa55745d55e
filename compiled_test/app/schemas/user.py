from pydantic import BaseModel, EmailStr
from typing import Optional

class UserBase(BaseModel):
    name: str
    email: EmailStr

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserUpdate(UserBase):
    password: Optional[str] = None

class UserInDB(UserBase):
    uid: int
    token: Optional[str] = None

    class Config:
        from_attributes = True

class UserResponse(BaseModel):
    uid: int
    name: str
    email: EmailStr
    token: str

    class Config:
        from_attributes = True 