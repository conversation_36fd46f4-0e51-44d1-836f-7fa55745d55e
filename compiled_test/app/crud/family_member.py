from sqlalchemy.orm import Session
from app.models.family_member import FamilyMember
from app.schemas.family_member import FamilyMemberCreate
from typing import List

def create_family_member(db: Session, family_member: FamilyMemberCreate):
    """创建家庭成员"""
    db_family_member = FamilyMember(**family_member.dict())
    db.add(db_family_member)
    db.commit()
    db.refresh(db_family_member)
    return db_family_member

def get_family_members_by_uid(db: Session, uid: int) -> List[FamilyMember]:
    """获取用户的所有家庭成员"""
    return db.query(FamilyMember).filter(FamilyMember.uid == uid).all()

def count_family_members_by_uid(db: Session, uid: int) -> int:
    """统计用户的家庭成员数量"""
    return db.query(FamilyMember).filter(FamilyMember.uid == uid).count()

def get_family_member_by_fuid(db: Session, fuid: int):
    """根据fuid获取家庭成员"""
    return db.query(FamilyMember).filter(FamilyMember.fuid == fuid).first()