from sqlalchemy.orm import Session
from app.models.user import User
from app.schemas.user import UserCreate
from app.core.auth import verify_password

# 避免循环导入
def get_password_hash(password: str) -> str:
    """获取密码哈希值 - 避免循环导入"""
    from app.core.auth import get_password_hash as _get_password_hash
    return _get_password_hash(password)

def get_user_by_uid(db: Session, uid: int):
    """通过用户ID获取用户"""
    return db.query(User).filter(User.uid == uid).first()

def get_user_by_email(db: Session, email: str):
    """通过邮箱获取用户"""
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, user: UserCreate):
    # 检查邮箱是否已存在
    if get_user_by_email(db, user.email):
        return None
    
    # 创建用户对象，密码需要加密
    hashed_password = get_password_hash(user.password)
    
    # 创建用户，设置一个临时token
    db_user = User(
        name=user.name,
        email=user.email,
        password=hashed_password,
        token=""
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, email: str, password: str):
    """验证用户登录"""
    user = get_user_by_email(db, email)
    if not user:
        return None
    # 验证密码
    if not verify_password(password, user.password):
        return None
    return user

def update_user_token(db: Session, uid: int, token: str):
    """更新用户token"""
    user = get_user_by_uid(db, uid)
    if user:
        setattr(user, 'token', token)
        db.commit()
        db.refresh(user)
    return user


