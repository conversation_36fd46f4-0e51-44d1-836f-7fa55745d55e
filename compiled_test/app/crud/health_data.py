from sqlalchemy.orm import Session
from typing import Optional, List
from app.models.health_report import HealthReport  # 确保使用绝对导入
from app.models.bvp_waveform import BvpWaveform
import datetime  # 添加datetime导入

def get_latest_health_report_by_name(db: Session, name: str) -> Optional[HealthReport]:
    """根据姓名获取最新的健康报告（保持向后兼容）"""
    return db.query(HealthReport).filter(
        HealthReport.name == name
    ).order_by(HealthReport.create_time.desc()).first()

def get_latest_health_report_by_uid(db: Session, uid: int) -> Optional[HealthReport]:
    """根据用户ID获取最新的健康报告"""
    return db.query(HealthReport).filter(
        HealthReport.uid == uid,
        HealthReport.fuid.is_(None)  # 用户本人的数据
    ).order_by(HealthReport.create_time.desc()).first()

def get_latest_health_report_by_fuid(db: Session, uid: int, fuid: int) -> Optional[HealthReport]:
    """根据家庭成员ID获取最新的健康报告"""
    return db.query(HealthReport).filter(
        HealthReport.uid == uid,
        HealthReport.fuid == fuid
    ).order_by(HealthReport.create_time.desc()).first()

def get_health_reports_by_uid(db: Session, uid: int, limit: int = 10) -> List[HealthReport]:
    """获取用户的所有健康报告（包括家庭成员）"""
    return db.query(HealthReport).filter(
        HealthReport.uid == uid
    ).order_by(HealthReport.create_time.desc()).limit(limit).all()

def get_health_reports_by_uid_and_fuid(db: Session, uid: int, fuid: Optional[int] = None, limit: int = 10) -> List[HealthReport]:
    """根据用户ID和家庭成员ID获取健康报告列表"""
    query = db.query(HealthReport).filter(HealthReport.uid == uid)
    
    if fuid is None:
        # 获取用户本人的数据
        query = query.filter(HealthReport.fuid.is_(None))
    else:
        # 获取指定家庭成员的数据
        query = query.filter(HealthReport.fuid == fuid)
    
    return query.order_by(HealthReport.create_time.desc()).limit(limit).all()

def get_bvp_waveform_by_report_id(db: Session, report_id: str) -> Optional[BvpWaveform]:
    """根据report_id获取BVP波形数据"""
    return db.query(BvpWaveform).filter(
        BvpWaveform.report_id == report_id
    ).first()
