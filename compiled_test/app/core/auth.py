from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
from app.core.config import settings
import json

# 配置
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = int(settings.ACCESS_TOKEN_EXPIRE_MINUTES)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希值"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def extract_token_from_request(request: Request) -> str:
    """从Request对象中提取token - Cython兼容版本"""
    authorization = request.headers.get("authorization") or request.headers.get("Authorization")
    
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少Authorization header",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的Authorization header格式，应为: Bearer <token>",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return authorization.split(" ")[1]

def get_current_user(token: str) -> Dict[str, Any]:
    """验证token并获取用户信息 - Cython兼容版本"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return {
            "sub": user_id,
            "exp": payload.get("exp"),
            "iat": payload.get("iat")
        }
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_current_user_with_db(token: str):
    """获取当前用户信息（包含数据库查询）- Cython兼容版本"""
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        from app.crud import user as crud_user
        
        user = crud_user.get_user_by_uid(db, int(current_user["sub"]))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return user
    finally:
        close_db_session(db)

def get_current_user_from_request(request: Request) -> Dict[str, Any]:
    """从Request对象中提取并验证用户信息的便捷函数"""
    token = extract_token_from_request(request)
    return get_current_user(token)
