#!/usr/bin/env python3
"""
Vue健康检测应用前端服务真实状态验证
"""

import requests
import time
from datetime import datetime

def test_frontend_real_status():
    """测试前端服务真实状态"""
    print("🔍 Vue健康检测应用前端服务真实状态检测")
    print("=" * 70)
    
    # 测试端口3002（当前Vue服务端口）
    print("\n📡 测试端口3002（当前Vue服务端口）")
    print("-" * 50)
    
    pages_to_test = [
        ("根路径", "http://localhost:3002/"),
        ("登录页面", "http://localhost:3002/login"),
        ("首页", "http://localhost:3002/home"),
        ("健康扫描", "http://localhost:3002/scan"),
        ("家庭成员", "http://localhost:3002/family"),
        ("个人中心", "http://localhost:3002/profile"),
        ("健康报告", "http://localhost:3002/report")
    ]
    
    accessible_count = 0
    total_count = len(pages_to_test)
    
    for page_name, url in pages_to_test:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {page_name} - HTTP 200 OK")
                accessible_count += 1
            else:
                print(f"  ❌ {page_name} - HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ {page_name} - 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ❌ {page_name} - 请求超时")
        except Exception as e:
            print(f"  ❌ {page_name} - 错误: {e}")
    
    # 测试端口3002（用户期望的端口）
    print("\n📡 测试端口3002（用户期望的端口）")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:3002/", timeout=5)
        if response.status_code == 200:
            print("  ✅ 端口3002 - 服务正常运行")
        else:
            print(f"  ❌ 端口3002 - HTTP {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("  ❌ 端口3002 - 连接被拒绝（服务未运行）")
    except Exception as e:
        print(f"  ❌ 端口3002 - 错误: {e}")
    
    # 计算成功率
    success_rate = (accessible_count / total_count) * 100
    
    print(f"\n📊 端口3001页面可访问性: {accessible_count}/{total_count} ({success_rate:.1f}%)")
    
    return accessible_count, total_count, success_rate

def test_page_content():
    """测试页面内容是否正确加载"""
    print("\n🔍 测试页面内容加载")
    print("-" * 50)
    
    try:
        # 测试登录页面内容
        response = requests.get("http://localhost:3002/login", timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            if 'vue' in content or 'vite' in content or 'app' in content or 'main.js' in content:
                print("  ✅ 登录页面 - Vue应用正确加载")
                return True
            else:
                print("  ❌ 登录页面 - 内容异常，可能不是Vue应用")
                print(f"  📝 页面内容预览: {content[:200]}...")
                return False
        else:
            print(f"  ❌ 登录页面 - HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 登录页面内容测试失败: {e}")
        return False

def generate_real_status_report(accessible_count, total_count, success_rate, content_ok):
    """生成真实状态报告"""
    print("\n" + "=" * 80)
    print("📋 Vue健康检测应用前端服务真实状态报告")
    print("=" * 80)
    
    print(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"页面可访问性: {accessible_count}/{total_count} ({success_rate:.1f}%)")
    print(f"页面内容正确性: {'✅ 正常' if content_ok else '❌ 异常'}")
    
    if success_rate >= 80 and content_ok:
        print("\n🎉 前端服务状态：正常运行！")
        
        print("\n✅ 服务状态:")
        print("  - Vue开发服务器正在端口3002运行")
        print("  - 所有主要页面可以正常访问")
        print("  - Vue应用正确加载")
        print("  - 页面内容正常")
        
        print("\n🌐 正确的访问地址:")
        print("  - 前端应用: http://localhost:3002/")
        print("  - 登录页面: http://localhost:3002/login")
        print("  - 首页: http://localhost:3002/home")
        print("  - 健康扫描: http://localhost:3002/scan")
        print("  - 家庭成员: http://localhost:3002/family")
        print("  - 个人中心: http://localhost:3002/profile")
        print("  - 健康报告: http://localhost:3002/report")
        
        print("\n⚠️ 重要提醒:")
        print("  - Vue服务运行在端口3001，不是3002")
        print("  - 请使用 http://localhost:3001/ 访问应用")
        print("  - 如需使用端口3002，需要修改vite.config.js配置")
        
        print("\n💡 如何固定使用端口3002:")
        print("  1. 编辑 VUE/vite.config.js")
        print("  2. 在server配置中添加: port: 3002")
        print("  3. 重启Vue开发服务器")
        
    elif success_rate >= 50:
        print("\n⚠️ 前端服务状态：部分可用")
        print("  - 服务基本运行，但部分页面有问题")
        print("  - 建议检查Vue路由配置")
        
    else:
        print("\n❌ 前端服务状态：严重问题")
        print("  - 服务存在严重问题")
        print("  - 需要检查Vue开发服务器启动状态")
        print("  - 检查控制台错误日志")

def main():
    """主检测函数"""
    print("🚀 开始Vue健康检测应用前端服务真实状态检测")
    print("解决用户无法访问登录页面的问题")
    print("=" * 80)
    
    # 等待服务稳定
    print("⏳ 等待服务稳定...")
    time.sleep(2)
    
    # 执行检测
    accessible_count, total_count, success_rate = test_frontend_real_status()
    content_ok = test_page_content()
    
    # 生成报告
    generate_real_status_report(accessible_count, total_count, success_rate, content_ok)

if __name__ == "__main__":
    main()
