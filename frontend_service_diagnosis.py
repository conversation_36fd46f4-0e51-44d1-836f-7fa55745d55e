#!/usr/bin/env python3
"""
Vue健康检测应用前端服务诊断和修复验证
"""

import os
import time
import requests
import subprocess
from datetime import datetime

def check_frontend_service_status():
    """检查前端服务状态"""
    print("🔍 诊断前端服务状态")
    print("=" * 60)
    
    # 检查端口3002是否有服务运行
    try:
        result = subprocess.run(['lsof', '-i', ':3002'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            print("  ✅ 端口3002有服务运行")
            print(f"  📋 进程信息:\n{result.stdout}")
            return True
        else:
            print("  ❌ 端口3002没有服务运行")
            return False
    except Exception as e:
        print(f"  ❌ 检查端口状态失败: {e}")
        return False

def check_vue_project_structure():
    """检查Vue项目结构"""
    print("\n🏗️ 检查Vue项目结构")
    print("=" * 60)
    
    vue_path = "/Users/<USER>/PycharmProjects/FastAPI_v4/VideoProject/VUE"
    
    required_files = [
        "package.json",
        "vite.config.js", 
        "src/main.js",
        "src/App.vue",
        "index.html"
    ]
    
    required_dirs = [
        "src",
        "src/views",
        "src/components", 
        "src/stores",
        "src/router",
        "node_modules"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file in required_files:
        file_path = os.path.join(vue_path, file)
        if os.path.exists(file_path):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing_files.append(file)
    
    # 检查目录
    for dir in required_dirs:
        dir_path = os.path.join(vue_path, dir)
        if os.path.exists(dir_path):
            print(f"  ✅ {dir}/")
        else:
            print(f"  ❌ {dir}/")
            missing_dirs.append(dir)
    
    if missing_files or missing_dirs:
        print(f"\n  ⚠️ 缺失文件: {missing_files}")
        print(f"  ⚠️ 缺失目录: {missing_dirs}")
        return False
    else:
        print(f"\n  🎉 Vue项目结构完整")
        return True

def test_page_accessibility():
    """测试页面可访问性"""
    print("\n🌐 测试页面可访问性")
    print("=" * 60)
    
    pages_to_test = [
        ("根路径", "http://localhost:3002/"),
        ("登录页面", "http://localhost:3002/login"),
        ("首页", "http://localhost:3002/home"),
        ("健康扫描", "http://localhost:3002/scan"),
        ("家庭成员", "http://localhost:3002/family"),
        ("个人中心", "http://localhost:3002/profile"),
        ("健康报告", "http://localhost:3002/report")
    ]
    
    accessible_pages = 0
    total_pages = len(pages_to_test)
    
    for page_name, url in pages_to_test:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {page_name} - HTTP 200 OK")
                accessible_pages += 1
            else:
                print(f"  ❌ {page_name} - HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ {page_name} - 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ❌ {page_name} - 请求超时")
        except Exception as e:
            print(f"  ❌ {page_name} - 错误: {e}")
    
    success_rate = (accessible_pages / total_pages) * 100
    print(f"\n  📊 页面可访问性: {accessible_pages}/{total_pages} ({success_rate:.1f}%)")
    
    return accessible_pages >= total_pages * 0.8

def check_console_errors():
    """检查Vue开发服务器控制台是否有错误"""
    print("\n🔍 检查开发服务器状态")
    print("=" * 60)
    
    try:
        # 检查Vue开发服务器是否正在运行
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=10)
        
        vue_processes = []
        for line in result.stdout.split('\n'):
            if 'vite' in line.lower() or 'npm run dev' in line:
                vue_processes.append(line.strip())
        
        if vue_processes:
            print("  ✅ 发现Vue开发服务器进程:")
            for process in vue_processes:
                print(f"    {process}")
            return True
        else:
            print("  ❌ 未发现Vue开发服务器进程")
            return False
            
    except Exception as e:
        print(f"  ❌ 检查进程失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n⚙️ 测试基本功能")
    print("=" * 60)
    
    tests = []
    
    # 测试静态资源加载
    try:
        response = requests.get("http://localhost:3002/src/main.js", timeout=5)
        if response.status_code == 200:
            tests.append(("静态资源加载", True))
            print("  ✅ 静态资源加载正常")
        else:
            tests.append(("静态资源加载", False))
            print("  ❌ 静态资源加载失败")
    except Exception as e:
        tests.append(("静态资源加载", False))
        print(f"  ❌ 静态资源加载错误: {e}")
    
    # 测试API代理（如果配置了的话）
    try:
        response = requests.get("http://localhost:3002/api/health", timeout=5)
        if response.status_code in [200, 404, 500]:  # 任何响应都说明代理工作
            tests.append(("API代理", True))
            print("  ✅ API代理配置正常")
        else:
            tests.append(("API代理", False))
            print("  ❌ API代理配置异常")
    except Exception:
        tests.append(("API代理", False))
        print("  ⚠️ API代理未配置或无法访问")
    
    # 测试Vue路由
    try:
        response = requests.get("http://localhost:3002/login", timeout=5)
        if response.status_code == 200 and 'vue' in response.text.lower():
            tests.append(("Vue路由", True))
            print("  ✅ Vue路由工作正常")
        else:
            tests.append(("Vue路由", False))
            print("  ❌ Vue路由可能有问题")
    except Exception as e:
        tests.append(("Vue路由", False))
        print(f"  ❌ Vue路由测试失败: {e}")
    
    success_count = sum(1 for _, success in tests if success)
    total_count = len(tests)
    
    print(f"\n  📊 基本功能测试: {success_count}/{total_count} ({(success_count/total_count*100):.1f}%)")
    
    return success_count >= total_count * 0.7

def generate_diagnosis_report(results):
    """生成诊断报告"""
    print("\n" + "=" * 80)
    print("📋 Vue健康检测应用前端服务诊断报告")
    print("=" * 80)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"服务健康度: {success_rate:.1f}%")
    
    print("\n📊 详细结果:")
    for check_name, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {check_name}: {status}")
    
    if success_rate >= 80:
        print("\n🎉 前端服务诊断结果：健康状态良好！")
        
        print("\n✅ 服务状态:")
        print("  - Vue开发服务器正在运行")
        print("  - 端口3002可正常访问")
        print("  - 所有主要页面可以加载")
        print("  - 项目结构完整")
        print("  - 基本功能正常")
        
        print("\n🚀 可用功能:")
        print("  - 🌐 前端应用: http://localhost:3002/")
        print("  - 🔐 登录页面: http://localhost:3002/login")
        print("  - 🏠 首页: http://localhost:3002/home")
        print("  - 📹 健康扫描: http://localhost:3002/scan")
        print("  - 👨‍👩‍👧‍👦 家庭成员: http://localhost:3002/family")
        print("  - 👤 个人中心: http://localhost:3002/profile")
        print("  - 📊 健康报告: http://localhost:3002/report")
        
        print("\n💡 建议:")
        print("  - 前端服务运行正常，可以开始使用应用")
        print("  - 确保后端API服务也在运行以获得完整功能")
        print("  - 定期检查控制台日志以发现潜在问题")
        
    elif success_rate >= 60:
        print("\n⚠️ 前端服务诊断结果：部分功能异常")
        print("  - 服务基本可用，但存在一些问题")
        print("  - 建议检查失败的项目并进行修复")
        
    else:
        print("\n❌ 前端服务诊断结果：严重问题")
        print("  - 服务存在严重问题，需要立即修复")
        print("  - 请检查Vue开发服务器是否正确启动")
        print("  - 检查项目依赖是否正确安装")

def main():
    """主诊断函数"""
    print("🚀 开始Vue健康检测应用前端服务诊断")
    print("诊断前端服务状态和页面可访问性")
    print("=" * 80)
    
    # 等待服务稳定
    print("⏳ 等待服务稳定...")
    time.sleep(3)
    
    # 执行诊断
    results = {}
    
    try:
        results["前端服务状态"] = check_frontend_service_status()
    except Exception as e:
        print(f"❌ 前端服务状态检查异常: {e}")
        results["前端服务状态"] = False
    
    try:
        results["项目结构完整性"] = check_vue_project_structure()
    except Exception as e:
        print(f"❌ 项目结构检查异常: {e}")
        results["项目结构完整性"] = False
    
    try:
        results["页面可访问性"] = test_page_accessibility()
    except Exception as e:
        print(f"❌ 页面可访问性检查异常: {e}")
        results["页面可访问性"] = False
    
    try:
        results["开发服务器状态"] = check_console_errors()
    except Exception as e:
        print(f"❌ 开发服务器状态检查异常: {e}")
        results["开发服务器状态"] = False
    
    try:
        results["基本功能测试"] = test_basic_functionality()
    except Exception as e:
        print(f"❌ 基本功能测试异常: {e}")
        results["基本功能测试"] = False
    
    # 生成报告
    generate_diagnosis_report(results)

if __name__ == "__main__":
    main()
