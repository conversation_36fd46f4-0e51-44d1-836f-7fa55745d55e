# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.reIeGCggJnCIAoQYs_W1PiPqIeoyiV9N0elaA7Ps9L4 | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> Jwt Bearer

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和Token

> Authorization: Bearer your_token

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 接口是否正常验证接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 12:08:37

> 更新时间: 2025-07-23 22:14:55

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/health/hello

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/health/hello?apipost_id=18564857ff053

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
{"message":"Hello, World!","status":"healthy"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| message | Hello, World! | string | 信息 |
| status | healthy | string | 状态 |

* 失败(404)

```javascript
暂无数据
```

**Query**

# 注册账号接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:15:59

> 更新时间: 2025-07-23 22:13:39

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/register

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/register?apipost_id=1789d943ff002

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "name": "string",
  "email": "<EMAIL>",
  "password": "string"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | string | string | 是 | 姓名 |
| email | <EMAIL> | string | 是 | 邮箱 |
| password | string | string | 是 | 密码 |

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
{"uid":17,"name":"string","email":"<EMAIL>","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************.ofp0qQdljqQ3d9rdrAfsXCz9IhjGk6BhwWchzeG9Aw0"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| uid | 17 | number | 用户uid |
| name | string | string | 名称 |
| email | <EMAIL> | string | 邮箱 |
| token | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************.ofp0qQdljqQ3d9rdrAfsXCz9IhjGk6BhwWchzeG9Aw0 | string | token |

* 失败(404)

```javascript
暂无数据
```

**Query**

# 登录接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:16:58

> 更新时间: 2025-07-23 22:14:52

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/login

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/login?apipost_id=179a3487ff011

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "email": "<EMAIL>",
  "password": "string"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| email | <EMAIL> | string | 是 | 邮箱 |
| password | string | string | 是 | 密码 |

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
{"uid":9,"name":"string","email":"<EMAIL>","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.reIeGCggJnCIAoQYs_W1PiPqIeoyiV9N0elaA7Ps9L4"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| uid | 9 | number | - |
| name | string | string | 姓名 |
| email | <EMAIL> | string | 邮箱 |
| token | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | token |

* 失败(404)

```javascript
暂无数据
```

**Query**

# 发送找回密码验证码接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:32:16

> 更新时间: 2025-07-23 22:46:33

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/forgotpassword

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/forgotpassword?apipost_id=17d2195fff037

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "email": "<EMAIL>"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| email | <EMAIL> | string | 是 | 邮箱 |

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
{"message":"验证码已发送到您的邮箱，请查收","success":true}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| message | 验证码已发送到您的邮箱，请查收 | string | 信息 |
| success | true | boolean | - |

* 失败(404)

```javascript
{"detail":"服务器内部错误: No module named 'app.core.email_service'"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| detail | 服务器内部错误: No module named 'app.core.email_service' | string | - |

**Query**

# 重置密码接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:33:04

> 更新时间: 2025-07-23 22:14:29

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/resetpassword

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/resetpassword?apipost_id=17d5f4e7ff03a

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "email": "<EMAIL>",
  "verification_code": "string",
  "new_password": "string"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| email | <EMAIL> | string | 是 | 邮箱 |
| verification_code | string | string | 是 | 邮箱验证吗 |
| new_password | string | string | 是 | 新密码 |

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
{"detail":[{"type":"value_error","loc":["body","verification_code"],"msg":"Value error, 验证码必须是6位数字","input":"string","ctx":{"error":{}}}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| detail | - | array | - |
| detail.type | value_error | string | - |
| detail.loc | - | array | - |
| detail.msg | Value error, 验证码必须是6位数字 | string | - |
| detail.input | string | string | - |
| detail.ctx | - | object | - |
| detail.ctx.error | - | object | - |

**Query**

# 新建家庭成员接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:18:59

> 更新时间: 2025-07-23 12:36:08

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/addfamily

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/addfamily?apipost_id=17a45c2bff018

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**请求Body参数**

```javascript
{
  "relationship": "本人",
  "name": "李四",
  "gender": "男",
  "height": 180,
  "weight": 100,
  "birth_year": 1996,
  "avatar_url": "string.png",
  "uid": 9
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| relationship | string | string | 是 | 家庭关系：（本人、父亲、母亲、女儿、儿子、孙子、孙女、哥哥、弟弟、姐姐、妹妹） |
| name | string | string | 是 | 姓名 |
| gender | string | string | 是 | 性别（男，女） |
| height | 0 | number | 是 | 身高（cm） |
| weight | 0 | number | 是 | 体重（kg） |
| birth_year | 0 | number | 是 | 出生年份 |
| avatar_url | string | string | 是 | 头像地址 |
| uid | 0 | number | 是 | 用户UID |

**认证方式**

> Jwt Bearer

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和Token

> Authorization: Bearer your_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**Query**

# 家庭成员列表接口（无分页）

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:24:12

> 更新时间: 2025-07-23 12:36:14

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/users/{uid}/familylist

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/users/{uid}/familylist?apipost_id=17b29dafff024

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| uid | 9 | string | 是 | 用户UID |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**Query**

# 分析视频健康数据接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 11:34:43

> 更新时间: 2025-07-23 22:11:24

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/health/video

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/health/video?apipost_id=17dded6bff03f

**请求方式**

> POST

**Content-Type**

> form-data

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**请求Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| file | /Users/<USER>/Desktop/1.mp4 | file | 是 | 视频文件 |
| video_url | - | string | 是 | 视频文件地址（与视频文件二选一） |
| request_data | {     "uid": 9,      "fuid": 10,     "name": "张三",     "gender": "男",     "height": 175,     "weight": 70,     "birth_year": 1990 } | string | 是 | 请求参数 json格式 |

**认证方式**

> Jwt Bearer

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和Token

> Authorization: Bearer your_token

**响应示例**

* 成功(200)

```javascript
{
	"name": {
		"label": "姓名 Name",
		"value": "张三"
	},
	"gender": {
		"label": "性别 Gender",
		"value": "男"
	},
	"age": {
		"label": "年龄 Age",
		"value": 35,
		"unit": "岁 years"
	},
	"height": {
		"label": "身高 Height",
		"value": 175,
		"unit": "cm"
	},
	"weight": {
		"label": "体重 Weight",
		"value": 70,
		"unit": "kg"
	},
	"bmi": {
		"label": "体质指数 BMI",
		"value": 22.86,
		"unit": "kg/m²"
	},
	"heart_rate": {
		"label": "心率 Heart Rate",
		"value": 88.56885688568858,
		"unit": "次/分 BPM"
	},
	"pulse_rate": {
		"label": "脉搏率 Pulse Rate",
		"value": 89.15304606240713,
		"unit": "次/分 BPM"
	},
	"hrv": {
		"label": "心率变异性 HRV",
		"time_domain": {
			"label": "时域指标 Time Domain",
			"value": {
				"ibi": 673,
				"sdnn": 86.70674287077821,
				"rmssd": 62.70029892576379,
				"pnn20": 0.7894736842105263,
				"pnn50": 0.****************,
				"hr_mad": 34.33333333333428
			}
		},
		"frequency_domain": {
			"label": "频域指标 Frequency Domain",
			"value": {
				"VLF": 1.6297838045952864,
				"TP": 70.30891603525671,
				"LF": 12.78173380610794,
				"HF": 55.89739842455349,
				"LF/HF": 0.22866419844851738
			}
		},
		"nonlinear": {
			"label": "非线性指标 Nonlinear",
			"value": {
				"sd1": 44.256996602008975,
				"sd2": 47.290745279645705,
				"s": 6575.184887406526,
				"sd1/sd2": 0.9358489983674992
			}
		}
	},
	"blood_pressure": {
		"label": "血压 Blood Pressure",
		"value": {
			"SBP": 154.1,
			"DBP": 100.8
		},
		"unit": "mmHg"
	},
	"spo2": {
		"label": "血氧饱和度 SpO2",
		"value": 98,
		"unit": "%"
	},
	"breathing_rate": {
		"label": "呼吸频率 Breathing Rate",
		"value": 16,
		"unit": "次/分 rpm"
	},
	"cardiac_risk": {
		"label": "心脏风险 Cardiac Risk",
		"value": "低"
	},
	"brain_risk": {
		"label": "脑风险 Brain Risk",
		"value": "低"
	},
	"afib": {
		"label": "房颤风险 AFib Risk",
		"value": "低"
	},
	"arrhythmia": {
		"label": "心率不齐风险 Arrhythmia Risk",
		"value": "低"
	},
	"anemia": {
		"label": "是否贫血 Anemia",
		"value": "否"
	},
	"hemoglobin": {
		"label": "血红蛋白 Hemoglobin",
		"value": 145,
		"unit": "g/L"
	},
	"risk_assessment": {
		"label": "风险评估 Risk Assessment",
		"value": "低风险"
	},
	"signal_quality": {
		"label": "信号质量 Signal Quality",
		"value": 0.7008532106675304,
		"unit": "0~1"
	},
	"hrv_time_domain": {
		"label": "HRV时域 HRV Time Domain",
		"value": {
			"ibi": 673,
			"sdnn": 86.70674287077821,
			"rmssd": 62.70029892576379,
			"pnn20": 0.7894736842105263,
			"pnn50": 0.****************,
			"hr_mad": 34.33333333333428
		}
	},
	"hrv_frequency_domain": {
		"label": "HRV频域 HRV Frequency Domain",
		"value": {
			"VLF": 1.6297838045952864,
			"TP": 70.30891603525671,
			"LF": 12.78173380610794,
			"HF": 55.89739842455349,
			"LF/HF": 0.22866419844851738
		}
	},
	"hrv_nonlinear": {
		"label": "HRV非线性 HRV Nonlinear",
		"value": {
			"sd1": 44.256996602008975,
			"sd2": 47.290745279645705,
			"s": 6575.184887406526,
			"sd1/sd2": 0.9358489983674992
		}
	},
	"bvp_waveform": {
		"label": "BVP波形 BVP Waveform",
		"value": {
			"bvp": [
				1.0250569297990189
			],
			"timestamps": [],
			"sampling_rate": 30
		},
		"unit": "标准化"
	}
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| name | - | object | - |
| name.label | 姓名 Name | string | - |
| name.value | 张三 | string | - |
| gender | - | object | - |
| gender.label | 性别 Gender | string | - |
| gender.value | 男 | string | - |
| age | - | object | - |
| age.label | 年龄 Age | string | - |
| age.value | 35 | number | - |
| age.unit | 岁 years | string | - |
| height | - | object | - |
| height.label | 身高 Height | string | - |
| height.value | 175 | number | - |
| height.unit | cm | string | - |
| weight | - | object | - |
| weight.label | 体重 Weight | string | - |
| weight.value | 70 | number | - |
| weight.unit | kg | string | - |
| bmi | - | object | - |
| bmi.label | 体质指数 BMI | string | - |
| bmi.value | 22.86 | number | - |
| bmi.unit | kg/m² | string | - |
| heart_rate | - | object | - |
| heart_rate.label | 心率 Heart Rate | string | - |
| heart_rate.value | 88.56885688568858 | number | - |
| heart_rate.unit | 次/分 BPM | string | - |
| pulse_rate | - | object | - |
| pulse_rate.label | 脉搏率 Pulse Rate | string | - |
| pulse_rate.value | 89.15304606240713 | number | - |
| pulse_rate.unit | 次/分 BPM | string | - |
| hrv | - | object | - |
| hrv.label | 心率变异性 HRV | string | - |
| hrv.time_domain | - | object | - |
| hrv.time_domain.label | 时域指标 Time Domain | string | - |
| hrv.time_domain.value | - | object | - |
| hrv.time_domain.value.ibi | 673 | number | - |
| hrv.time_domain.value.sdnn | 86.70674287077821 | number | - |
| hrv.time_domain.value.rmssd | 62.70029892576379 | number | - |
| hrv.time_domain.value.pnn20 | 0.7894736842105263 | number | - |
| hrv.time_domain.value.pnn50 | 0.**************** | number | - |
| hrv.time_domain.value.hr_mad | 34.33333333333428 | number | - |
| hrv.frequency_domain | - | object | - |
| hrv.frequency_domain.label | 频域指标 Frequency Domain | string | - |
| hrv.frequency_domain.value | - | object | - |
| hrv.frequency_domain.value.VLF | 1.6297838045952864 | number | - |
| hrv.frequency_domain.value.TP | 70.30891603525671 | number | - |
| hrv.frequency_domain.value.LF | 12.78173380610794 | number | - |
| hrv.frequency_domain.value.HF | 55.89739842455349 | number | - |
| hrv.frequency_domain.value.LF/HF | 0.22866419844851738 | number | - |
| hrv.nonlinear | - | object | - |
| hrv.nonlinear.label | 非线性指标 Nonlinear | string | - |
| hrv.nonlinear.value | - | object | - |
| hrv.nonlinear.value.sd1 | 44.256996602008975 | number | - |
| hrv.nonlinear.value.sd2 | 47.290745279645705 | number | - |
| hrv.nonlinear.value.s | 6575.184887406526 | number | - |
| hrv.nonlinear.value.sd1/sd2 | 0.9358489983674992 | number | - |
| blood_pressure | - | object | - |
| blood_pressure.label | 血压 Blood Pressure | string | - |
| blood_pressure.value | - | object | - |
| blood_pressure.value.SBP | 154.1 | number | - |
| blood_pressure.value.DBP | 100.8 | number | - |
| blood_pressure.unit | mmHg | string | - |
| spo2 | - | object | - |
| spo2.label | 血氧饱和度 SpO2 | string | - |
| spo2.value | 98 | number | - |
| spo2.unit | % | string | - |
| breathing_rate | - | object | - |
| breathing_rate.label | 呼吸频率 Breathing Rate | string | - |
| breathing_rate.value | 16 | number | - |
| breathing_rate.unit | 次/分 rpm | string | - |
| cardiac_risk | - | object | - |
| cardiac_risk.label | 心脏风险 Cardiac Risk | string | - |
| cardiac_risk.value | 低 | string | - |
| brain_risk | - | object | - |
| brain_risk.label | 脑风险 Brain Risk | string | - |
| brain_risk.value | 低 | string | - |
| afib | - | object | - |
| afib.label | 房颤风险 AFib Risk | string | - |
| afib.value | 低 | string | - |
| arrhythmia | - | object | - |
| arrhythmia.label | 心率不齐风险 Arrhythmia Risk | string | - |
| arrhythmia.value | 低 | string | - |
| anemia | - | object | - |
| anemia.label | 是否贫血 Anemia | string | - |
| anemia.value | 否 | string | - |
| hemoglobin | - | object | - |
| hemoglobin.label | 血红蛋白 Hemoglobin | string | - |
| hemoglobin.value | 145 | number | - |
| hemoglobin.unit | g/L | string | - |
| risk_assessment | - | object | - |
| risk_assessment.label | 风险评估 Risk Assessment | string | - |
| risk_assessment.value | 低风险 | string | - |
| signal_quality | - | object | - |
| signal_quality.label | 信号质量 Signal Quality | string | - |
| signal_quality.value | 0.7008532106675304 | number | - |
| signal_quality.unit | 0~1 | string | - |
| hrv_time_domain | - | object | - |
| hrv_time_domain.label | HRV时域 HRV Time Domain | string | - |
| hrv_time_domain.value | - | object | - |
| hrv_time_domain.value.ibi | 673 | number | - |
| hrv_time_domain.value.sdnn | 86.70674287077821 | number | - |
| hrv_time_domain.value.rmssd | 62.70029892576379 | number | - |
| hrv_time_domain.value.pnn20 | 0.7894736842105263 | number | - |
| hrv_time_domain.value.pnn50 | 0.**************** | number | - |
| hrv_time_domain.value.hr_mad | 34.33333333333428 | number | - |
| hrv_frequency_domain | - | object | - |
| hrv_frequency_domain.label | HRV频域 HRV Frequency Domain | string | - |
| hrv_frequency_domain.value | - | object | - |
| hrv_frequency_domain.value.VLF | 1.6297838045952864 | number | - |
| hrv_frequency_domain.value.TP | 70.30891603525671 | number | - |
| hrv_frequency_domain.value.LF | 12.78173380610794 | number | - |
| hrv_frequency_domain.value.HF | 55.89739842455349 | number | - |
| hrv_frequency_domain.value.LF/HF | 0.22866419844851738 | number | - |
| hrv_nonlinear | - | object | - |
| hrv_nonlinear.label | HRV非线性 HRV Nonlinear | string | - |
| hrv_nonlinear.value | - | object | - |
| hrv_nonlinear.value.sd1 | 44.256996602008975 | number | - |
| hrv_nonlinear.value.sd2 | 47.290745279645705 | number | - |
| hrv_nonlinear.value.s | 6575.184887406526 | number | - |
| hrv_nonlinear.value.sd1/sd2 | 0.9358489983674992 | number | - |
| bvp_waveform | - | object | - |
| bvp_waveform.label | BVP波形 BVP Waveform | string | - |
| bvp_waveform.value | - | object | - |
| bvp_waveform.value.bvp | - | array | - |
| bvp_waveform.value.timestamps | - | array | - |
| bvp_waveform.value.sampling_rate | 30 | number | - |
| bvp_waveform.unit | 标准化 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | token 格式：Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

**Query**

# 获取家人信息接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 12:10:08

> 更新时间: 2025-07-23 13:48:56

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/home/<USER>/{fuid}

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/home/<USER>/{fuid}?apipost_id=185c49dfff058

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| uid | 9 | string | 是 | 用户UID |
| fuid | 10 | string | 是 | 家庭成员FUID |

**认证方式**

> Jwt Bearer

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和Token

> Authorization: Bearer your_token

**响应示例**

* 成功(200)

```javascript
{
    "family_member": {
        "fuid": 10,
        "uid": 9,
        "relationship": "本人",
        "name": "李四",
        "gender": "男",
        "height": "180.00",
        "weight": "100.00",
        "birth_year": 1996,
        "age": 29,
        "avatar_url": "string.png"
    },
    "health_report": null,
    "bvp_waveform": null,
    "metadata": {
        "uid": 9,
        "fuid": 10,
        "has_health_data": false,
        "has_bvp_data": false,
        "is_family_member": true,
        "query_time": "2025-07-23T12:11:05.651593"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| family_member | - | object | - |
| family_member.fuid | 10 | number | 用户UID |
| family_member.uid | 9 | number | 用户UID |
| family_member.relationship | 本人 | string | 家庭关系：（本人、父亲、母亲、女儿、儿子、孙子、孙女、哥哥、弟弟、姐姐、妹妹） |
| family_member.name | 李四 | string | 姓名 |
| family_member.gender | 男 | string | 性别（男，女） |
| family_member.height | 180.00 | string | 身高（cm） |
| family_member.weight | 100.00 | string | 体重（kg） |
| family_member.birth_year | 1996 | number | 出生年份 |
| family_member.age | 29 | number | 年龄 |
| family_member.avatar_url | string.png | string | 头像地址 |
| health_report | - | null | 健康数据 |
| bvp_waveform | - | null | 健康数据图形数据 |
| metadata | - | object | - |
| metadata.uid | 9 | number | 用户UID |
| metadata.fuid | 10 | number | 用户UID |
| metadata.has_health_data | false | boolean | 是否有健康数据 |
| metadata.has_bvp_data | false | boolean | 是否有健康数据图形数据 |
| metadata.is_family_member | true | boolean | 是否有fuid |
| metadata.query_time | 2025-07-23T12:11:05.651593 | string | 请求 |

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取首页数据接口

> 创建人: 涅柔斯

> 更新人: 涅柔斯

> 创建时间: 2025-07-23 12:16:16

> 更新时间: 2025-07-23 12:36:57

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /api/v1/home/<USER>

| 环境  | URL |
| --- | --- |
| 线上环境 | https://testv.orangeone.com.cn |

**Mock URL**

> /api/v1/home/<USER>

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | - |

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| uid | 9 | string | 是 | 用户uid |

**认证方式**

> Jwt Bearer

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和Token

> Authorization: Bearer your_token

**响应示例**

* 成功(200)

```javascript
{
    "family_member": {
        "fuid": 0,
        "uid": 9,
        "relationship": "",
        "name": "",
        "gender": "",
        "height": 0.0,
        "weight": 0.0,
        "birth_year": 0,
        "age": 0,
        "avatar_url": ""
    },
    "health_report": null,
    "bvp_waveform": null,
    "metadata": {
        "uid": 9,
        "fuid": null,
        "has_health_data": false,
        "has_bvp_data": false,
        "is_family_member": false,
        "query_time": "2025-07-23T12:28:15.905366"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| family_member | - | object | - |
| family_member.fuid | 0 | number | 用户fuid |
| family_member.uid | 9 | number | 用户uid |
| family_member.relationship | - | string | - |
| family_member.name | - | string | 姓名 |
| family_member.gender | - | string | 性别（男 女） |
| family_member.height | 0 | number | 身高（cm） |
| family_member.weight | 0 | number | 体重（kg） |
| family_member.birth_year | 0 | number | 出生年份 |
| family_member.age | 0 | number | 年龄 |
| family_member.avatar_url | - | string | 头像地址 |
| health_report | - | null | 健康数据 |
| bvp_waveform | - | null | 健康图表数据 |
| metadata | - | object | - |
| metadata.uid | 9 | number | 用户uid |
| metadata.fuid | - | null | 用户uid |
| metadata.has_health_data | false | boolean | 是否有健康数据 |
| metadata.has_bvp_data | false | boolean | 是否有健康图表数据 |
| metadata.is_family_member | false | boolean | 是否有fuid |
| metadata.query_time | 2025-07-23T12:28:15.905366 | string | 请求时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Authorization | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.FLx0Esnq3d42Oyxn3OGN6HJofeteD9YUfDrIz-bQ4TI | string | 是 | - |

**Query**
