from fastapi import APIRouter, File, UploadFile, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.concurrency import run_in_threadpool
import os
import uuid
import json
import datetime
import traceback
import asyncio
import psutil
import time
from typing import Optional, Union, Any
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
from app.models.health_report import HealthReport
from app.models.bvp_waveform import BvpWaveform
from app.utils.health_analyzer import HealthAnalyzer
from app.core.log_config import get_logger
import requests
from app.utils.oss_downloader import OssClient
from urllib.parse import urlparse
from pydantic import BaseModel, Field
from app.core.auth import extract_token_from_request, get_current_user

# 日志统一配置
logger = get_logger('app', 'logs/app.log')

router = APIRouter(tags=["health"])

def is_oss_url(url: str) -> bool:
    """检查是否为OSS URL"""
    if not url:
        return False
    return 'aliyuncs.com' in url.lower() or 'oss-' in url.lower()

def parse_request_data(request_data: str) -> dict:
    """解析请求数据"""
    if not request_data:
        return {}
    try:
        return json.loads(request_data)
    except:
        return {}

def safe_parse_string(value) -> Optional[str]:
    """安全解析字符串"""
    if value is None or value == "":
        return None
    return str(value).strip()

def safe_parse_float(value) -> Optional[float]:
    """安全解析浮点数"""
    if value is None or value == "":
        return None
    try:
        return float(value)
    except:
        return None

def safe_parse_int(value) -> Optional[int]:
    """安全解析整数"""
    if value is None or value == "":
        return None
    try:
        return int(value)
    except:
        return None

@router.post("/video")
async def analyze_video_health(request: Request):
    """
    分析视频健康数据 - 需要token认证
    使用multipart/form-data格式上传
    """
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    # 解析multipart表单数据
    try:
        form = await request.form()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"表单数据解析失败: {str(e)}")
    
    # 获取文件和表单字段
    file = form.get("file")
    request_data_str = form.get("request_data", "")
    
    # 解析request_data JSON字符串
    data_dict = parse_request_data(request_data_str)
    
    # 从解析后的字典中提取字段
    video_url_str: Optional[str] = safe_parse_string(data_dict.get('video_url', ''))
    name_str: Optional[str] = safe_parse_string(data_dict.get('name', ''))
    gender_str: Optional[str] = safe_parse_string(data_dict.get('gender', ''))
    height_float: Optional[float] = safe_parse_float(data_dict.get('height', ''))
    weight_float: Optional[float] = safe_parse_float(data_dict.get('weight', ''))
    birth_year_int: Optional[int] = safe_parse_int(data_dict.get('birth_year', ''))
    uid_int: Optional[int] = safe_parse_int(data_dict.get('uid', ''))
    fuid_int: Optional[int] = safe_parse_int(data_dict.get('fuid', ''))

    # 验证必填参数
    if not uid_int:
        raise HTTPException(status_code=400, detail="用户ID(uid)为必填参数")
    
    # 验证用户权限：确保当前用户只能为自己创建健康报告
    current_user_id = int(current_user.get("sub", 0))
    if uid_int != current_user_id:
        raise HTTPException(status_code=403, detail="无权为其他用户创建健康报告")

    # 校验参数：file和video_url必须二选一
    if (file is None and not video_url_str) or (file is not None and video_url_str):
        raise HTTPException(status_code=400, detail="请上传视频文件或视频地址，且只能二选一")

    allowed_ext: list = ['.mp4', '.mov']
    # 使用绝对路径确保目录创建成功
    temp_dir: str = os.path.abspath("temp_uploads")
    os.makedirs(temp_dir, exist_ok=True)
    temp_path: Optional[str] = None
    unique_filename: Optional[str] = None
    oss_object_key: Optional[str] = None
    is_oss: bool = False
    oss_client: Optional[OssClient] = None

    try:
        # 1. 处理文件上传或URL下载
        if file is not None:
            # 文件上传处理
            if not hasattr(file, 'filename') or not file.filename:
                raise HTTPException(status_code=400, detail="文件名不能为空")
            
            file_ext = os.path.splitext(file.filename)[-1].lower()
            if file_ext not in allowed_ext:
                raise HTTPException(status_code=400, detail=f"不支持的文件格式，仅支持: {', '.join(allowed_ext)}")
            
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            temp_path = os.path.join(temp_dir, unique_filename)
            
            try:
                content = await file.read()
                size_mb = len(content) / (1024 * 1024)
                if size_mb < 1 or size_mb > 100:
                    raise HTTPException(status_code=400, detail="视频大小需在1MB~100MB之间")
                
                with open(temp_path, "wb") as f:
                    f.write(content)
            except Exception as e:
                logger.error(f"文件保存失败: {e}")
                raise HTTPException(status_code=500, detail="文件保存失败")
        else:
            # URL下载处理
            if is_oss_url(video_url_str):
                # OSS下载
                is_oss = True
                try:
                    oss_client = OssClient()
                    parsed_url = urlparse(video_url_str)
                    oss_object_key = parsed_url.path.lstrip('/')
                except Exception as e:
                    logger.error(f"OSS客户端初始化失败: {e}")
                    raise HTTPException(status_code=500, detail="OSS配置错误")
                    
                unique_filename = f"{uuid.uuid4().hex}_oss_{os.path.splitext(oss_object_key)[-1].lower()}"
                temp_path = os.path.join(temp_dir, unique_filename)
                
                try:
                    oss_client.download(oss_object_key, temp_path)
                    size_mb = os.path.getsize(temp_path) / (1024 * 1024)
                    if size_mb < 1 or size_mb > 100:
                        logger.error(f"OSS视频文件大小不符: {size_mb:.2f}MB")
                        os.remove(temp_path)
                        raise HTTPException(status_code=400, detail="视频大小需在1MB~100MB之间")
                except Exception as e:
                    logger.error(f"OSS视频下载失败: {e}")
                    raise HTTPException(status_code=500, detail="OSS视频下载失败")
            else:
                # 普通http下载
                unique_filename = f"{uuid.uuid4().hex}_downloaded{os.path.splitext(video_url_str)[-1].lower()}"
                temp_path = os.path.join(temp_dir, unique_filename)
                
                try:
                    response = requests.get(video_url_str, timeout=30)
                    response.raise_for_status()
                    
                    size_mb = len(response.content) / (1024 * 1024)
                    if size_mb < 1 or size_mb > 100:
                        raise HTTPException(status_code=400, detail="视频大小需在1MB~100MB之间")
                    
                    with open(temp_path, "wb") as f:
                        f.write(response.content)
                except Exception as e:
                    logger.error(f"视频下载失败: {e}")
                    raise HTTPException(status_code=500, detail="视频下载失败")

        # 3. 调用健康分析（用线程池避免阻塞，添加超时控制）
        start_time = time.time()
        try:
            logger.info(f"开始视频健康分析，文件: {temp_path}")

            # 记录当前内存使用情况
            try:
                memory_info = psutil.Process().memory_info()
                logger.info(f"分析前内存使用: RSS={memory_info.rss / 1024 / 1024:.2f}MB, VMS={memory_info.vms / 1024 / 1024:.2f}MB")
            except:
                pass

            analyzer = HealthAnalyzer()

            # 使用 asyncio.wait_for 添加超时控制（5分钟）
            result: dict = await asyncio.wait_for(
                run_in_threadpool(
                    analyzer.analyze_video_health,
                    temp_path,
                    name_str,
                    gender_str,
                    birth_year_int,
                    height_float,
                    weight_float
                ),
                timeout=300.0  # 5分钟超时
            )

            processing_time = time.time() - start_time
            logger.info(f"视频健康分析完成，耗时: {processing_time:.2f}秒")

            # 记录分析后内存使用情况
            try:
                memory_info = psutil.Process().memory_info()
                logger.info(f"分析后内存使用: RSS={memory_info.rss / 1024 / 1024:.2f}MB, VMS={memory_info.vms / 1024 / 1024:.2f}MB")
            except:
                pass

        except asyncio.TimeoutError:
            processing_time = time.time() - start_time
            logger.error(f"视频健康分析超时，耗时: {processing_time:.2f}秒")
            raise HTTPException(status_code=408, detail="视频分析超时，请尝试使用较短的视频或稍后重试")
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"健康分析失败，耗时: {processing_time:.2f}秒，错误: {str(e)}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"健康分析失败: {str(e)}")

        # 4. 数据入库
        db = None
        try:
            logger.info("开始数据入库操作")
            db = get_db_session()

            # 生成报告ID
            report_id: str = str(uuid.uuid4())
            logger.info(f"生成报告ID: {report_id}")

            # 计算BMI和年龄
            bmi = None
            if height_float and weight_float and height_float > 0:
                bmi = round(weight_float / ((height_float/100) ** 2), 2)
                logger.info(f"计算BMI: {bmi}")

            # 计算年龄（从出生年份）
            age = None
            if birth_year_int:
                current_year = datetime.datetime.now().year
                age = current_year - birth_year_int
                logger.info(f"计算年龄: {age}")

            # 创建健康报告记录（字段名与数据库模型对齐）
            health_obj = HealthReport(
                report_id=report_id,
                uid=uid_int,
                fuid=fuid_int,
                name=name_str,
                gender=gender_str,
                age=age,  # 使用age而不是birth_year
                height=height_float,
                weight=weight_float,
                bmi=bmi,
                heart_rate=result.get('heart_rate', {}).get('value'),
                # 血压需要特殊处理，模型中是JSON字段
                blood_pressure={
                    'systolic': result.get('blood_pressure', {}).get('systolic'),
                    'diastolic': result.get('blood_pressure', {}).get('diastolic')
                } if result.get('blood_pressure') else None,
                spo2=result.get('oxygen_saturation', {}).get('value'),  # 使用spo2而不是oxygen_saturation
                # 风险评估字段映射
                cardiac_risk=result.get('heart_risk', {}).get('value'),  # 使用cardiac_risk
                brain_risk=result.get('brain_risk', {}).get('value'),
                afib=result.get('atrial_fibrillation_risk', {}).get('value'),  # 使用afib
                # 保存完整分析结果到extra字段
                extra=result,
                # 注意：使用create_time而不是created_at（数据库模型字段名）
            )

            db.add(health_obj)
            db.flush()
            logger.info("健康报告记录已添加")

            # 创建BVP波形记录
            bvp_data: dict = result.get('bvp_waveform', {}).get('value', {})
            bvp_obj = BvpWaveform(
                report_id=report_id,
                bvp=json.dumps(bvp_data.get('bvp', []), ensure_ascii=False),
                timestamps=json.dumps(bvp_data.get('timestamps', []), ensure_ascii=False),
                sampling_rate=bvp_data.get('sampling_rate')
            )
            db.add(bvp_obj)
            logger.info("BVP波形记录已添加")

            db.commit()
            logger.info("数据入库完成")

        except Exception as e:
            logger.error(f"数据入库失败: {str(e)}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            if db:
                try:
                    db.rollback()
                    logger.info("数据库事务已回滚")
                except Exception as rollback_error:
                    logger.error(f"数据库回滚失败: {rollback_error}")
            raise HTTPException(status_code=500, detail=f"数据入库失败: {str(e)}")
        finally:
            if db:
                try:
                    close_db_session(db)
                    logger.info("数据库连接已关闭")
                except Exception as close_error:
                    logger.error(f"关闭数据库连接失败: {close_error}")

        logger.info("请求处理完成，返回结果")
        return JSONResponse(content=result)

    except HTTPException:
        # 重新抛出 HTTP 异常，保持原有的错误码和消息
        raise
    except Exception as e:
        # 捕获所有其他未处理的异常
        logger.error(f"未预期的错误: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="服务器内部错误，请稍后重试")

    finally:
        # 5. 清理临时文件和OSS文件
        logger.info("开始清理资源")

        # 清理临时文件
        if temp_path:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"临时文件已删除: {temp_path}")
                else:
                    logger.info(f"临时文件不存在，无需删除: {temp_path}")
            except Exception as e:
                logger.warning(f"临时文件删除失败: {temp_path}, 错误: {e}")

        # 清理OSS文件
        if is_oss and oss_client and oss_object_key:
            try:
                oss_client.delete(oss_object_key)
                logger.info(f"OSS文件已删除: {oss_object_key}")
            except Exception as e:
                logger.warning(f"OSS文件删除失败: {oss_object_key}, 错误: {e}")

        logger.info("资源清理完成")

@router.get("/hello")
def health_check():
    """简单的健康检查端点"""
    return JSONResponse(content={"message": "Hello, World!", "status": "healthy"})

@router.get("/status")
async def detailed_health_check():
    """详细的健康检查端点，包含系统状态信息"""
    try:
        # 检查数据库连接
        db = None
        db_status = "unknown"
        try:
            db = get_db_session()
            # 简单的数据库查询测试
            db.execute("SELECT 1")
            db_status = "healthy"
        except Exception as e:
            db_status = f"error: {str(e)}"
        finally:
            if db:
                try:
                    close_db_session(db)
                except:
                    pass

        # 获取系统资源信息
        system_info = {}
        try:
            memory_info = psutil.Process().memory_info()
            system_info = {
                "memory_rss_mb": round(memory_info.rss / 1024 / 1024, 2),
                "memory_vms_mb": round(memory_info.vms / 1024 / 1024, 2),
                "cpu_percent": psutil.Process().cpu_percent(),
                "timestamp": datetime.datetime.now().isoformat()
            }
        except Exception as e:
            system_info = {"error": str(e)}

        return JSONResponse(content={
            "status": "healthy" if db_status == "healthy" else "degraded",
            "database": db_status,
            "system": system_info,
            "service": "health-detection-api",
            "version": "1.0.0"
        })

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "service": "health-detection-api"
            }
        )
