{"version": 3, "file": "ffmpeg.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,GACtB,CATD,CASGK,MAAM,I,uBCRLC,EAAsB,CAG1BA,E,GCHAA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECLDF,EAAyBQ,GAEZA,EAAU,cCHvBR,EAAoBS,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBd,EAAoBG,EAAI,CAACY,EAAKC,IAAUZ,OAAOa,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFhB,EAAoBoB,EAAKzB,IACH,oBAAX0B,QAA0BA,OAAOC,aAC1ClB,OAAOC,eAAeV,EAAS0B,OAAOC,YAAa,CAAEC,MAAO,WAE7DnB,OAAOC,eAAeV,EAAS,aAAc,CAAE4B,OAAO,GAAO,E,MCL9D,IAAIC,EACAxB,EAAoBS,EAAEgB,gBAAeD,EAAYxB,EAAoBS,EAAEiB,SAAW,IACtF,IAAIC,EAAW3B,EAAoBS,EAAEkB,SACrC,IAAKH,GAAaG,IACbA,EAASC,gBACZJ,EAAYG,EAASC,cAAcC,MAC/BL,GAAW,CACf,IAAIM,EAAUH,EAASI,qBAAqB,UAC5C,GAAGD,EAAQE,OAEV,IADA,IAAIC,EAAIH,EAAQE,OAAS,EAClBC,GAAK,IAAMT,GAAWA,EAAYM,EAAQG,KAAKJ,GAExD,CAID,IAAKL,EAAW,MAAM,IAAIU,MAAM,yDAChCV,EAAYA,EAAUW,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KACpFnC,EAAoBoC,EAAIZ,C,KClBxBxB,EAAoBqC,EAAIV,SAASW,SAAWvC,KAAK2B,SAASa,K,ICI/CC,E,iDACX,SAAWA,GACPA,EAAoB,KAAI,OACxBA,EAAoB,KAAI,OACxBA,EAAuB,QAAI,UAC3BA,EAA0B,WAAI,aAC9BA,EAAyB,UAAI,YAC7BA,EAA2B,YAAI,cAC/BA,EAAsB,OAAI,SAC1BA,EAA0B,WAAI,aAC9BA,EAAwB,SAAI,WAC5BA,EAA0B,WAAI,aAC9BA,EAAqB,MAAI,QACzBA,EAAwB,SAAI,WAC5BA,EAAwB,SAAI,WAC5BA,EAAmB,IAAI,MACvBA,EAAqB,MAAI,QACzBA,EAAuB,QAAI,SAC9B,CAjBD,CAiBGA,IAAkBA,EAAgB,CAAC,ICnB/B,MAAMC,EAAe,MACxB,IAAIC,EAAY,EAChB,MAAO,IAAMA,GAChB,EAH2B,GCFfC,GAD6B,IAAIT,MAAM,wBACpB,IAAIA,MAAM,2DAC7BU,EAAmB,IAAIV,MAAM,6BACN,IAAIA,MAAM,mCCQvC,MAAMW,EACT,GAAU,KAKV,GAAY,CAAC,EACb,GAAW,CAAC,EACZ,GAAqB,GACrB,GAA0B,GAC1BC,QAAS,EAIT,GAAoB,KACZnC,MAAK,IACLA,MAAK,EAAQoC,UAAY,EAAGC,MAAQC,KAAIC,OAAMF,YAC1C,OAAQE,GACJ,KAAKV,EAAcW,KACfxC,KAAKmC,QAAS,EACdnC,MAAK,EAAUsC,GAAID,GACnB,MACJ,KAAKR,EAAcY,MACnB,KAAKZ,EAAca,QACnB,KAAKb,EAAcc,KACnB,KAAKd,EAAce,QACnB,KAAKf,EAAcgB,WACnB,KAAKhB,EAAciB,UACnB,KAAKjB,EAAckB,YACnB,KAAKlB,EAAcmB,OACnB,KAAKnB,EAAcoB,WACnB,KAAKpB,EAAcqB,SACnB,KAAKrB,EAAcsB,WACfnD,MAAK,EAAUsC,GAAID,GACnB,MACJ,KAAKR,EAAcuB,IACfpD,MAAK,EAAmBqD,SAASC,GAAMA,EAAEjB,KACzC,MACJ,KAAKR,EAAc0B,SACfvD,MAAK,EAAwBqD,SAASC,GAAMA,EAAEjB,KAC9C,MACJ,KAAKR,EAAc2B,MACfxD,MAAK,EAASsC,GAAID,UAGnBrC,MAAK,EAAUsC,UACftC,MAAK,EAASsC,EAAG,EAEhC,EAKJ,GAAQ,EAAGC,OAAMF,QAAQoB,EAAQ,GAAIC,IAC5B1D,MAAK,EAGH,IAAI2D,SAAQ,CAACC,EAASC,KACzB,MAAMvB,EAAKR,IACX9B,MAAK,GAAWA,MAAK,EAAQ8D,YAAY,CAAExB,KAAIC,OAAMF,QAAQoB,GAC7DzD,MAAK,EAAUsC,GAAMsB,EACrB5D,MAAK,EAASsC,GAAMuB,EACpBH,GAAQK,iBAAiB,SAAS,KAC9BF,EAAO,IAAIG,aAAa,aAAa1B,gBAAkB,cAAc,GACtE,CAAE2B,MAAM,GAAO,IATXN,QAAQE,OAAO7B,GAY9B,EAAAkC,CAAGC,EAAOC,GACQ,QAAVD,EACAnE,MAAK,EAAmBqE,KAAKD,GAEd,aAAVD,GACLnE,MAAK,EAAwBqE,KAAKD,EAE1C,CACA,GAAAE,CAAIH,EAAOC,GACO,QAAVD,EACAnE,MAAK,EAAqBA,MAAK,EAAmBuE,QAAQjB,GAAMA,IAAMc,IAEvD,aAAVD,IACLnE,MAAK,EAA0BA,MAAK,EAAwBuE,QAAQjB,GAAMA,IAAMc,IAExF,CAQAI,KAAO,EAAGC,oBAAmBC,GAAW,CAAC,GAAKhB,UAAW,CAAC,KACjD1D,MAAK,IACNA,MAAK,EAAUyE,EACX,IAAIE,OAAO,IAAIC,IAAIH,EAAgB,gFAAkB,CACjDlC,KAAM,WAIV,IAAIoC,OAAO,IAAIC,IAAI,kBAAiC,CAChDrC,UAAM,IAEdvC,MAAK,KAEFA,MAAK,EAAM,CACduC,KAAMV,EAAcW,KACpBH,KAAMqC,QACPG,EAAWnB,IAsBlBoB,KAAO,CAEPC,EAMAC,GAAU,GAAMtB,UAAW,CAAC,IAAM1D,MAAK,EAAM,CACzCuC,KAAMV,EAAcc,KACpBN,KAAM,CAAE0C,OAAMC,iBACfH,EAAWnB,GAiBduB,QAAU,CAEVF,EAMAC,GAAU,GAAMtB,UAAW,CAAC,IAAM1D,MAAK,EAAM,CACzCuC,KAAMV,EAAce,QACpBP,KAAM,CAAE0C,OAAMC,iBACfH,EAAWnB,GAOdwB,UAAY,KACR,MAAMC,EAAM1F,OAAO2F,KAAKpF,MAAK,GAE7B,IAAK,MAAMsC,KAAM6C,EACbnF,MAAK,EAASsC,GAAIL,UACXjC,MAAK,EAASsC,UACdtC,MAAK,EAAUsC,GAEtBtC,MAAK,IACLA,MAAK,EAAQkF,YACblF,MAAK,EAAU,KACfA,KAAKmC,QAAS,EAClB,EAeJkD,UAAY,CAACC,EAAMjD,GAAQqB,UAAW,CAAC,KACnC,MAAMD,EAAQ,GAId,OAHIpB,aAAgBkD,YAChB9B,EAAMY,KAAKhC,EAAKmD,QAEbxF,MAAK,EAAM,CACduC,KAAMV,EAAcgB,WACpBR,KAAM,CAAEiD,OAAMjD,SACfoB,EAAOC,EAAO,EAErB+B,MAAQ,CAACC,EAAQC,EAASC,IAEf5F,MAAK,EAAM,CACduC,KAAMV,EAAcY,MACpBJ,KAAM,CAAEqD,SAAQC,UAASC,eAHf,IAMlBC,QAAWD,GAEA5F,MAAK,EAAM,CACduC,KAAMV,EAAca,QACpBL,KAAM,CAAEuD,eAHE,IAkBlBE,SAAW,CAACR,EAQZS,EAAW,UAAYrC,UAAW,CAAC,IAAM1D,MAAK,EAAM,CAChDuC,KAAMV,EAAciB,UACpBT,KAAM,CAAEiD,OAAMS,kBACflB,EAAWnB,GAMdsC,WAAa,CAACV,GAAQ5B,UAAW,CAAC,IAAM1D,MAAK,EAAM,CAC/CuC,KAAMV,EAAckB,YACpBV,KAAM,CAAEiD,cACTT,EAAWnB,GAMduC,OAAS,CAACC,EAASC,GAAWzC,UAAW,CAAC,IAAM1D,MAAK,EAAM,CACvDuC,KAAMV,EAAcmB,OACpBX,KAAM,CAAE6D,UAASC,iBAClBtB,EAAWnB,GAMd0C,UAAY,CAACd,GAAQ5B,UAAW,CAAC,IAAM1D,MAAK,EAAM,CAC9CuC,KAAMV,EAAcoB,WACpBZ,KAAM,CAAEiD,cACTT,EAAWnB,GAMd2C,QAAU,CAACf,GAAQ5B,UAAW,CAAC,IAAM1D,MAAK,EAAM,CAC5CuC,KAAMV,EAAcqB,SACpBb,KAAM,CAAEiD,cACTT,EAAWnB,GAMd4C,UAAY,CAAChB,GAAQ5B,UAAW,CAAC,IAAM1D,MAAK,EAAM,CAC9CuC,KAAMV,EAAcsB,WACpBd,KAAM,CAAEiD,cACTT,EAAWnB,GChTX,IAAI6C,E,OACX,SAAWA,GACPA,EAAgB,MAAI,QACpBA,EAAiB,OAAI,SACrBA,EAAoB,UAAI,YACxBA,EAAgB,MAAI,QACpBA,EAAmB,SAAI,WACvBA,EAAkB,QAAI,SACzB,CAPD,CAOGA,IAAaA,EAAW,CAAC,I", "sources": ["webpack://FFmpegWASM/webpack/universalModuleDefinition", "webpack://FFmpegWASM/webpack/bootstrap", "webpack://FFmpegWASM/webpack/runtime/define property getters", "webpack://FFmpegWASM/webpack/runtime/get javascript chunk filename", "webpack://FFmpegWASM/webpack/runtime/global", "webpack://FFmpegWASM/webpack/runtime/hasOwnProperty shorthand", "webpack://FFmpegWASM/webpack/runtime/make namespace object", "webpack://FFmpegWASM/webpack/runtime/publicPath", "webpack://FFmpegWASM/webpack/runtime/jsonp chunk loading", "webpack://FFmpegWASM/./dist/esm/const.js", "webpack://FFmpegWASM/./dist/esm/utils.js", "webpack://FFmpegWASM/./dist/esm/errors.js", "webpack://FFmpegWASM/./dist/esm/classes.js", "webpack://FFmpegWASM/./dist/esm/types.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"FFmpegWASM\"] = factory();\n\telse\n\t\troot[\"FFmpegWASM\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".ffmpeg.js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript)\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && !scriptUrl) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t179: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// no jsonp function", "export const MIME_TYPE_JAVASCRIPT = \"text/javascript\";\nexport const MIME_TYPE_WASM = \"application/wasm\";\nexport const CORE_VERSION = \"0.12.9\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/umd/ffmpeg-core.js`;\nexport var FFMessageType;\n(function (FFMessageType) {\n    FFMessageType[\"LOAD\"] = \"LOAD\";\n    FFMessageType[\"EXEC\"] = \"EXEC\";\n    FFMessageType[\"FFPROBE\"] = \"FFPROBE\";\n    FFMessageType[\"WRITE_FILE\"] = \"WRITE_FILE\";\n    FFMessageType[\"READ_FILE\"] = \"READ_FILE\";\n    FFMessageType[\"DELETE_FILE\"] = \"DELETE_FILE\";\n    FFMessageType[\"RENAME\"] = \"RENAME\";\n    FFMessageType[\"CREATE_DIR\"] = \"CREATE_DIR\";\n    FFMessageType[\"LIST_DIR\"] = \"LIST_DIR\";\n    FFMessageType[\"DELETE_DIR\"] = \"DELETE_DIR\";\n    FFMessageType[\"ERROR\"] = \"ERROR\";\n    FFMessageType[\"DOWNLOAD\"] = \"DOWNLOAD\";\n    FFMessageType[\"PROGRESS\"] = \"PROGRESS\";\n    FFMessageType[\"LOG\"] = \"LOG\";\n    FFMessageType[\"MOUNT\"] = \"MOUNT\";\n    FFMessageType[\"UNMOUNT\"] = \"UNMOUNT\";\n})(FFMessageType || (FFMessageType = {}));\n", "/**\n * Generate an unique message ID.\n */\nexport const getMessageID = (() => {\n    let messageID = 0;\n    return () => messageID++;\n})();\n", "export const ERROR_UNKNOWN_MESSAGE_TYPE = new Error(\"unknown message type\");\nexport const ERROR_NOT_LOADED = new Error(\"ffmpeg is not loaded, call `await ffmpeg.load()` first\");\nexport const ERROR_TERMINATED = new Error(\"called FFmpeg.terminate()\");\nexport const ERROR_IMPORT_FAILURE = new Error(\"failed to import ffmpeg-core.js\");\n", "import { FFMessageType } from \"./const.js\";\nimport { getMessageID } from \"./utils.js\";\nimport { ERROR_TERMINATED, ERROR_NOT_LOADED } from \"./errors.js\";\n/**\n * Provides APIs to interact with ffmpeg web worker.\n *\n * @example\n * ```ts\n * const ffmpeg = new FFmpeg();\n * ```\n */\nexport class FFmpeg {\n    #worker = null;\n    /**\n     * #resolves and #rejects tracks Promise resolves and rejects to\n     * be called when we receive message from web worker.\n     */\n    #resolves = {};\n    #rejects = {};\n    #logEventCallbacks = [];\n    #progressEventCallbacks = [];\n    loaded = false;\n    /**\n     * register worker message event handlers.\n     */\n    #registerHandlers = () => {\n        if (this.#worker) {\n            this.#worker.onmessage = ({ data: { id, type, data }, }) => {\n                switch (type) {\n                    case FFMessageType.LOAD:\n                        this.loaded = true;\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.MOUNT:\n                    case FFMessageType.UNMOUNT:\n                    case FFMessageType.EXEC:\n                    case FFMessageType.FFPROBE:\n                    case FFMessageType.WRITE_FILE:\n                    case FFMessageType.READ_FILE:\n                    case FFMessageType.DELETE_FILE:\n                    case FFMessageType.RENAME:\n                    case FFMessageType.CREATE_DIR:\n                    case FFMessageType.LIST_DIR:\n                    case FFMessageType.DELETE_DIR:\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.LOG:\n                        this.#logEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.PROGRESS:\n                        this.#progressEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.ERROR:\n                        this.#rejects[id](data);\n                        break;\n                }\n                delete this.#resolves[id];\n                delete this.#rejects[id];\n            };\n        }\n    };\n    /**\n     * Generic function to send messages to web worker.\n     */\n    #send = ({ type, data }, trans = [], signal) => {\n        if (!this.#worker) {\n            return Promise.reject(ERROR_NOT_LOADED);\n        }\n        return new Promise((resolve, reject) => {\n            const id = getMessageID();\n            this.#worker && this.#worker.postMessage({ id, type, data }, trans);\n            this.#resolves[id] = resolve;\n            this.#rejects[id] = reject;\n            signal?.addEventListener(\"abort\", () => {\n                reject(new DOMException(`Message # ${id} was aborted`, \"AbortError\"));\n            }, { once: true });\n        });\n    };\n    on(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks.push(callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks.push(callback);\n        }\n    }\n    off(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks = this.#logEventCallbacks.filter((f) => f !== callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks = this.#progressEventCallbacks.filter((f) => f !== callback);\n        }\n    }\n    /**\n     * Loads ffmpeg-core inside web worker. It is required to call this method first\n     * as it initializes WebAssembly and other essential variables.\n     *\n     * @category FFmpeg\n     * @returns `true` if ffmpeg core is loaded for the first time.\n     */\n    load = ({ classWorkerURL, ...config } = {}, { signal } = {}) => {\n        if (!this.#worker) {\n            this.#worker = classWorkerURL ?\n                new Worker(new URL(classWorkerURL, import.meta.url), {\n                    type: \"module\",\n                }) :\n                // We need to duplicated the code here to enable webpack\n                // to bundle worekr.js here.\n                new Worker(new URL(\"./worker.js\", import.meta.url), {\n                    type: \"module\",\n                });\n            this.#registerHandlers();\n        }\n        return this.#send({\n            type: FFMessageType.LOAD,\n            data: config,\n        }, undefined, signal);\n    };\n    /**\n     * Execute ffmpeg command.\n     *\n     * @remarks\n     * To avoid common I/O issues, [\"-nostdin\", \"-y\"] are prepended to the args\n     * by default.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", ...);\n     * // ffmpeg -i video.avi video.mp4\n     * await ffmpeg.exec([\"-i\", \"video.avi\", \"video.mp4\"]);\n     * const data = ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @returns `0` if no error, `!= 0` if timeout (1) or error.\n     * @category FFmpeg\n     */\n    exec = (\n    /** ffmpeg command line args */\n    args, \n    /**\n     * milliseconds to wait before stopping the command execution.\n     *\n     * @defaultValue -1\n     */\n    timeout = -1, { signal } = {}) => this.#send({\n        type: FFMessageType.EXEC,\n        data: { args, timeout },\n    }, undefined, signal);\n    /**\n     * Execute ffprobe command.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", ...);\n     * // Getting duration of a video in seconds: ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 video.avi -o output.txt\n     * await ffmpeg.ffprobe([\"-v\", \"error\", \"-show_entries\", \"format=duration\", \"-of\", \"default=noprint_wrappers=1:nokey=1\", \"video.avi\", \"-o\", \"output.txt\"]);\n     * const data = ffmpeg.readFile(\"output.txt\");\n     * ```\n     *\n     * @returns `0` if no error, `!= 0` if timeout (1) or error.\n     * @category FFmpeg\n     */\n    ffprobe = (\n    /** ffprobe command line args */\n    args, \n    /**\n     * milliseconds to wait before stopping the command execution.\n     *\n     * @defaultValue -1\n     */\n    timeout = -1, { signal } = {}) => this.#send({\n        type: FFMessageType.FFPROBE,\n        data: { args, timeout },\n    }, undefined, signal);\n    /**\n     * Terminate all ongoing API calls and terminate web worker.\n     * `FFmpeg.load()` must be called again before calling any other APIs.\n     *\n     * @category FFmpeg\n     */\n    terminate = () => {\n        const ids = Object.keys(this.#rejects);\n        // rejects all incomplete Promises.\n        for (const id of ids) {\n            this.#rejects[id](ERROR_TERMINATED);\n            delete this.#rejects[id];\n            delete this.#resolves[id];\n        }\n        if (this.#worker) {\n            this.#worker.terminate();\n            this.#worker = null;\n            this.loaded = false;\n        }\n    };\n    /**\n     * Write data to ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", await fetchFile(\"../video.avi\"));\n     * await ffmpeg.writeFile(\"text.txt\", \"hello world\");\n     * ```\n     *\n     * @category File System\n     */\n    writeFile = (path, data, { signal } = {}) => {\n        const trans = [];\n        if (data instanceof Uint8Array) {\n            trans.push(data.buffer);\n        }\n        return this.#send({\n            type: FFMessageType.WRITE_FILE,\n            data: { path, data },\n        }, trans, signal);\n    };\n    mount = (fsType, options, mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.MOUNT,\n            data: { fsType, options, mountPoint },\n        }, trans);\n    };\n    unmount = (mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.UNMOUNT,\n            data: { mountPoint },\n        }, trans);\n    };\n    /**\n     * Read data from ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * const data = await ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @category File System\n     */\n    readFile = (path, \n    /**\n     * File content encoding, supports two encodings:\n     * - utf8: read file as text file, return data in string type.\n     * - binary: read file as binary file, return data in Uint8Array type.\n     *\n     * @defaultValue binary\n     */\n    encoding = \"binary\", { signal } = {}) => this.#send({\n        type: FFMessageType.READ_FILE,\n        data: { path, encoding },\n    }, undefined, signal);\n    /**\n     * Delete a file.\n     *\n     * @category File System\n     */\n    deleteFile = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_FILE,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Rename a file or directory.\n     *\n     * @category File System\n     */\n    rename = (oldPath, newPath, { signal } = {}) => this.#send({\n        type: FFMessageType.RENAME,\n        data: { oldPath, newPath },\n    }, undefined, signal);\n    /**\n     * Create a directory.\n     *\n     * @category File System\n     */\n    createDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.CREATE_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * List directory contents.\n     *\n     * @category File System\n     */\n    listDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.LIST_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Delete an empty directory.\n     *\n     * @category File System\n     */\n    deleteDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_DIR,\n        data: { path },\n    }, undefined, signal);\n}\n", "export var FFFSType;\n(function (FFFSType) {\n    FFFSType[\"MEMFS\"] = \"MEMFS\";\n    FFFSType[\"NODEFS\"] = \"NODEFS\";\n    FFFSType[\"NODERAWFS\"] = \"NODERAWFS\";\n    FFFSType[\"IDBFS\"] = \"IDBFS\";\n    FFFSType[\"WORKERFS\"] = \"WORKERFS\";\n    FFFSType[\"PROXYFS\"] = \"PROXYFS\";\n})(FFFSType || (FFFSType = {}));\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "chunkId", "g", "globalThis", "this", "Function", "e", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "scriptUrl", "importScripts", "location", "document", "currentScript", "src", "scripts", "getElementsByTagName", "length", "i", "Error", "replace", "p", "b", "baseURI", "href", "FFMessageType", "getMessageID", "messageID", "ERROR_NOT_LOADED", "ERROR_TERMINATED", "FFmpeg", "loaded", "onmessage", "data", "id", "type", "LOAD", "MOUNT", "UNMOUNT", "EXEC", "FFPROBE", "WRITE_FILE", "READ_FILE", "DELETE_FILE", "RENAME", "CREATE_DIR", "LIST_DIR", "DELETE_DIR", "LOG", "for<PERSON>ach", "f", "PROGRESS", "ERROR", "trans", "signal", "Promise", "resolve", "reject", "postMessage", "addEventListener", "DOMException", "once", "on", "event", "callback", "push", "off", "filter", "load", "classWorkerURL", "config", "Worker", "URL", "undefined", "exec", "args", "timeout", "ffprobe", "terminate", "ids", "keys", "writeFile", "path", "Uint8Array", "buffer", "mount", "fsType", "options", "mountPoint", "unmount", "readFile", "encoding", "deleteFile", "rename", "old<PERSON><PERSON>", "newPath", "createDir", "listDir", "deleteDir", "FFFSType"], "sourceRoot": ""}