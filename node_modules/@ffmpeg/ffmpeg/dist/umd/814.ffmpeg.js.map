{"version": 3, "file": "814.ffmpeg.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,GACtB,CATD,CASGK,MAAM,I,qBCTT,SAASC,EAAyBC,GAGjC,OAAOC,QAAQC,UAAUC,MAAK,KAC7B,IAAIC,EAAI,IAAIC,MAAM,uBAAyBL,EAAM,KAEjD,MADAI,EAAEE,KAAO,mBACHF,CAAC,GAET,CACAL,EAAyBQ,KAAO,IAAM,GACtCR,EAAyBG,QAAUH,EACnCA,EAAyBS,GAAK,IAC9Bb,EAAOD,QAAUK,C,GCXbU,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAalB,QAGrB,IAAIC,EAASc,EAAyBE,GAAY,CAGjDjB,QAAS,CAAC,GAOX,OAHAoB,EAAoBH,GAAUhB,EAAQA,EAAOD,QAASgB,GAG/Cf,EAAOD,OACf,C,OCtBAgB,EAAoBK,EAAI,CAACC,EAAKC,IAAUC,OAAOC,UAAUC,eAAeC,KAAKL,EAAKC,G,mBCA3E,MAGMK,EAAW,gEACjB,IAAIC,GACX,SAAWA,GACPA,EAAoB,KAAI,OACxBA,EAAoB,KAAI,OACxBA,EAAuB,QAAI,UAC3BA,EAA0B,WAAI,aAC9BA,EAAyB,UAAI,YAC7BA,EAA2B,YAAI,cAC/BA,EAAsB,OAAI,SAC1BA,EAA0B,WAAI,aAC9BA,EAAwB,SAAI,WAC5BA,EAA0B,WAAI,aAC9BA,EAAqB,MAAI,QACzBA,EAAwB,SAAI,WAC5BA,EAAwB,SAAI,WAC5BA,EAAmB,IAAI,MACvBA,EAAqB,MAAI,QACzBA,EAAuB,QAAI,SAC9B,CAjBD,CAiBGA,IAAkBA,EAAgB,CAAC,ICtB/B,MAAMC,EAA6B,IAAInB,MAAM,wBACvCoB,EAAmB,IAAIpB,MAAM,0DAE7BqB,GADmB,IAAIrB,MAAM,6BACN,IAAIA,MAAM,oCCE9C,IAAIsB,EAgGJ7B,KAAK8B,UAAYC,OAASC,MAAQtB,KAAIuB,OAAMD,KAAME,OAC9C,MAAMC,EAAQ,GACd,IAAIH,EACJ,IACI,GAAIC,IAASR,EAAcW,OAASP,EAChC,MAAMF,EACV,OAAQM,GACJ,KAAKR,EAAcW,KACfJ,OAvGHD,QAASM,QAASC,EAAUC,QAASC,EAAUC,UAAWC,MACnE,MAAMC,GAASd,EACf,IACSS,IACDA,EAAWd,GAEfoB,cAAcN,EAClB,CACA,MAMI,GALKA,GAAYA,IAAad,IAC1Bc,EAAWd,EAASqB,QAAQ,QAAS,UAEzC7C,KAAK8C,wBAA0B,OACZR,IAAWS,SACzB/C,KAAK8C,iBACN,MAAMlB,CAEd,CACA,MAAMS,EAAUC,EACVC,EAAUC,GAAsBF,EAASO,QAAQ,QAAS,SAC1DJ,EAAYC,GAEZJ,EAASO,QAAQ,QAAS,cAWhC,OAVAhB,QAAe7B,KAAK8C,iBAAiB,CAGjCE,oBAAqB,GAAGX,KAAWY,KAAKC,KAAKC,UAAU,CAAEZ,UAASE,mBAEtEZ,EAAOuB,WAAWpB,GAAShC,KAAKqD,YAAY,CAAEpB,KAAMR,EAAc6B,IAAKtB,WACvEH,EAAO0B,aAAavB,GAAShC,KAAKqD,YAAY,CAC1CpB,KAAMR,EAAc+B,SACpBxB,WAEGW,CAAK,EAsEac,CAAKvB,GAClB,MACJ,KAAKT,EAAciC,KACf1B,EAvEH,GAAG2B,OAAMC,WAAU,MAC5B/B,EAAOgC,WAAWD,GAClB/B,EAAOiC,QAAQH,GACf,MAAMI,EAAMlC,EAAOkC,IAEnB,OADAlC,EAAOmC,QACAD,CAAG,EAkESD,CAAK5B,GACZ,MACJ,KAAKT,EAAcwC,QACfjC,EAnEA,GAAG2B,OAAMC,WAAU,MAC/B/B,EAAOgC,WAAWD,GAClB/B,EAAOqC,WAAWP,GAClB,MAAMI,EAAMlC,EAAOkC,IAEnB,OADAlC,EAAOmC,QACAD,CAAG,EA8DSG,CAAQhC,GACf,MACJ,KAAKT,EAAc0C,WACfnC,EA/DE,GAAGoC,OAAMpC,WACvBH,EAAOwC,GAAGC,UAAUF,EAAMpC,IACnB,GA6DYsC,CAAUpC,GACjB,MACJ,KAAKT,EAAc8C,UACfvC,EA9DC,GAAGoC,OAAMI,cAAe3C,EAAOwC,GAAGI,SAASL,EAAM,CAAEI,aA8D7CC,CAASvC,GAChB,MACJ,KAAKT,EAAciD,YACf1C,EA/DG,GAAGoC,WAClBvC,EAAOwC,GAAGM,OAAOP,IACV,GA6DYQ,CAAW1C,GAClB,MACJ,KAAKT,EAAcoD,OACf7C,EA9DD,GAAG8C,UAASC,cACvBlD,EAAOwC,GAAGW,OAAOF,EAASC,IACnB,GA4DYC,CAAO9C,GACd,MACJ,KAAKT,EAAcwD,WACfjD,EA5DE,GAAGoC,WACjBvC,EAAOwC,GAAGa,MAAMd,IACT,GA0DYe,CAAUjD,GACjB,MACJ,KAAKT,EAAc2D,SACfpD,EA3DA,GAAGoC,WACf,MAAMiB,EAAQxD,EAAOwC,GAAGiB,QAAQlB,GAC1BmB,EAAQ,GACd,IAAK,MAAMC,KAAQH,EAAO,CACtB,MAAMI,EAAO5D,EAAOwC,GAAGoB,KAAK,GAAGrB,KAAQoB,KACjCE,EAAQ7D,EAAOwC,GAAGqB,MAAMD,EAAKE,MACnCJ,EAAMK,KAAK,CAAEJ,OAAME,SACvB,CACA,OAAOH,CAAK,EAmDOM,CAAQ3D,GACf,MACJ,KAAKT,EAAcqE,WACf9D,EAnDE,GAAGoC,WACjBvC,EAAOwC,GAAG0B,MAAM3B,IACT,GAiDY4B,CAAU9D,GACjB,MACJ,KAAKT,EAAcwE,MACfjE,EAlDF,GAAGkE,SAAQC,UAASC,iBAC9B,MAAMC,EAAMH,EACNI,EAAKzE,EAAOwC,GAAGkC,YAAYF,GACjC,QAAKC,IAELzE,EAAOwC,GAAGmC,MAAMF,EAAIH,EAASC,IACtB,EAAI,EA4CQI,CAAMtE,GACb,MACJ,KAAKT,EAAcgF,QACfzE,EA7CA,GAAGoE,iBACfvE,EAAOwC,GAAGqC,QAAQN,IACX,GA2CYM,CAAQxE,GACf,MACJ,QACI,MAAMR,EAElB,CACA,MAAOpB,GAMH,YALAN,KAAKqD,YAAY,CACb3C,KACAuB,KAAMR,EAAckF,MACpB3E,KAAM1B,EAAEsG,YAGhB,CACI5E,aAAgB6E,YAChB1E,EAAMyD,KAAK5D,EAAK8E,QAEpB9G,KAAKqD,YAAY,CAAE3C,KAAIuB,OAAMD,QAAQG,EAAM,C", "sources": ["webpack://FFmpegWASM/webpack/universalModuleDefinition", "webpack://FFmpegWASM/./dist/esm/ lazy namespace object", "webpack://FFmpegWASM/webpack/bootstrap", "webpack://FFmpegWASM/webpack/runtime/hasOwnProperty shorthand", "webpack://FFmpegWASM/./dist/esm/const.js", "webpack://FFmpegWASM/./dist/esm/errors.js", "webpack://FFmpegWASM/./dist/esm/worker.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"FFmpegWASM\"] = factory();\n\telse\n\t\troot[\"FFmpegWASM\"] = factory();\n})(self, () => {\nreturn ", "function webpackEmptyAsyncContext(req) {\n\t// Here Promise.resolve().then() is used instead of new Promise() to prevent\n\t// uncaught exception popping up in devtools\n\treturn Promise.resolve().then(() => {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t});\n}\nwebpackEmptyAsyncContext.keys = () => ([]);\nwebpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;\nwebpackEmptyAsyncContext.id = 454;\nmodule.exports = webpackEmptyAsyncContext;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "export const MIME_TYPE_JAVASCRIPT = \"text/javascript\";\nexport const MIME_TYPE_WASM = \"application/wasm\";\nexport const CORE_VERSION = \"0.12.9\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/umd/ffmpeg-core.js`;\nexport var FFMessageType;\n(function (FFMessageType) {\n    FFMessageType[\"LOAD\"] = \"LOAD\";\n    FFMessageType[\"EXEC\"] = \"EXEC\";\n    FFMessageType[\"FFPROBE\"] = \"FFPROBE\";\n    FFMessageType[\"WRITE_FILE\"] = \"WRITE_FILE\";\n    FFMessageType[\"READ_FILE\"] = \"READ_FILE\";\n    FFMessageType[\"DELETE_FILE\"] = \"DELETE_FILE\";\n    FFMessageType[\"RENAME\"] = \"RENAME\";\n    FFMessageType[\"CREATE_DIR\"] = \"CREATE_DIR\";\n    FFMessageType[\"LIST_DIR\"] = \"LIST_DIR\";\n    FFMessageType[\"DELETE_DIR\"] = \"DELETE_DIR\";\n    FFMessageType[\"ERROR\"] = \"ERROR\";\n    FFMessageType[\"DOWNLOAD\"] = \"DOWNLOAD\";\n    FFMessageType[\"PROGRESS\"] = \"PROGRESS\";\n    FFMessageType[\"LOG\"] = \"LOG\";\n    FFMessageType[\"MOUNT\"] = \"MOUNT\";\n    FFMessageType[\"UNMOUNT\"] = \"UNMOUNT\";\n})(FFMessageType || (FFMessageType = {}));\n", "export const ERROR_UNKNOWN_MESSAGE_TYPE = new Error(\"unknown message type\");\nexport const ERROR_NOT_LOADED = new Error(\"ffmpeg is not loaded, call `await ffmpeg.load()` first\");\nexport const ERROR_TERMINATED = new Error(\"called FFmpeg.terminate()\");\nexport const ERROR_IMPORT_FAILURE = new Error(\"failed to import ffmpeg-core.js\");\n", "/// <reference no-default-lib=\"true\" />\n/// <reference lib=\"esnext\" />\n/// <reference lib=\"webworker\" />\nimport { CORE_URL, FFMessageType } from \"./const.js\";\nimport { ERROR_UNKNOWN_MESSAGE_TYPE, ERROR_NOT_LOADED, ERROR_IMPORT_FAILURE, } from \"./errors.js\";\nlet ffmpeg;\nconst load = async ({ coreURL: _coreURL, wasmURL: _wasmURL, workerURL: _workerURL, }) => {\n    const first = !ffmpeg;\n    try {\n        if (!_coreURL)\n            _coreURL = CORE_URL;\n        // when web worker type is `classic`.\n        importScripts(_coreURL);\n    }\n    catch {\n        if (!_coreURL || _coreURL === CORE_URL)\n            _coreURL = CORE_URL.replace('/umd/', '/esm/');\n        // when web worker type is `module`.\n        self.createFFmpegCore = (await import(\n        /* @vite-ignore */ _coreURL)).default;\n        if (!self.createFFmpegCore) {\n            throw ERROR_IMPORT_FAILURE;\n        }\n    }\n    const coreURL = _coreURL;\n    const wasmURL = _wasmURL ? _wasmURL : _coreURL.replace(/.js$/g, \".wasm\");\n    const workerURL = _workerURL\n        ? _workerURL\n        : _coreURL.replace(/.js$/g, \".worker.js\");\n    ffmpeg = await self.createFFmpegCore({\n        // Fix `Overload resolution failed.` when using multi-threaded ffmpeg-core.\n        // Encoded wasmURL and workerURL in the URL as a hack to fix locateFile issue.\n        mainScriptUrlOrBlob: `${coreURL}#${btoa(JSON.stringify({ wasmURL, workerURL }))}`,\n    });\n    ffmpeg.setLogger((data) => self.postMessage({ type: FFMessageType.LOG, data }));\n    ffmpeg.setProgress((data) => self.postMessage({\n        type: FFMessageType.PROGRESS,\n        data,\n    }));\n    return first;\n};\nconst exec = ({ args, timeout = -1 }) => {\n    ffmpeg.setTimeout(timeout);\n    ffmpeg.exec(...args);\n    const ret = ffmpeg.ret;\n    ffmpeg.reset();\n    return ret;\n};\nconst ffprobe = ({ args, timeout = -1 }) => {\n    ffmpeg.setTimeout(timeout);\n    ffmpeg.ffprobe(...args);\n    const ret = ffmpeg.ret;\n    ffmpeg.reset();\n    return ret;\n};\nconst writeFile = ({ path, data }) => {\n    ffmpeg.FS.writeFile(path, data);\n    return true;\n};\nconst readFile = ({ path, encoding }) => ffmpeg.FS.readFile(path, { encoding });\n// TODO: check if deletion works.\nconst deleteFile = ({ path }) => {\n    ffmpeg.FS.unlink(path);\n    return true;\n};\nconst rename = ({ oldPath, newPath }) => {\n    ffmpeg.FS.rename(oldPath, newPath);\n    return true;\n};\n// TODO: check if creation works.\nconst createDir = ({ path }) => {\n    ffmpeg.FS.mkdir(path);\n    return true;\n};\nconst listDir = ({ path }) => {\n    const names = ffmpeg.FS.readdir(path);\n    const nodes = [];\n    for (const name of names) {\n        const stat = ffmpeg.FS.stat(`${path}/${name}`);\n        const isDir = ffmpeg.FS.isDir(stat.mode);\n        nodes.push({ name, isDir });\n    }\n    return nodes;\n};\n// TODO: check if deletion works.\nconst deleteDir = ({ path }) => {\n    ffmpeg.FS.rmdir(path);\n    return true;\n};\nconst mount = ({ fsType, options, mountPoint }) => {\n    const str = fsType;\n    const fs = ffmpeg.FS.filesystems[str];\n    if (!fs)\n        return false;\n    ffmpeg.FS.mount(fs, options, mountPoint);\n    return true;\n};\nconst unmount = ({ mountPoint }) => {\n    ffmpeg.FS.unmount(mountPoint);\n    return true;\n};\nself.onmessage = async ({ data: { id, type, data: _data }, }) => {\n    const trans = [];\n    let data;\n    try {\n        if (type !== FFMessageType.LOAD && !ffmpeg)\n            throw ERROR_NOT_LOADED; // eslint-disable-line\n        switch (type) {\n            case FFMessageType.LOAD:\n                data = await load(_data);\n                break;\n            case FFMessageType.EXEC:\n                data = exec(_data);\n                break;\n            case FFMessageType.FFPROBE:\n                data = ffprobe(_data);\n                break;\n            case FFMessageType.WRITE_FILE:\n                data = writeFile(_data);\n                break;\n            case FFMessageType.READ_FILE:\n                data = readFile(_data);\n                break;\n            case FFMessageType.DELETE_FILE:\n                data = deleteFile(_data);\n                break;\n            case FFMessageType.RENAME:\n                data = rename(_data);\n                break;\n            case FFMessageType.CREATE_DIR:\n                data = createDir(_data);\n                break;\n            case FFMessageType.LIST_DIR:\n                data = listDir(_data);\n                break;\n            case FFMessageType.DELETE_DIR:\n                data = deleteDir(_data);\n                break;\n            case FFMessageType.MOUNT:\n                data = mount(_data);\n                break;\n            case FFMessageType.UNMOUNT:\n                data = unmount(_data);\n                break;\n            default:\n                throw ERROR_UNKNOWN_MESSAGE_TYPE;\n        }\n    }\n    catch (e) {\n        self.postMessage({\n            id,\n            type: FFMessageType.ERROR,\n            data: e.toString(),\n        });\n        return;\n    }\n    if (data instanceof Uint8Array) {\n        trans.push(data.buffer);\n    }\n    self.postMessage({ id, type, data }, trans);\n};\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "webpackEmptyAsyncContext", "req", "Promise", "resolve", "then", "e", "Error", "code", "keys", "id", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "o", "obj", "prop", "Object", "prototype", "hasOwnProperty", "call", "CORE_URL", "FFMessageType", "ERROR_UNKNOWN_MESSAGE_TYPE", "ERROR_NOT_LOADED", "ERROR_IMPORT_FAILURE", "ffmpeg", "onmessage", "async", "data", "type", "_data", "trans", "LOAD", "coreURL", "_coreURL", "wasmURL", "_wasmURL", "workerURL", "_workerURL", "first", "importScripts", "replace", "createFFmpegCore", "default", "mainScriptUrlOrBlob", "btoa", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "postMessage", "LOG", "setProgress", "PROGRESS", "load", "EXEC", "args", "timeout", "setTimeout", "exec", "ret", "reset", "FFPROBE", "ffprobe", "WRITE_FILE", "path", "FS", "writeFile", "READ_FILE", "encoding", "readFile", "DELETE_FILE", "unlink", "deleteFile", "RENAME", "old<PERSON><PERSON>", "newPath", "rename", "CREATE_DIR", "mkdir", "createDir", "LIST_DIR", "names", "readdir", "nodes", "name", "stat", "isDir", "mode", "push", "listDir", "DELETE_DIR", "rmdir", "deleteDir", "MOUNT", "fsType", "options", "mountPoint", "str", "fs", "filesystems", "mount", "UNMOUNT", "unmount", "ERROR", "toString", "Uint8Array", "buffer"], "sourceRoot": ""}