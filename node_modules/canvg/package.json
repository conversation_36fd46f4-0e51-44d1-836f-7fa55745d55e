{"name": "canvg", "version": "3.0.11", "description": "JavaScript SVG parser and renderer on Canvas.", "authors": [{"name": "<PERSON>", "email": "gabe<PERSON><EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "url": "https://twitter.com/dangreen58"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/canvg/canvg"}, "bugs": {"url": "https://github.com/canvg/canvg/issues"}, "type": "module", "sideEffects": false, "main": "lib/index.cjs", "module": "lib/index.es.js", "raw": "lib/index.babel.js", "umd": "lib/umd.js", "types": "lib/index.d.ts", "exports": {"require": "./lib/index.cjs", "import": "./lib/index.es.js"}, "engines": {"node": ">=10.0.0"}, "keywords": ["javascript", "client", "browser", "svg", "canvas"], "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "files": ["lib"]}