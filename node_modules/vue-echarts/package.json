{"name": "vue-echarts", "version": "7.0.3", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": "https://github.com/ecomfe/vue-echarts.git", "author": "GU Yiling <<EMAIL>>", "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "files": ["dist", "scripts/postinstall.js"], "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-import-css": "^3.5.1", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}}