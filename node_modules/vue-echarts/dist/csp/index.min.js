var VueDemi=function(n,t,A){if(n.install)return n;if(!t)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),n;if(t.version.slice(0,4)==="2.7."){let a=function(f,v){var u,P={},b={config:t.config,use:t.use.bind(t),mixin:t.mixin.bind(t),component:t.component.bind(t),provide:function(d,O){return P[d]=O,this},directive:function(d,O){return O?(t.directive(d,O),b):t.directive(d)},mount:function(d,O){return u||(u=new t(Object.assign({propsData:v},f,{provide:Object.assign(P,f.provide)})),u.$mount(d,O),u)},unmount:function(){u&&(u.$destroy(),u=void 0)}};return b};var H=a;for(var p in t)n[p]=t[p];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=t,n.Vue2=t,n.version=t.version,n.warn=t.util.warn,n.createApp=a}else if(t.version.slice(0,2)==="2.")if(A){for(var p in A)n[p]=A[p];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=t,n.Vue2=t,n.version=t.version}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(t.version.slice(0,2)==="3."){for(var p in t)n[p]=t[p];n.isVue2=!1,n.isVue3=!0,n.install=function(){},n.Vue=t,n.Vue2=void 0,n.version=t.version,n.set=function(a,f,v){return Array.isArray(a)?(a.length=Math.max(a.length,f),a.splice(f,1,v),v):(a[f]=v,v)},n.del=function(a,f){if(Array.isArray(a)){a.splice(f,1);return}delete a[f]}}else console.error("[vue-demi] Vue version "+t.version+" is unsupported.");return n}(this.VueDemi=this.VueDemi||(typeof VueDemi<"u"?VueDemi:{}),this.Vue||(typeof Vue<"u"?Vue:void 0),this.VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(n,t){typeof exports=="object"&&typeof module<"u"?module.exports=t(require("echarts"),require("vue-demi"),require("echarts/core")):typeof define=="function"&&define.amd?define(["echarts","vue-demi","echarts/core"],t):(n=typeof globalThis<"u"?globalThis:n||self,n.VueECharts=t(n.echarts,n.VueDemi,n.echarts))})(this,function(n,t,A){"use strict";const p=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function H(e){function c(o){return(...l)=>{if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[o].apply(e.value,l)}}function r(){const o=Object.create(null);return p.forEach(l=>{o[l]=c(l)}),o}return r()}function a(e,c,r){t.watch([r,e,c],([o,l,h],Z,U)=>{let g=null;if(o&&l&&h){const{offsetWidth:C,offsetHeight:K}=o,S=h===!0?{}:h,{throttle:T=100,onResize:z}=S;let j=!1;const w=()=>{l.resize(),z?.()},Y=T?A.throttle(w,T):w;g=new ResizeObserver(()=>{!j&&(j=!0,o.offsetWidth===C&&o.offsetHeight===K)||Y()}),g.observe(o)}U(()=>{g&&(g.disconnect(),g=null)})})}const f={autoresize:[Boolean,Object]},v=/^on[^a-z]/,u=e=>v.test(e);function P(e){const c={};for(const r in e)u(r)||(c[r]=e[r]);return c}function b(e,c){const r=t.isRef(e)?t.unref(e):e;return r&&typeof r=="object"&&"value"in r?r.value||c:r||c}const d="ecLoadingOptions";function O(e,c,r){const o=t.inject(d,{}),l=t.computed(()=>({...b(o,{}),...r?.value}));t.watchEffect(()=>{const h=e.value;h&&(c.value?h.showLoading(l.value):h.hideLoading())})}const X={loading:Boolean,loadingOptions:Object},B="x-vue-echarts";t.Vue2&&t.Vue2.config.ignoredElements.push(B);const q="ecTheme",G="ecInitOptions",W="ecUpdateOptions",k=/(^&?~?!?)native:/;var F=t.defineComponent({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...f,...X},emits:{},inheritAttrs:!1,setup(e,{attrs:c}){const r=t.shallowRef(),o=t.shallowRef(),l=t.shallowRef(),h=t.inject(q,null),Z=t.inject(G,null),U=t.inject(W,null),{autoresize:g,manualUpdate:C,loading:K,loadingOptions:S}=t.toRefs(e),T=t.computed(()=>l.value||e.option||null),z=t.computed(()=>e.theme||b(h,{})),j=t.computed(()=>e.initOptions||b(Z,{})),w=t.computed(()=>e.updateOptions||b(U,{})),Y=t.computed(()=>P(c)),$={},x=t.getCurrentInstance().proxy.$listeners,L={};x?Object.keys(x).forEach(i=>{k.test(i)?$[i.replace(k,"$1")]=x[i]:L[i]=x[i]}):Object.keys(c).filter(i=>u(i)).forEach(i=>{let s=i.charAt(2).toLowerCase()+i.slice(3);if(s.indexOf("native:")===0){const y=`on${s.charAt(7).toUpperCase()}${s.slice(8)}`;$[y]=c[i];return}s.substring(s.length-4)==="Once"&&(s=`~${s.substring(0,s.length-4)}`),L[s]=c[i]});function N(i){if(!r.value)return;const s=o.value=A.init(r.value,z.value,j.value);e.group&&(s.group=e.group),Object.keys(L).forEach(I=>{let _=L[I];if(!_)return;let E=I.toLowerCase();E.charAt(0)==="~"&&(E=E.substring(1),_.__once__=!0);let M=s;if(E.indexOf("zr:")===0&&(M=s.getZr(),E=E.substring(3)),_.__once__){delete _.__once__;const nt=_;_=(...et)=>{nt(...et),M.off(E,_)}}M.on(E,_)});function y(){s&&!s.isDisposed()&&s.resize()}function Q(){const I=i||T.value;I&&s.setOption(I,w.value)}g.value?t.nextTick(()=>{y(),Q()}):Q()}function D(i,s){e.manualUpdate&&(l.value=i),o.value?o.value.setOption(i,s||{}):N(i)}function J(){o.value&&(o.value.dispose(),o.value=void 0)}let R=null;t.watch(C,i=>{typeof R=="function"&&(R(),R=null),i||(R=t.watch(()=>e.option,(s,y)=>{s&&(o.value?o.value.setOption(s,{notMerge:s!==y,...w.value}):N())},{deep:!0}))},{immediate:!0}),t.watch([z,j],()=>{J(),N()},{deep:!0}),t.watchEffect(()=>{e.group&&o.value&&(o.value.group=e.group)});const tt=H(o);return O(o,K,S),a(o,g,r),t.onMounted(()=>{N()}),t.onBeforeUnmount(()=>{J()}),{chart:o,root:r,setOption:D,nonEventAttrs:Y,nativeListeners:$,...tt}},render(){const e=t.Vue2?{attrs:this.nonEventAttrs,on:this.nativeListeners}:{...this.nonEventAttrs,...this.nativeListeners};return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",t.h(B,e)}}),m=Object.freeze({__proto__:null,INIT_OPTIONS_KEY:G,LOADING_OPTIONS_KEY:d,THEME_KEY:q,UPDATE_OPTIONS_KEY:W,default:F}),V={...F,...m};return V});
//# sourceMappingURL=index.min.js.map
