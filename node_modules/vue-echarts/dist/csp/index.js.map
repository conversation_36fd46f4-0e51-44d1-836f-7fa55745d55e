{"version": 3, "file": "index.js", "sources": ["../../src/composables/api.ts", "../../src/composables/autoresize.ts", "../../src/utils.ts", "../../src/composables/loading.ts", "../../src/wc.ts", "../../src/ECharts.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Ref } from \"vue-demi\";\nimport type { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "import { watch } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\n\nimport type { Ref, PropType } from \"vue-demi\";\nimport type { EChartsType, AutoResize } from \"../types\";\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoResize | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  watch(\n    [root, chart, autoresize],\n    ([root, chart, autoresize], _, onCleanup) => {\n      let ro: ResizeObserver | null = null;\n\n      if (root && chart && autoresize) {\n        const { offsetWidth, offsetHeight } = root;\n        const autoresizeOptions = autoresize === true ? {} : autoresize;\n        const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n        let initialResizeTriggered = false;\n\n        const callback = () => {\n          chart.resize();\n          onResize?.();\n        };\n\n        const resizeCallback = wait ? throttle(callback, wait) : callback;\n\n        ro = new ResizeObserver(() => {\n          // We just skip ResizeObserver's initial resize callback if the\n          // size has not changed since the chart is rendered.\n          if (!initialResizeTriggered) {\n            initialResizeTriggered = true;\n            if (\n              root.offsetWidth === offsetWidth &&\n              root.offsetHeight === offsetHeight\n            ) {\n              return;\n            }\n          }\n          resizeCallback();\n        });\n        ro.observe(root);\n      }\n\n      onCleanup(() => {\n        if (ro) {\n          ro.disconnect();\n          ro = null;\n        }\n      });\n    }\n  );\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoResize>\n};\n", "import { unref, isRef } from \"vue-demi\";\n\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport { inject, computed, watchEffect } from \"vue-demi\";\n\nimport type { Ref, InjectionKey, PropType } from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      // Use esbuild repl to keep build process simple\n      // https://esbuild.github.io/try/#dAAwLjIzLjAALS1taW5pZnkAY2xhc3MgRUNoYXJ0c0VsZW1lbnQgZXh0ZW5kcyBIVE1MRWxlbWVudCB7CiAgX19kaXNwb3NlID0gbnVsbDsKCiAgZGlzY29ubmVjdGVkQ2FsbGJhY2soKSB7CiAgICBpZiAodGhpcy5fX2Rpc3Bvc2UpIHsKICAgICAgdGhpcy5fX2Rpc3Bvc2UoKTsKICAgICAgdGhpcy5fX2Rpc3Bvc2UgPSBudWxsOwogICAgfQogIH0KfQoKaWYgKGN1c3RvbUVsZW1lbnRzLmdldCh0YWcpID09IG51bGwpIHsKICBjdXN0b21FbGVtZW50cy5kZWZpbmUodGFnLCBFQ2hhcnRzRWxlbWVudCk7Cn0K\n      \"class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);\"\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\n\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME } from \"./wc\";\n\nimport type { PropType, InjectionKey } from \"vue-demi\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport type { EChartsElement } from \"./wc\";\n\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!root.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        root.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, root);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs);\n  }\n});\n"], "names": ["root", "chart", "autoresize", "init", "initChart", "manualUpdate"], "mappings": ";;;AAIA,MAAM,YAAe,GAAA;AAAA,EACnB,UAAA;AAAA,EACA,WAAA;AAAA,EACA,QAAA;AAAA,EACA,WAAA;AAAA,EACA,QAAA;AAAA,EACA,gBAAA;AAAA,EACA,gBAAA;AAAA,EACA,kBAAA;AAAA,EACA,cAAA;AAAA,EACA,YAAA;AAAA,EACA,qBAAA;AAAA,EACA,YAAA;AAAA,EACA,OAAA;AAAA,EACA,YAAA;AAAA,EACA,SAAA;AACF,CAAA,CAAA;AAMO,SAAS,aACd,KACe,EAAA;AACf,EAAA,SAAS,iBACP,IACqE,EAAA;AACrE,IAAA,OAAO,IAAI,IAAS,KAAA;AAClB,MAAI,IAAA,CAAC,MAAM,KAAO,EAAA;AAChB,QAAM,MAAA,IAAI,MAAM,iCAAiC,CAAA,CAAA;AAAA,OACnD;AACA,MAAA,OAAQ,MAAM,KAAM,CAAA,IAAI,EAAU,KAAM,CAAA,KAAA,CAAM,OAAO,IAAI,CAAA,CAAA;AAAA,KAC3D,CAAA;AAAA,GACF;AAEA,EAAA,SAAS,iBAAmC,GAAA;AAC1C,IAAM,MAAA,OAAA,mBAAiB,MAAA,CAAA,MAAA,CAAO,IAAI,CAAA,CAAA;AAClC,IAAA,YAAA,CAAa,QAAQ,CAAQ,IAAA,KAAA;AAC3B,MAAQ,OAAA,CAAA,IAAI,CAAI,GAAA,gBAAA,CAAiB,IAAI,CAAA,CAAA;AAAA,KACtC,CAAA,CAAA;AAED,IAAO,OAAA,OAAA,CAAA;AAAA,GACT;AAEA,EAAA,OAAO,iBAAkB,EAAA,CAAA;AAC3B;;AC5CgB,SAAA,aAAA,CACd,KACA,EAAA,UAAA,EACA,IACM,EAAA;AACN,EAAA,KAAA;AAAA,IACE,CAAC,IAAM,EAAA,KAAA,EAAO,UAAU,CAAA;AAAA,IACxB,CAAC,CAACA,KAAAA,EAAMC,QAAOC,WAAU,CAAA,EAAG,GAAG,SAAc,KAAA;AAC3C,MAAA,IAAI,EAA4B,GAAA,IAAA,CAAA;AAEhC,MAAIF,IAAAA,KAAAA,IAAQC,UAASC,WAAY,EAAA;AAC/B,QAAM,MAAA,EAAE,WAAa,EAAA,YAAA,EAAiBF,GAAAA,KAAAA,CAAAA;AACtC,QAAA,MAAM,iBAAoBE,GAAAA,WAAAA,KAAe,IAAO,GAAA,EAAKA,GAAAA,WAAAA,CAAAA;AACrD,QAAA,MAAM,EAAE,QAAA,EAAU,IAAO,GAAA,GAAA,EAAK,UAAa,GAAA,iBAAA,CAAA;AAE3C,QAAA,IAAI,sBAAyB,GAAA,KAAA,CAAA;AAE7B,QAAA,MAAM,WAAW,MAAM;AACrB,UAAAD,OAAM,MAAO,EAAA,CAAA;AACb,UAAW,QAAA,IAAA,CAAA;AAAA,SACb,CAAA;AAEA,QAAA,MAAM,cAAiB,GAAA,IAAA,GAAO,QAAS,CAAA,QAAA,EAAU,IAAI,CAAI,GAAA,QAAA,CAAA;AAEzD,QAAK,EAAA,GAAA,IAAI,eAAe,MAAM;AAG5B,UAAA,IAAI,CAAC,sBAAwB,EAAA;AAC3B,YAAyB,sBAAA,GAAA,IAAA,CAAA;AACzB,YAAA,IACED,KAAK,CAAA,WAAA,KAAgB,WACrBA,IAAAA,KAAAA,CAAK,iBAAiB,YACtB,EAAA;AACA,cAAA,OAAA;AAAA,aACF;AAAA,WACF;AACA,UAAe,cAAA,EAAA,CAAA;AAAA,SAChB,CAAA,CAAA;AACD,QAAA,EAAA,CAAG,QAAQA,KAAI,CAAA,CAAA;AAAA,OACjB;AAEA,MAAA,SAAA,CAAU,MAAM;AACd,QAAA,IAAI,EAAI,EAAA;AACN,UAAA,EAAA,CAAG,UAAW,EAAA,CAAA;AACd,UAAK,EAAA,GAAA,IAAA,CAAA;AAAA,SACP;AAAA,OACD,CAAA,CAAA;AAAA,KACH;AAAA,GACF,CAAA;AACF,CAAA;AAEO,MAAM,eAAkB,GAAA;AAAA,EAC7B,UAAA,EAAY,CAAC,OAAA,EAAS,MAAM,CAAA;AAC9B,CAAA;;AChDA,MAAM,IAAO,GAAA,WAAA,CAAA;AACN,MAAM,IAAO,GAAA,CAAC,GAAyB,KAAA,IAAA,CAAK,KAAK,GAAG,CAAA,CAAA;AAEpD,SAAS,OAAO,KAAqB,EAAA;AAC1C,EAAA,MAAM,SAAgB,EAAC,CAAA;AACvB,EAAA,KAAA,MAAW,OAAO,KAAO,EAAA;AACvB,IAAI,IAAA,CAAC,IAAK,CAAA,GAAG,CAAG,EAAA;AACd,MAAO,MAAA,CAAA,GAAG,CAAI,GAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAAA,KACzB;AAAA,GACF;AAEA,EAAO,OAAA,MAAA,CAAA;AACT,CAAA;AAEgB,SAAA,cAAA,CACd,WACA,YACO,EAAA;AACP,EAAA,MAAM,QAAQ,KAAM,CAAA,SAAS,CAAI,GAAA,KAAA,CAAM,SAAS,CAAI,GAAA,SAAA,CAAA;AAEpD,EAAA,IAAI,KAAS,IAAA,OAAO,KAAU,KAAA,QAAA,IAAY,WAAW,KAAO,EAAA;AAC1D,IAAA,OAAO,MAAM,KAAS,IAAA,YAAA,CAAA;AAAA,GACxB;AAEA,EAAA,OAAO,KAAS,IAAA,YAAA,CAAA;AAClB;;AC9BO,MAAM,mBACX,GAAA,mBAAA;AAIc,SAAA,UAAA,CACd,KACA,EAAA,OAAA,EACA,cACM,EAAA;AACN,EAAA,MAAM,qBAAwB,GAAA,MAAA,CAAO,mBAAqB,EAAA,EAAE,CAAA,CAAA;AAC5D,EAAM,MAAA,kBAAA,GAAqB,SAAS,OAAO;AAAA,IACzC,GAAG,cAAA,CAAe,qBAAuB,EAAA,EAAE,CAAA;AAAA,IAC3C,GAAG,cAAgB,EAAA,KAAA;AAAA,GACnB,CAAA,CAAA,CAAA;AAEF,EAAA,WAAA,CAAY,MAAM;AAChB,IAAA,MAAM,WAAW,KAAM,CAAA,KAAA,CAAA;AACvB,IAAA,IAAI,CAAC,QAAU,EAAA;AACb,MAAA,OAAA;AAAA,KACF;AAEA,IAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,MAAS,QAAA,CAAA,WAAA,CAAY,mBAAmB,KAAK,CAAA,CAAA;AAAA,KACxC,MAAA;AACL,MAAA,QAAA,CAAS,WAAY,EAAA,CAAA;AAAA,KACvB;AAAA,GACD,CAAA,CAAA;AACH,CAAA;AAEO,MAAM,YAAe,GAAA;AAAA,EAC1B,OAAS,EAAA,OAAA;AAAA,EACT,cAAgB,EAAA,MAAA;AAClB,CAAA;;ACrCO,MAAM,QAAW,GAAA,eAAA;;AC+CxB,IAAI,IAAM,EAAA;AACR,EAAK,IAAA,CAAA,MAAA,CAAO,eAAgB,CAAA,IAAA,CAAK,QAAQ,CAAA,CAAA;AAC3C,CAAA;AAEO,MAAM,SAAY,GAAA,UAAA;AAClB,MAAM,gBACX,GAAA,gBAAA;AACK,MAAM,kBACX,GAAA,kBAAA;AAGF,MAAM,eAAkB,GAAA,kBAAA,CAAA;AAExB,cAAe,eAAgB,CAAA;AAAA,EAC7B,IAAM,EAAA,SAAA;AAAA,EACN,KAAO,EAAA;AAAA,IACL,MAAQ,EAAA,MAAA;AAAA,IACR,KAAO,EAAA;AAAA,MACL,IAAA,EAAM,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,KACvB;AAAA,IACA,WAAa,EAAA,MAAA;AAAA,IACb,aAAe,EAAA,MAAA;AAAA,IACf,KAAO,EAAA,MAAA;AAAA,IACP,YAAc,EAAA,OAAA;AAAA,IACd,GAAG,eAAA;AAAA,IACH,GAAG,YAAA;AAAA,GACL;AAAA,EACA,OAAO,EAAC;AAAA,EACR,YAAc,EAAA,KAAA;AAAA,EACd,KAAM,CAAA,KAAA,EAAO,EAAE,KAAA,EAAS,EAAA;AACtB,IAAA,MAAM,OAAO,UAA2B,EAAA,CAAA;AACxC,IAAA,MAAM,QAAQ,UAAwB,EAAA,CAAA;AACtC,IAAA,MAAM,eAAe,UAAmB,EAAA,CAAA;AACxC,IAAM,MAAA,YAAA,GAAe,MAAO,CAAA,SAAA,EAAW,IAAI,CAAA,CAAA;AAC3C,IAAM,MAAA,kBAAA,GAAqB,MAAO,CAAA,gBAAA,EAAkB,IAAI,CAAA,CAAA;AACxD,IAAM,MAAA,oBAAA,GAAuB,MAAO,CAAA,kBAAA,EAAoB,IAAI,CAAA,CAAA;AAE5D,IAAA,MAAM,EAAE,UAAY,EAAA,YAAA,EAAc,SAAS,cAAe,EAAA,GAAI,OAAO,KAAK,CAAA,CAAA;AAE1E,IAAA,MAAM,UAAa,GAAA,QAAA;AAAA,MACjB,MAAM,YAAA,CAAa,KAAS,IAAA,KAAA,CAAM,MAAU,IAAA,IAAA;AAAA,KAC9C,CAAA;AACA,IAAA,MAAM,SAAY,GAAA,QAAA;AAAA,MAChB,MAAM,KAAM,CAAA,KAAA,IAAS,cAAe,CAAA,YAAA,EAAc,EAAE,CAAA;AAAA,KACtD,CAAA;AACA,IAAA,MAAM,eAAkB,GAAA,QAAA;AAAA,MACtB,MAAM,KAAM,CAAA,WAAA,IAAe,cAAe,CAAA,kBAAA,EAAoB,EAAE,CAAA;AAAA,KAClE,CAAA;AACA,IAAA,MAAM,iBAAoB,GAAA,QAAA;AAAA,MACxB,MAAM,KAAM,CAAA,aAAA,IAAiB,cAAe,CAAA,oBAAA,EAAsB,EAAE,CAAA;AAAA,KACtE,CAAA;AACA,IAAA,MAAM,aAAgB,GAAA,QAAA,CAAS,MAAM,MAAA,CAAO,KAAK,CAAC,CAAA,CAAA;AAClD,IAAA,MAAM,kBAA2C,EAAC,CAAA;AAGlD,IAAM,MAAA,SAAA,GAAY,kBAAmB,EAAA,CAAE,KAAM,CAAA,UAAA,CAAA;AAC7C,IAAA,MAAM,gBAAqC,EAAC,CAAA;AAE5C,IAAA,IAAI,CAAC,SAAW,EAAA;AAOd,MAAO,MAAA,CAAA,IAAA,CAAK,KAAK,CAAA,CACd,MAAO,CAAA,CAAA,GAAA,KAAO,KAAK,GAAG,CAAC,CACvB,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AAGd,QAAI,IAAA,KAAA,GAAQ,IAAI,MAAO,CAAA,CAAC,EAAE,WAAY,EAAA,GAAI,GAAI,CAAA,KAAA,CAAM,CAAC,CAAA,CAAA;AAGrD,QAAA,IAAI,KAAM,CAAA,OAAA,CAAQ,SAAS,CAAA,KAAM,CAAG,EAAA;AAElC,UAAM,MAAA,SAAA,GAAY,KAAK,KAAM,CAAA,MAAA,CAAO,CAAC,CAAE,CAAA,WAAA,EAAa,CAAA,EAAG,KAAM,CAAA,KAAA;AAAA,YAC3D,CAAA;AAAA,WACD,CAAA,CAAA,CAAA;AAED,UAAgB,eAAA,CAAA,SAAS,CAAI,GAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AACtC,UAAA,OAAA;AAAA,SACF;AAIA,QAAA,IAAI,MAAM,SAAU,CAAA,KAAA,CAAM,MAAS,GAAA,CAAC,MAAM,MAAQ,EAAA;AAChD,UAAA,KAAA,GAAQ,IAAI,KAAM,CAAA,SAAA,CAAU,GAAG,KAAM,CAAA,MAAA,GAAS,CAAC,CAAC,CAAA,CAAA,CAAA;AAAA,SAClD;AAEA,QAAc,aAAA,CAAA,KAAK,CAAI,GAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAAA,OACjC,CAAA,CAAA;AAAA,KACE,MAAA;AASL,MAAA,MAAA,CAAO,IAAK,CAAA,SAAS,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AACpC,QAAI,IAAA,eAAA,CAAgB,IAAK,CAAA,GAAG,CAAG,EAAA;AAC7B,UAAA,eAAA,CAAgB,IAAI,OAAQ,CAAA,eAAA,EAAiB,IAAI,CAAC,CAAA,GAAI,UAAU,GAAG,CAAA,CAAA;AAAA,SAC9D,MAAA;AACL,UAAc,aAAA,CAAA,GAAG,CAAI,GAAA,SAAA,CAAU,GAAG,CAAA,CAAA;AAAA,SACpC;AAAA,OACD,CAAA,CAAA;AAAA,KACH;AAEA,IAAA,SAASG,OAAK,MAAiB,EAAA;AAC7B,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA;AACf,QAAA,OAAA;AAAA,OACF;AAEA,MAAM,MAAA,QAAA,GAAY,MAAM,KAAQ,GAAAC,IAAA;AAAA,QAC9B,IAAK,CAAA,KAAA;AAAA,QACL,SAAU,CAAA,KAAA;AAAA,QACV,eAAgB,CAAA,KAAA;AAAA,OAClB,CAAA;AAEA,MAAA,IAAI,MAAM,KAAO,EAAA;AACf,QAAA,QAAA,CAAS,QAAQ,KAAM,CAAA,KAAA,CAAA;AAAA,OACzB;AAEA,MAAA,MAAA,CAAO,IAAK,CAAA,aAAa,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AACxC,QAAI,IAAA,OAAA,GAAU,cAAc,GAAG,CAAA,CAAA;AAE/B,QAAA,IAAI,CAAC,OAAS,EAAA;AACZ,UAAA,OAAA;AAAA,SACF;AAEA,QAAI,IAAA,KAAA,GAAQ,IAAI,WAAY,EAAA,CAAA;AAC5B,QAAA,IAAI,KAAM,CAAA,MAAA,CAAO,CAAC,CAAA,KAAM,GAAK,EAAA;AAC3B,UAAQ,KAAA,GAAA,KAAA,CAAM,UAAU,CAAC,CAAA,CAAA;AACzB,UAAA,OAAA,CAAQ,QAAW,GAAA,IAAA,CAAA;AAAA,SACrB;AAEA,QAAA,IAAI,MAAsB,GAAA,QAAA,CAAA;AAC1B,QAAA,IAAI,KAAM,CAAA,OAAA,CAAQ,KAAK,CAAA,KAAM,CAAG,EAAA;AAC9B,UAAA,MAAA,GAAS,SAAS,KAAM,EAAA,CAAA;AACxB,UAAQ,KAAA,GAAA,KAAA,CAAM,UAAU,CAAC,CAAA,CAAA;AAAA,SAC3B;AAEA,QAAA,IAAI,QAAQ,QAAU,EAAA;AACpB,UAAA,OAAO,OAAQ,CAAA,QAAA,CAAA;AAEf,UAAA,MAAM,GAAM,GAAA,OAAA,CAAA;AAEZ,UAAA,OAAA,GAAU,IAAI,IAAgB,KAAA;AAC5B,YAAA,GAAA,CAAI,GAAG,IAAI,CAAA,CAAA;AACX,YAAO,MAAA,CAAA,GAAA,CAAI,OAAO,OAAO,CAAA,CAAA;AAAA,WAC3B,CAAA;AAAA,SACF;AAKA,QAAO,MAAA,CAAA,EAAA,CAAG,OAAO,OAAO,CAAA,CAAA;AAAA,OACzB,CAAA,CAAA;AAED,MAAA,SAAS,MAAS,GAAA;AAChB,QAAA,IAAI,QAAY,IAAA,CAAC,QAAS,CAAA,UAAA,EAAc,EAAA;AACtC,UAAA,QAAA,CAAS,MAAO,EAAA,CAAA;AAAA,SAClB;AAAA,OACF;AAEA,MAAA,SAAS,MAAS,GAAA;AAChB,QAAM,MAAA,GAAA,GAAM,UAAU,UAAW,CAAA,KAAA,CAAA;AACjC,QAAA,IAAI,GAAK,EAAA;AACP,UAAS,QAAA,CAAA,SAAA,CAAU,GAAK,EAAA,iBAAA,CAAkB,KAAK,CAAA,CAAA;AAAA,SACjD;AAAA,OACF;AAEA,MAAA,IAAI,WAAW,KAAO,EAAA;AAGpB,QAAA,QAAA,CAAS,MAAM;AACb,UAAO,MAAA,EAAA,CAAA;AACP,UAAO,MAAA,EAAA,CAAA;AAAA,SACR,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAO,MAAA,EAAA,CAAA;AAAA,OACT;AAAA,KACF;AAEA,IAAS,SAAA,SAAA,CAAU,QAAgB,aAA+B,EAAA;AAChE,MAAA,IAAI,MAAM,YAAc,EAAA;AACtB,QAAA,YAAA,CAAa,KAAQ,GAAA,MAAA,CAAA;AAAA,OACvB;AAEA,MAAI,IAAA,CAAC,MAAM,KAAO,EAAA;AAChB,QAAAD,MAAA,CAAK,MAAM,CAAA,CAAA;AAAA,OACN,MAAA;AACL,QAAA,KAAA,CAAM,KAAM,CAAA,SAAA,CAAU,MAAQ,EAAA,aAAA,IAAiB,EAAE,CAAA,CAAA;AAAA,OACnD;AAAA,KACF;AAEA,IAAA,SAAS,OAAU,GAAA;AACjB,MAAA,IAAI,MAAM,KAAO,EAAA;AACf,QAAA,KAAA,CAAM,MAAM,OAAQ,EAAA,CAAA;AACpB,QAAA,KAAA,CAAM,KAAQ,GAAA,KAAA,CAAA,CAAA;AAAA,OAChB;AAAA,KACF;AAEA,IAAA,IAAI,aAAqC,GAAA,IAAA,CAAA;AACzC,IAAA,KAAA;AAAA,MACE,YAAA;AAAA,MACA,CAAAE,aAAgB,KAAA;AACd,QAAI,IAAA,OAAO,kBAAkB,UAAY,EAAA;AACvC,UAAc,aAAA,EAAA,CAAA;AACd,UAAgB,aAAA,GAAA,IAAA,CAAA;AAAA,SAClB;AAEA,QAAA,IAAI,CAACA,aAAc,EAAA;AACjB,UAAgB,aAAA,GAAA,KAAA;AAAA,YACd,MAAM,KAAM,CAAA,MAAA;AAAA,YACZ,CAAC,QAAQ,SAAc,KAAA;AACrB,cAAA,IAAI,CAAC,MAAQ,EAAA;AACX,gBAAA,OAAA;AAAA,eACF;AACA,cAAI,IAAA,CAAC,MAAM,KAAO,EAAA;AAChB,gBAAKF,MAAA,EAAA,CAAA;AAAA,eACA,MAAA;AACL,gBAAM,KAAA,CAAA,KAAA,CAAM,UAAU,MAAQ,EAAA;AAAA;AAAA;AAAA,kBAG5B,UAAU,MAAW,KAAA,SAAA;AAAA,kBACrB,GAAG,iBAAkB,CAAA,KAAA;AAAA,iBACtB,CAAA,CAAA;AAAA,eACH;AAAA,aACF;AAAA,YACA,EAAE,MAAM,IAAK,EAAA;AAAA,WACf,CAAA;AAAA,SACF;AAAA,OACF;AAAA,MACA;AAAA,QACE,SAAW,EAAA,IAAA;AAAA,OACb;AAAA,KACF,CAAA;AAEA,IAAA,KAAA;AAAA,MACE,CAAC,WAAW,eAAe,CAAA;AAAA,MAC3B,MAAM;AACJ,QAAQ,OAAA,EAAA,CAAA;AACR,QAAKA,MAAA,EAAA,CAAA;AAAA,OACP;AAAA,MACA;AAAA,QACE,IAAM,EAAA,IAAA;AAAA,OACR;AAAA,KACF,CAAA;AAEA,IAAA,WAAA,CAAY,MAAM;AAChB,MAAI,IAAA,KAAA,CAAM,KAAS,IAAA,KAAA,CAAM,KAAO,EAAA;AAC9B,QAAM,KAAA,CAAA,KAAA,CAAM,QAAQ,KAAM,CAAA,KAAA,CAAA;AAAA,OAC5B;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAY,aAAa,KAAK,CAAA,CAAA;AAEpC,IAAW,UAAA,CAAA,KAAA,EAAO,SAAS,cAAc,CAAA,CAAA;AAEzC,IAAc,aAAA,CAAA,KAAA,EAAO,YAAY,IAAI,CAAA,CAAA;AAErC,IAAA,SAAA,CAAU,MAAM;AACd,MAAKA,MAAA,EAAA,CAAA;AAAA,KACN,CAAA,CAAA;AAED,IAAA,eAAA,CAAgB,MAAM;AACpB,MAMO;AACL,QAAQ,OAAA,EAAA,CAAA;AAAA,OACV;AAAA,KACD,CAAA,CAAA;AAED,IAAO,OAAA;AAAA,MACL,KAAA;AAAA,MACA,IAAA;AAAA,MACA,SAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,GAAG,SAAA;AAAA,KACL,CAAA;AAAA,GACF;AAAA,EACA,MAAS,GAAA;AAGP,IAAA,MAAM,QACJ,IACI,GAAA,EAAE,KAAO,EAAA,IAAA,CAAK,eAAe,EAAI,EAAA,IAAA,CAAK,eAAgB,EAAA,GACtD,EAAE,GAAG,IAAA,CAAK,aAAe,EAAA,GAAG,KAAK,eAAgB,EAAA,CAAA;AAEvD,IAAA,KAAA,CAAM,GAAM,GAAA,MAAA,CAAA;AACZ,IAAM,KAAA,CAAA,KAAA,GAAQ,MAAM,KAAQ,GAAA,CAAC,SAAS,CAAE,CAAA,MAAA,CAAO,KAAM,CAAA,KAAK,CAAI,GAAA,SAAA,CAAA;AAC9D,IAAO,OAAA,CAAA,CAAE,UAAU,KAAK,CAAA,CAAA;AAAA,GAC1B;AACF,CAAC,CAAA;;;;"}