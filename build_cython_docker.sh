#!/bin/bash

# =====================================================
# FastAPI Cython Docker 自动化构建脚本
# =====================================================
# 
# 功能说明：
# 1. 将Python源码编译为Cython .so文件以保护源代码
# 2. 构建包含编译后代码的Docker镜像
# 3. 自动测试编译结果和容器运行状态
# 4. 可选推送镜像到阿里云容器镜像服务
#
# 使用方法：
#   chmod +x build_cython_docker.sh
#   ./build_cython_docker.sh              # 交互式选择
#   ./build_cython_docker.sh --compile    # 仅编译测试
#   ./build_cython_docker.sh --docker     # 构建Docker镜像
#   ./build_cython_docker.sh --push       # 推送到阿里云ACR
#   ./build_cython_docker.sh --help       # 查看帮助
#
# 注意事项：
# - 确保已安装Docker和Python 3.9+
# - 确保requirements.txt包含所有依赖
# - main.py和__init__.py文件会保持原始格式
# - 构建前会自动备份源代码
# =====================================================

set -e

# =====================================================
# 颜色定义和工具函数
# =====================================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    FastAPI Cython Docker 自动化构建脚本"
    echo "=================================================="
    echo -e "${NC}"
}

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# =====================================================
# 函数：检查环境要求
# =====================================================
check_requirements() {
    print_step "检查环境要求..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_message "Python版本: $PYTHON_VERSION"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        exit 1
    fi
    
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    print_message "Docker版本: $DOCKER_VERSION"
    
    # 检查必要的Python包
    print_message "检查Python依赖..."
    if ! python3 -c "import cython" 2>/dev/null; then
        print_warning "Cython未安装，正在安装..."
        pip install cython setuptools wheel
    fi
    
    # 检查项目结构
    if [ ! -f "setup.py" ]; then
        print_error "未找到setup.py文件"
        exit 1
    fi
    
    if [ ! -d "app" ]; then
        print_error "未找到app目录"
        exit 1
    fi
    
    print_message "环境检查通过 ✅"
}

# =====================================================
# 函数：备份源代码
# =====================================================
create_backup() {
    print_step "备份源代码..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    
    if [ -d "$BACKUP_DIR" ]; then
        print_warning "备份目录已存在，跳过备份"
        return
    fi
    
    # 创建备份
    cp -r app "$BACKUP_DIR"
    print_message "源代码已备份到: $BACKUP_DIR"
    
    # 清理之前的编译产物
    print_message "清理之前的编译产物..."
    find app -name "*.so" -delete 2>/dev/null || true
    find app -name "*.c" -delete 2>/dev/null || true
    rm -rf build/ 2>/dev/null || true
    
    print_message "备份完成 ✅"
}

# =====================================================
# 函数：测试Cython编译
# =====================================================
test_cython_build() {
    print_step "测试Cython编译..."
    
    # 执行Cython编译
    print_message "执行Cython编译..."
    if python3 setup.py build_ext --inplace; then
        # 检查编译结果
        SO_COUNT=$(find app -name "*.so" 2>/dev/null | wc -l)
        print_message "编译生成了 $SO_COUNT 个.so文件"
        
        if [ "$SO_COUNT" -eq 0 ]; then
            print_error "Cython编译失败，没有生成.so文件"
            exit 1
        fi
        
        # 列出生成的编译文件
        print_message "生成的.so文件列表："
        find app -name "*.so" | head -10 | while read file; do
            echo "  ✅ $file"
        done
        
        if [ "$SO_COUNT" -gt 10 ]; then
            echo "  ... 还有 $((SO_COUNT - 10)) 个文件"
        fi
        
        print_message "本地Cython编译测试通过 ✅"
    else
        print_error "Cython编译失败"
        exit 1
    fi
}

# =====================================================
# 函数：测试模块导入
# =====================================================
test_import_modules() {
    print_step "测试模块导入..."
    
    # 创建临时验证脚本
    cat > temp_verify.py << 'EOF'
#!/usr/bin/env python3
"""
Cython编译结果验证脚本
"""

import sys
import time
from pathlib import Path

def print_header():
    print("🔍 Cython编译结果验证")
    print("=" * 50)

def check_compiled_modules():
    print("\n🔍 检查Cython编译结果...")
    
    app_dir = Path("app")
    if not app_dir.exists():
        print("❌ 未找到app目录")
        return False
    
    # 查找所有.so和.py文件
    so_files = list(app_dir.rglob("*.so"))
    py_files = list(app_dir.rglob("*.py"))
    
    # 应该保留的.py文件
    excluded_py = ["__init__.py", "main.py"]
    remaining_py = [f for f in py_files if f.name not in excluded_py]
    
    print(f"✅ 找到 {len(so_files)} 个 .so 文件")
    print(f"✅ 找到 {len(py_files)} 个 .py 文件")
    print(f"✅ 剩余 {len(remaining_py)} 个业务.py文件")
    
    # 显示.so文件列表（只显示前10个）
    if so_files:
        print("\n📁 编译生成的.so文件:")
        for so_file in sorted(so_files)[:10]:
            rel_path = so_file.relative_to(app_dir)
            print(f"   ✅ {rel_path}")
        if len(so_files) > 10:
            print(f"   ... 还有 {len(so_files) - 10} 个文件")
    
    return len(so_files) > 0

def test_import_modules():
    print("\n🧪 测试模块导入...")
    
    # 测试关键模块
    test_modules = [
        ("app.main", "FastAPI主应用"),
        ("app.core.config", "配置模块"),
        ("app.core.database", "数据库模块"),
        ("app.api.v1.router", "API路由"),
    ]
    
    success_count = 0
    total_count = len(test_modules)
    failed_modules = []
    
    for module_name, description in test_modules:
        try:
            # 尝试导入模块
            module = __import__(module_name, fromlist=[''])
            print(f"✅ {module_name} ({description}) - 导入成功")
            
            # 检查模块是否来自.so文件
            if hasattr(module, '__file__') and module.__file__:
                if module.__file__.endswith('.so'):
                    print(f"   🔒 来源: {module.__file__} (已编译)")
                else:
                    print(f"   📄 来源: {module.__file__} (源码)")
            
            success_count += 1
            
        except ImportError as e:
            failed_modules.append((module_name, description, str(e)))
            print(f"❌ {module_name} ({description}) - 导入失败: {e}")
        except Exception as e:
            failed_modules.append((module_name, description, str(e)))
            print(f"❌ {module_name} ({description}) - 导入异常: {e}")
    
    # 计算成功率
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    print(f"\n📊 模块导入成功率: {success_rate:.1f}% ({success_count}/{total_count})")
    
    # 如果有失败的模块，详细报告
    if failed_modules:
        print(f"\n❌ 模块导入失败详情:")
        for module_name, description, error in failed_modules:
            print(f"   - {module_name} ({description}): {error}")
        
        # 检查是否是依赖问题
        dependency_issues = [f for f in failed_modules if "No module named" in f[2]]
        if dependency_issues:
            print(f"\n💡 发现依赖缺失问题:")
            for module_name, _, error in dependency_issues:
                missing_dep = error.split("'")[1] if "'" in error else "未知"
                print(f"   - 缺少依赖: {missing_dep}")
            print("\n解决方案:")
            print("   1. 检查requirements.txt是否包含所有必要依赖")
            print("   2. 运行: pip install -r requirements.txt")
            print("   3. 确保所有依赖版本兼容")
        
        return False
    
    print("\n🎉 所有模块导入测试通过！")
    return True

def check_file_protection():
    print("\n🔒 检查源代码保护情况...")
    
    app_dir = Path("app")
    
    # 统计保护情况
    protected_count = 0
    total_modules = 0
    unprotected_files = []
    
    # 遍历所有Python文件
    for py_file in app_dir.rglob("*.py"):
        # 跳过应该保留的文件
        if py_file.name in ["__init__.py", "main.py"]:
            continue
            
        total_modules += 1
        
        # 检查对应的.so文件是否存在
        py_relative = py_file.relative_to(app_dir)
        so_pattern = str(py_relative.with_suffix(".cpython-*-*.so"))
        so_files = list(app_dir.glob(so_pattern))
        
        if so_files:
            protected_count += 1
            rel_path = py_file.relative_to(app_dir)
            so_name = so_files[0].name
            print(f"✅ {rel_path} -> {so_name} (已保护)")
        else:
            unprotected_files.append(py_file)
            rel_path = py_file.relative_to(app_dir)
            print(f"❌ {rel_path} (未保护)")
    
    # 统计结果
    protection_rate = (protected_count / total_modules * 100) if total_modules > 0 else 0
    print(f"\n📊 源码保护率: {protection_rate:.1f}% ({protected_count}/{total_modules})")
    
    if unprotected_files:
        print(f"\n⚠️ 未保护的文件:")
        for file in unprotected_files[:5]:  # 只显示前5个
            rel_path = file.relative_to(app_dir)
            print(f"   - {rel_path}")
        if len(unprotected_files) > 5:
            print(f"   ... 还有 {len(unprotected_files) - 5} 个文件")
    
    return protection_rate >= 80  # 80%以上保护率认为成功

def main():
    print_header()
    
    # 检查是否在正确的目录
    if not Path("app").exists():
        print("❌ 未找到app目录，请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 执行各项检查
    checks = [
        ("编译结果检查", check_compiled_modules),
        ("模块导入测试", test_import_modules), 
        ("源码保护检查", check_file_protection),
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            results.append((check_name, False))
    
    # 生成验证报告
    total_checks = len(results)
    passed_checks = sum(1 for _, result in results if result)
    
    print(f"\n{'='*20} 验证报告 {'='*20}")
    print(f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总检查项: {total_checks}")
    print(f"通过项目: {passed_checks}")
    print(f"失败项目: {total_checks - passed_checks}")
    print(f"通过率: {(passed_checks/total_checks*100):.1f}%")
    
    print("\n详细结果:")
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    # 总体评估
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过了！Cython编译成功。")
        print("✅ 源代码已得到有效保护")
        print("✅ 编译后的模块可以正常使用")
        sys.exit(0)
    else:
        print(f"\n⚠️  {total_checks - passed_checks} 项检查未通过，需要进一步检查。")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

    # 运行验证脚本
    print_message "运行编译验证脚本..."
    if python3 temp_verify.py; then
        print_message "模块导入测试通过 ✅"
    else
        print_error "模块导入测试失败 ❌"
        # 清理临时文件
        rm -f temp_verify.py
        exit 1
    fi
    
    # 清理临时文件
    rm -f temp_verify.py
    print_message "模块导入测试完成 ✅"
}

# =====================================================
# 函数：测试FastAPI启动
# =====================================================
test_fastapi_startup() {
    print_step "测试FastAPI应用启动..."
    
    # 创建测试脚本
    cat > temp_fastapi_test.py << 'EOF'
#!/usr/bin/env python3
"""
FastAPI启动测试脚本
"""

import sys
import time
import signal
import subprocess
from pathlib import Path

def test_fastapi_startup():
    print("🧪 测试FastAPI应用启动...")
    
    # 检查main.py是否存在
    if not Path("app/main.py").exists():
        print("❌ 未找到app/main.py文件")
        return False
    
    try:
        # 启动FastAPI应用（后台运行）
        print("🚀 启动FastAPI应用...")
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "127.0.0.1", 
            "--port", "8999",
            "--log-level", "error"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待应用启动
        print("⏳ 等待应用启动（5秒）...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ FastAPI应用启动成功")
            
            # 尝试简单的HTTP请求测试
            try:
                import requests
                response = requests.get("http://127.0.0.1:8999/api/v1/health/hello", timeout=5)
                if response.status_code == 200:
                    print("✅ API响应测试通过")
                    result = True
                else:
                    print(f"⚠️ API响应异常: {response.status_code}")
                    result = True  # 启动成功就算通过
            except ImportError:
                print("⚠️ requests未安装，跳过API测试")
                result = True  # 启动成功就算通过
            except Exception as e:
                print(f"⚠️ API测试失败: {e}")
                result = True  # 启动成功就算通过
        else:
            print("❌ FastAPI应用启动失败")
            stdout, stderr = process.communicate()
            if stderr:
                print(f"错误信息: {stderr.decode()[:500]}")
            result = False
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        return result
        
    except Exception as e:
        print(f"❌ FastAPI启动测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_fastapi_startup()
    sys.exit(0 if success else 1)
EOF

    # 运行FastAPI启动测试
    if python3 temp_fastapi_test.py; then
        print_message "FastAPI启动测试通过 ✅"
    else
        print_warning "FastAPI启动测试失败，但不影响Docker构建 ⚠️"
    fi
    
    # 清理临时文件
    rm -f temp_fastapi_test.py
}

# =====================================================
# 函数：智能检测构建平台
# 根据系统环境智能选择构建策略
# =====================================================
detect_build_strategy() {
    local OS_TYPE=$(uname -s)
    local ARCH_TYPE=$(uname -m)
    
    print_message "系统信息: $OS_TYPE $ARCH_TYPE" >&2
    
    case "$OS_TYPE" in
        "Darwin")
            # Mac系统，支持多架构构建
            if command -v docker buildx &> /dev/null; then
                print_message "检测到Mac系统，支持多架构构建" >&2
                echo "multi:linux/amd64,linux/arm64"
            else
                print_warning "Mac系统但不支持buildx，构建Linux版本" >&2
                echo "single:linux"
            fi
            ;;
        "Linux")
            case "$ARCH_TYPE" in
                "x86_64"|"amd64")
                    print_message "检测到Linux x86_64，构建amd64版本" >&2
                    echo "single:linux/amd64"
                    ;;
                "aarch64"|"arm64")
                    print_message "检测到Linux ARM64，构建arm64版本" >&2
                    echo "single:linux/arm64"
                    ;;
                *)
                    print_warning "未知Linux架构 ($ARCH_TYPE)，构建amd64版本" >&2
                    echo "single:linux/amd64"
                    ;;
            esac
            ;;
        *)       
            print_message "检测到未知系统 ($OS_TYPE)" >&2
            print_warning "未知系统，构建Linux版本" >&2
            echo "single:linux" 
            ;;
    esac
}

# =====================================================
# 函数：确保基础镜像可用
# 检查多架构基础镜像是否可用
# =====================================================
ensure_base_image() {
    print_message "检查环境特定基础镜像..."
    
    local BASE_IMAGE_LINUX="crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-linux"
    local BASE_IMAGE_MAC="crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-maclocal"
    
    # 先尝试登录阿里云镜像仓库
    print_message "登录阿里云镜像仓库..."
    if ! echo "test_v123" | docker login crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com -u backkom96 --password-stdin >/dev/null 2>&1; then
        print_error "❌ 阿里云镜像仓库登录失败"
        return 1
    fi
    
    # 拉取Linux基础镜像
    print_message "拉取Linux基础镜像..."
    if docker pull "$BASE_IMAGE_LINUX" >/dev/null 2>&1; then
        print_message "✅ Linux基础镜像可用"
        docker tag "$BASE_IMAGE_LINUX" "python:3.9-slim-linux"
    else
        print_warning "⚠️ Linux基础镜像拉取失败"
    fi
    
    # 拉取Mac基础镜像
    print_message "拉取Mac基础镜像..."
    if docker pull "$BASE_IMAGE_MAC" >/dev/null 2>&1; then
        print_message "✅ Mac基础镜像可用"
        docker tag "$BASE_IMAGE_MAC" "python:3.9-slim-maclocal"
    else
        print_warning "⚠️ Mac基础镜像拉取失败"
    fi
    
    return 0
}

# =====================================================
# 函数：构建Docker镜像
# 支持智能多架构构建
# =====================================================
build_docker_image() {
    print_step "构建Docker镜像..."
    
    # 1. 确保基础镜像可用
    if ! ensure_base_image; then
        print_error "基础镜像检查失败，无法继续构建"
        return 1
    fi
    
    # 2. 基础配置
    IMAGE_NAME="fastapi-cython"
    IMAGE_TAG="latest"
    
    # 3. 检测当前环境
    local OS_TYPE=$(uname -s)
    local ARCH_TYPE=$(uname -m)
    
    print_message "当前环境: $OS_TYPE $ARCH_TYPE"
    
    # 4. 创建多平台构建器（如果不存在）
    print_message "设置多平台构建器..."
    if ! docker buildx ls | grep -q "multiarch-builder"; then
        docker buildx create --name multiarch-builder --driver docker-container --bootstrap
    fi
    docker buildx use multiarch-builder
    
    # 5. 备份原始 Dockerfile
    print_message "备份原始 Dockerfile..."
    cp Dockerfile Dockerfile.backup
    
    # 6. 分别构建各环境版本
    local success_count=0
    local total_count=0
    local built_images=()
    local current_env_image=""
    
    # 构建 Linux amd64 版本
    if docker images | grep -q "3.9-slim-linux"; then
        total_count=$((total_count + 1))
        print_message "构建 Linux x86_64 版本（amd64架构）..."
        
        # 创建 Linux 专用 Dockerfile
        sed 's|FROM.*|FROM crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-linux|' Dockerfile > Dockerfile.linux
        
        local linux_image="${IMAGE_NAME}:${IMAGE_TAG}-linux"
        
        # 使用 buildx 构建指定架构的镜像
        if docker buildx build \
            --platform linux/amd64 \
            -f Dockerfile.linux \
            -t "$linux_image" \
            --load \
            . ; then
            print_message "✅ Linux amd64 版本构建成功: $linux_image"
            success_count=$((success_count + 1))
            built_images+=("$linux_image")
            
            # 如果当前是Linux环境，标记为当前环境镜像
            if [[ "$OS_TYPE" == "Linux" ]]; then
                current_env_image="$linux_image"
            fi
        else
            print_warning "⚠️ Linux amd64 版本构建失败"
        fi
        
        rm -f Dockerfile.linux
    else
        print_warning "⚠️ Linux 基础镜像不可用，跳过构建"
    fi
    
    # 构建 Mac arm64 版本
    if docker images | grep -q "3.9-slim-maclocal"; then
        total_count=$((total_count + 1))
        print_message "构建 Mac M芯片 版本（arm64架构）..."
        
        # 创建 Mac 专用 Dockerfile
        sed 's|FROM.*|FROM crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-maclocal|' Dockerfile > Dockerfile.mac
        
        local mac_image="${IMAGE_NAME}:${IMAGE_TAG}-mac"
        
        # 使用 buildx 构建指定架构的镜像
        if docker buildx build \
            --platform linux/arm64 \
            -f Dockerfile.mac \
            -t "$mac_image" \
            --load \
            . ; then
            print_message "✅ Mac arm64 版本构建成功: $mac_image"
            success_count=$((success_count + 1))
            built_images+=("$mac_image")
            
            # 如果当前是Mac环境，标记为当前环境镜像
            if [[ "$OS_TYPE" == "Darwin" ]]; then
                current_env_image="$mac_image"
            fi
        else
            print_warning "⚠️ Mac arm64 版本构建失败"
        fi
        
        rm -f Dockerfile.mac
    else
        print_warning "⚠️ Mac 基础镜像不可用，跳过构建"
    fi
    
    # 7. 恢复原始 Dockerfile
    mv Dockerfile.backup Dockerfile
    
    # 8. 创建通用标签
    if [ "$success_count" -gt 0 ]; then
        # 如果有当前环境的镜像，使用它作为默认镜像
        if [ -n "$current_env_image" ]; then
            print_message "标记当前环境镜像为默认: $current_env_image -> ${IMAGE_NAME}:${IMAGE_TAG}"
            docker tag "$current_env_image" "${IMAGE_NAME}:${IMAGE_TAG}"
        else
            # 否则使用第一个成功构建的镜像
            print_message "标记第一个成功镜像为默认: ${built_images[0]} -> ${IMAGE_NAME}:${IMAGE_TAG}"
            docker tag "${built_images[0]}" "${IMAGE_NAME}:${IMAGE_TAG}"
        fi
    fi
    
    # 9. 构建结果汇总
    print_message "Docker镜像构建完成"
    print_message "成功构建: $success_count/$total_count 个版本"
    
    if [ "$success_count" -eq 0 ]; then
        print_error "所有版本构建失败"
        return 1
    fi
    
    print_message "构建的镜像:"
    for image in "${built_images[@]}"; do
        echo "  ✅ $image"
    done
    echo "  ✅ ${IMAGE_NAME}:${IMAGE_TAG} (默认)"
    
    return 0
}

# =====================================================
# 函数：加载环境变量
# =====================================================
load_env_vars() {
    if [ -f ".env" ]; then
        print_message "加载.env文件中的环境变量..."
        
        # 读取.env文件并构建docker run参数
        ENV_PARAMS=""
        while IFS='=' read -r key value; do
            # 跳过注释和空行
            if [[ $key =~ ^[[:space:]]*# ]] || [[ -z "$key" ]]; then
                continue
            fi
            
            # 移除前后空格
            key=$(echo "$key" | xargs)
            value=$(echo "$value" | xargs)
            
            # 移除值两边的引号
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
            
            if [ -n "$key" ] && [ -n "$value" ]; then
                ENV_PARAMS="$ENV_PARAMS -e $key=\"$value\""
            fi
        done < .env
        
        return 0
    else
        print_warning "未找到.env文件，将使用默认环境变量"
        return 1
    fi
}

# =====================================================
# 函数：测试Docker容器
# =====================================================
test_docker_container() {
    print_step "测试Docker容器..."
    
    IMAGE_NAME="fastapi-cython:latest"
    CONTAINER_NAME="fastapi-cython-test"
    TEST_PORT="8998"
    
    # 清理可能存在的测试容器
    print_message "清理之前的测试容器..."
    docker rm -f "$CONTAINER_NAME" >/dev/null 2>&1 || true
    
    # 检查镜像是否存在
    if ! docker images | grep -q "fastapi-cython"; then
        print_error "未找到fastapi-cython镜像，请先构建镜像"
        return 1
    fi
    
    print_message "启动测试容器..."
    
    # 尝试从.env文件加载环境变量
    if load_env_vars; then
        # 使用.env中的环境变量启动容器
        print_message "使用.env中的环境变量启动测试容器..."
        eval "docker run -d --name \"$CONTAINER_NAME\" \
            -p $TEST_PORT:8000 \
            $ENV_PARAMS \
            \"$IMAGE_NAME\""
    else
        # 使用默认测试环境变量
        print_message "使用默认测试环境变量启动测试容器..."
        docker run -d --name "$CONTAINER_NAME" \
            -p $TEST_PORT:8000 \
            -e SECRET_KEY=test_secret_key_for_cython_build \
            -e MYSQL_HOST=test_host \
            -e MYSQL_USER=test_user \
            -e MYSQL_PASSWORD=test_password \
            -e MYSQL_DB=test_db \
            -e ENABLE_DOCS=true \
            "$IMAGE_NAME"
    fi
    
    # 等待容器启动
    print_message "等待容器启动（10秒）..."
    sleep 10
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        print_message "✅ 容器启动成功"
        
        # 检查容器日志
        print_message "检查容器日志..."
        LOGS=$(docker logs "$CONTAINER_NAME" 2>&1 | tail -10)
        if echo "$LOGS" | grep -q "Uvicorn running"; then
            print_message "✅ FastAPI应用启动成功"
        else
            print_warning "⚠️ 应用可能未完全启动，日志："
            echo "$LOGS"
        fi
        
        # 尝试HTTP请求测试
        print_message "测试HTTP响应..."
        if command -v curl &> /dev/null; then
            if curl -s "http://localhost:$TEST_PORT/api/v1/health/hello" | grep -q "hello"; then
                print_message "✅ HTTP响应测试通过"
            else
                print_warning "⚠️ HTTP响应测试失败，但容器运行正常"
            fi
        else
            print_warning "⚠️ curl未安装，跳过HTTP测试"
        fi
        
        # 清理测试容器
        print_message "清理测试容器..."
        docker rm -f "$CONTAINER_NAME" >/dev/null 2>&1
        
        print_message "Docker容器测试通过 ✅"
        return 0
    else
        print_error "❌ 容器启动失败"
        
        # 显示容器日志
        print_message "容器日志："
        docker logs "$CONTAINER_NAME" 2>&1 || true
        
        # 清理失败的容器
        docker rm -f "$CONTAINER_NAME" >/dev/null 2>&1 || true
        return 1
    fi
}

# =====================================================
# 函数：推送镜像到阿里云ACR
# 根据构建策略推送单架构或多架构镜像
# =====================================================
push_to_registry() {
    print_message "开始推送镜像到阿里云ACR..."
    
    # 默认ACR配置
    local REGISTRY_HOST="crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com"
    local REGISTRY_NAMESPACE="bkonline"
    local REGISTRY_REPO="localm4"
    local REGISTRY_TAG="v6.0.3-cython"
    local LOCAL_IMAGE="fastapi-cython:latest"
    
    # 组合完整镜像地址
    local REGISTRY_IMAGE="$REGISTRY_HOST/$REGISTRY_NAMESPACE/$REGISTRY_REPO:$REGISTRY_TAG"
    print_message "目标镜像前缀: $REGISTRY_IMAGE"

    # 登录仓库
    print_message "登录阿里云容器镜像服务..."
    if ! echo "test_v123" | docker login "$REGISTRY_HOST" -u backkom96 --password-stdin >/dev/null 2>&1; then
        print_error "仓库登录失败"
        return 1
    fi

    # 检查本地镜像是否存在
    if ! docker images | grep -q "fastapi-cython.*latest"; then
        print_error "本地镜像 $LOCAL_IMAGE 不存在，请先构建镜像"
        return 1
    fi

    local pushed_images=()
    local push_success=0

    # 推送各个架构版本
    for suffix in "" "-linux" "-mac"; do
        local source_image="fastapi-cython:latest$suffix"
        local target_image="$REGISTRY_IMAGE$suffix"
        
        if docker images | grep -q "fastapi-cython.*latest$suffix"; then
            print_message "推送 $source_image -> $target_image"
            
            # 标记镜像
            docker tag "$source_image" "$target_image"
            
            # 推送镜像
            if docker push "$target_image"; then
                print_message "✅ $target_image 推送成功"
                pushed_images+=("$target_image")
                push_success=$((push_success + 1))
            else
                print_warning "⚠️ $target_image 推送失败"
            fi
        fi
    done

    # 推送结果汇总
    if [ "$push_success" -gt 0 ]; then
        print_message "🎉 镜像推送完成！"
        print_message "成功推送 $push_success 个镜像:"
        for image in "${pushed_images[@]}"; do
            echo "  ✅ $image"
        done
        
        print_message "部署命令示例:"
        echo "  docker pull ${pushed_images[0]}"
        echo "  docker run -d -p 8000:8000 ${pushed_images[0]}"
        
        return 0
    else
        print_error "所有镜像推送失败"
        return 1
    fi
}

# 在 push_to_registry 函数后添加新的直接构建推送函数
build_and_push_direct() {
    print_message "直接构建并推送镜像到阿里云ACR..."
    
    # 检查基础镜像
    if ! ensure_base_image; then
        print_error "基础镜像检查失败，无法继续构建"
        return 1
    fi
    
    # 检查是否已有编译结果
    SO_COUNT=$(find app -name "*.so" 2>/dev/null | wc -l)
    if [ "$SO_COUNT" -eq 0 ]; then
        print_warning "未找到编译结果，先执行基本构建..."
        basic_build
    fi
    
    # 默认ACR配置
    local REGISTRY_HOST="crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com"
    local REGISTRY_NAMESPACE="bkonline"
    local REGISTRY_REPO="localm4"
    local REGISTRY_TAG="v6.0.3-cython"
    
    # 登录仓库
    print_message "登录阿里云容器镜像服务..."
    if ! echo "test_v123" | docker login "$REGISTRY_HOST" -u backkom96 --password-stdin >/dev/null 2>&1; then
        print_error "仓库登录失败"
        return 1
    fi
    
    # 创建多平台构建器
    print_message "设置多平台构建器..."
    if ! docker buildx ls | grep -q "multiarch-builder"; then
        docker buildx create --name multiarch-builder --driver docker-container --bootstrap
    fi
    docker buildx use multiarch-builder
    
    local pushed_images=()
    
    # 构建并推送 Linux amd64 版本
    if docker images | grep -q "3.9-slim-linux"; then
        print_message "构建并推送 Linux x86_64 版本..."
        
        # 创建 Linux 专用 Dockerfile
        sed 's|FROM.*|FROM crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-linux|' Dockerfile > Dockerfile.linux
        
        local target_image="$REGISTRY_HOST/$REGISTRY_NAMESPACE/$REGISTRY_REPO:$REGISTRY_TAG-linux"
        
        if docker buildx build \
            --platform linux/amd64 \
            -f Dockerfile.linux \
            -t "$target_image" \
            --push \
            . ; then
            print_message "✅ Linux amd64 版本构建并推送成功: $target_image"
            pushed_images+=("$target_image")
        else
            print_warning "⚠️ Linux amd64 版本构建推送失败"
        fi
        
        rm -f Dockerfile.linux
    fi
    
    # 构建并推送 Mac arm64 版本
    if docker images | grep -q "3.9-slim-maclocal"; then
        print_message "构建并推送 Mac arm64 版本..."
        
        # 创建 Mac 专用 Dockerfile
        sed 's|FROM.*|FROM crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/bkonline/python:3.9-slim-maclocal|' Dockerfile > Dockerfile.mac
        
        local target_image="$REGISTRY_HOST/$REGISTRY_NAMESPACE/$REGISTRY_REPO:$REGISTRY_TAG-mac"
        
        if docker buildx build \
            --platform linux/arm64 \
            -f Dockerfile.mac \
            -t "$target_image" \
            --push \
            . ; then
            print_message "✅ Mac arm64 版本构建并推送成功: $target_image"
            pushed_images+=("$target_image")
        else
            print_warning "⚠️ Mac arm64 版本构建推送失败"
        fi
        
        rm -f Dockerfile.mac
    fi
    
    # 推送结果汇总
    if [ "${#pushed_images[@]}" -gt 0 ]; then
        print_message "🎉 直接构建推送完成！"
        print_message "成功推送 ${#pushed_images[@]} 个镜像:"
        for image in "${pushed_images[@]}"; do
            echo "  ✅ $image"
        done
        return 0
    else
        print_error "所有镜像构建推送失败"
        return 1
    fi
}

# =====================================================
# 函数：清理功能
# =====================================================
cleanup() {
    print_step "清理临时文件..."
    
    # 清理编译产物
    find . -name "*.c" -delete 2>/dev/null || true
    rm -rf build/ 2>/dev/null || true
    
    # 清理临时文件
    rm -f temp_*.py 2>/dev/null || true
    rm -f Dockerfile.* 2>/dev/null || true
    
    print_message "清理完成 ✅"
}

# =====================================================
# 各种清理函数
# =====================================================
clean_build_artifacts() {
    print_message "清理编译产物..."
    find app -name "*.so" -delete 2>/dev/null || true
    find app -name "*.c" -delete 2>/dev/null || true
    rm -rf build/ 2>/dev/null || true
    find app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    print_message "编译产物清理完成 ✅"
}

clean_docker_containers() {
    print_message "清理Docker容器..."
    docker rm -f $(docker ps -aq --filter "name=fastapi") 2>/dev/null || true
    print_message "Docker容器清理完成 ✅"
}

clean_docker_images() {
    print_message "清理Docker镜像..."
    docker rmi -f $(docker images -q "fastapi-cython") 2>/dev/null || true
    docker rmi -f $(docker images -q "*cython*") 2>/dev/null || true
    print_message "Docker镜像清理完成 ✅"
}

clean_docker_cache() {
    print_message "清理Docker构建缓存..."
    docker builder prune -f 2>/dev/null || true
    docker system prune -f 2>/dev/null || true
    print_message "Docker缓存清理完成 ✅"
}

clean_buildx() {
    print_message "清理Docker buildx构建器..."
    docker buildx rm multiarch-builder 2>/dev/null || true
    print_message "Buildx构建器清理完成 ✅"
}

clean_test_artifacts() {
    print_message "清理测试遗留物..."
    rm -f temp_*.py 2>/dev/null || true
    rm -f Dockerfile.* 2>/dev/null || true
    rm -rf backup_* 2>/dev/null || true
    print_message "测试遗留物清理完成 ✅"
}

clean_all() {
    print_message "执行全面清理..."
    clean_build_artifacts
    clean_docker_containers
    clean_docker_images
    clean_docker_cache
    clean_buildx
    clean_test_artifacts
    print_message "全面清理完成 ✅"
}

# =====================================================
# 函数：基本构建（仅编译和测试）
# =====================================================
basic_build() {
    print_message "开始基本构建流程..."
    
    check_requirements     # 检查环境
    create_backup         # 备份源码
    test_cython_build     # 编译测试
    test_import_modules   # 测试导入
    test_fastapi_startup  # 测试FastAPI启动
    
    print_message "🎉 基本构建完成！"
    print_message "编译结果已生成，可以进行下一步Docker构建"
}

# =====================================================
# 函数：Docker构建流程
# =====================================================
docker_build() {
    print_message "开始Docker镜像构建..."
    
    # 检查是否已有编译结果
    SO_COUNT=$(find app -name "*.so" 2>/dev/null | wc -l)
    if [ "$SO_COUNT" -eq 0 ]; then
        print_warning "未找到编译结果，先执行基本构建..."
        basic_build
    fi
    
    build_docker_image     # 构建镜像
    test_docker_container  # 测试容器
    
    print_message "🎉 Docker镜像构建完成！"
    print_message "镜像名称: fastapi-cython:latest"
}

# =====================================================
# 函数：推送流程
# =====================================================
push_flow() {
    print_message "开始推送流程..."
    
    # 检查是否有本地镜像
    if ! docker images | grep -q "fastapi-cython"; then
        print_warning "未找到本地镜像，先执行Docker构建..."
        docker_build
    fi
    
    push_to_registry      # 推送镜像
    
    print_message "🎉 推送流程完成！"
}

# =====================================================
# 函数：完整构建流程
# =====================================================
full_build() {
    print_message "开始完整构建流程..."
    
    check_requirements     # 检查环境
    create_backup         # 备份源码
    test_cython_build     # 编译测试
    test_import_modules   # 测试导入
    test_fastapi_startup  # 测试FastAPI启动
    build_docker_image    # 构建镜像
    test_docker_container # 测试容器
    cleanup              # 清理临时文件
    
    print_message "🎉 完整构建流程完成！"
    print_message "镜像名称: fastapi-cython:latest"
    print_message "启动命令: docker run -d -p 8000:8000 fastapi-cython:latest"
}

# =====================================================
# 函数：显示交互式菜单
# =====================================================
show_menu() {
    echo ""
    echo "请选择构建选项："
    echo "================================"
    echo "1. 基本构建（编译 + 测试导入 + 启动测试）"
    echo "2. 构建Docker镜像"
    echo "3. 推送到阿里云ACR"
    echo "4. 直接构建推送（推荐）"
    echo "5. 完整流程（编译 + Docker + 测试）"
    echo "6. 清理编译产物"
    echo "7. 清理Docker容器"
    echo "8. 清理Docker镜像"
    echo "9. 清理Docker构建缓存"
    echo "10. 清理临时构建器"
    echo "11. 清理测试遗留物"
    echo "12. 清理全部"
    echo "0. 退出"
    echo "================================"
    echo -n "请输入选项 [0-12]: "
}

# =====================================================
# 函数：显示使用说明
# =====================================================
show_usage() {
    echo ""
    echo "FastAPI Cython Docker 构建脚本"
    echo "================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  无参数        显示交互式菜单"
    echo "  --compile     仅执行编译和测试"
    echo "  --docker      构建Docker镜像"
    echo "  --push        推送到阿里云ACR"
    echo "  --build-push  直接构建并推送（推荐）"
    echo "  --full        执行完整构建流程"
    echo "  --clean       清理历史垃圾"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "交互式菜单选项:"
    echo "  1. 基本构建: 编译源码 + 测试导入 + FastAPI启动测试"
    echo "  2. Docker构建: 智能多/单架构构建 + 测试容器运行"
    echo "  3. ACR推送: 根据环境推送多架构或单架构镜像"
    echo "  4. 直接构建推送: 构建并推送多架构镜像（推荐）"
    echo "  5. 完整流程: 包含所有步骤的完整构建"
    echo "  6-11. 各种清理功能: 编译产物、容器、镜像、缓存等"
    echo ""
    echo "智能构建策略:"
    echo "  Mac环境: 构建linux/amd64,linux/arm64多架构镜像"
    echo "  Linux x86_64: 构建linux/amd64单架构镜像"
    echo "  Linux ARM64: 构建linux/arm64单架构镜像"
    echo ""
    echo "示例:"
    echo "  $0                    # 交互式选择"
    echo "  $0 --compile         # 仅编译测试"
    echo "  $0 --docker          # 构建镜像"
    echo "  $0 --push            # 推送镜像"
    echo "  $0 --build-push      # 直接构建推送（推荐）"
    echo "  $0 --full            # 完整流程"
    echo "  $0 --clean           # 清理垃圾"
    echo ""
}

# =====================================================
# 主程序入口
# =====================================================
main() {
    # 检查参数
    case "${1:-}" in
        --compile)
            print_header
            basic_build
            ;;
        --docker)
            print_header
            docker_build
            ;;
        --push)
            print_header
            push_flow
            ;;
        --build-push)
            print_header
            build_and_push_direct
            ;;
        --full)
            print_header
            full_build
            ;;
        --clean)
            print_header
            clean_all
            ;;
        --help)
            show_usage
            ;;
        "")
            # 交互式菜单
            print_header
            while true; do
                show_menu
                read -r choice
                
                case $choice in
                    1)
                        echo ""
                        basic_build
                        ;;
                    2)
                        echo ""
                        docker_build
                        ;;
                    3)
                        echo ""
                        push_flow
                        ;;
                    4)
                        echo ""
                        build_and_push_direct
                        ;;
                    5)
                        echo ""
                        full_build
                        ;;
                    6)
                        echo ""
                        clean_build_artifacts
                        ;;
                    7)
                        echo ""
                        clean_docker_containers
                        ;;
                    8)
                        echo ""
                        clean_docker_images
                        ;;
                    9)
                        echo ""
                        clean_docker_cache
                        ;;
                    10)
                        echo ""
                        clean_buildx
                        ;;
                    11)
                        echo ""
                        clean_test_artifacts
                        ;;
                    12)
                        echo ""
                        clean_all
                        ;;
                    0)
                        print_message "退出构建脚本"
                        exit 0
                        ;;
                    *)
                        print_error "无效选项，请重新选择"
                        ;;
                esac
                
                echo ""
                echo "按回车键继续..."
                read -r
            done
            ;;
        *)
            print_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
