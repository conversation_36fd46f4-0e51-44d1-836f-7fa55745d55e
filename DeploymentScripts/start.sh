#!/bin/bash

# =============================================
# FastAPI 用户健康分析系统 - 启动脚本
# 创建一个项目目录，进入目录 执行start.sh 启动
# chmod +x start.sh
# 交互式菜单系统，支持以下功能：
#   1. 启动环境（统一使用 docker-compose.yml）
#   2. 停止服务
#   3. 重启服务
#   4. 查看日志
#   5. 查看服务状态
#   6. 清理项目资源（二级菜单）
#   7. 登录阿里云镜像仓库
#   8. 拉取镜像
#   9. 查看帮助
#   0. 退出
#
# 环境变量：
#   请在 .env 文件中配置相关信息，脚本会自动检测并验证 .env 文件
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    clear
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  FastAPI 用户健康分析系统${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 改进的环境变量读取和验证
check_env_file() {
    if [ ! -f .env ]; then
        print_error ".env 文件不存在，请先配置好 .env 文件再启动！"
        exit 1
    fi
    
    print_message "验证 .env 文件格式..."
    local line_num=0
    local has_error=false
    
    while IFS= read -r line; do
        line_num=$((line_num + 1))
        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # 检查是否包含等号
        if [[ ! "$line" =~ = ]]; then
            print_error ".env 文件第 $line_num 行格式错误: $line"
            has_error=true
        fi
    done < .env
    
    if [ "$has_error" = true ]; then
        print_error ".env 文件格式验证失败，请检查文件格式"
        exit 1
    fi
    
    # 加载环境变量
    source .env
    
    # 检查必要的环境变量
    local required_vars=("MYSQL_HOST" "MYSQL_USER" "MYSQL_PASSWORD" "MYSQL_DB" "WEB_IMAGE" "SERVER_NAME")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "必要的环境变量 $var 未设置"
            exit 1
        fi
    done
    
    print_message ".env 文件验证通过"
}

create_directories() {
    print_message "创建必要的目录..."
    mkdir -p logs
    mkdir -p temp_uploads
    mkdir -p nginx/ssl

    # 自动适配证书和私钥文件
    CERT_SRC=""
    KEY_SRC=""
    # 优先顺序：cert.pem/key.pem
    if [ -f cert.pem ]; then CERT_SRC="cert.pem"; fi
    if [ -f key.pem ]; then KEY_SRC="key.pem"; fi
    # fullchain.pem/privkey.pem
    if [ -z "$CERT_SRC" ] && [ -f fullchain.pem ]; then CERT_SRC="fullchain.pem"; fi
    if [ -z "$KEY_SRC" ] && [ -f privkey.pem ]; then KEY_SRC="privkey.pem"; fi
    # 其他 .pem/.crt/.key
    if [ -z "$CERT_SRC" ]; then CERT_SRC=$(ls *.pem *.crt 2>/dev/null | head -n1); fi
    if [ -z "$KEY_SRC" ]; then KEY_SRC=$(ls *.key 2>/dev/null | head -n1); fi

    # 移动并重命名
    if [ -n "$CERT_SRC" ] && [ -n "$KEY_SRC" ]; then
        mv -f "$CERT_SRC" nginx/ssl/cert.pem
        mv -f "$KEY_SRC" nginx/ssl/key.pem
        print_message "已自动移动并重命名 $CERT_SRC 和 $KEY_SRC 到 nginx/ssl/"
    fi

    # 检查 nginx/ssl 下是否有证书
    if [ ! -f nginx/ssl/cert.pem ] || [ ! -f nginx/ssl/key.pem ]; then
        print_error "未检测到 SSL 证书文件 (nginx/ssl/cert.pem, nginx/ssl/key.pem)，请将证书(.pem/.crt)和私钥(.key)放到根目录或 nginx/ssl/ 目录。"
        exit 1
    fi
}

# 动态生成nginx配置，使用环境变量中的域名
generate_nginx_conf() {
    print_message "生成nginx/nginx.conf 配置文件..."
    cat > nginx/nginx.conf <<EOF
events {
    worker_connections 1024;
}

http {
    upstream fastapi_backend {
        server web:8000;
    }

    server {
        listen 80;
        server_name ${SERVER_NAME};
        return 301 https://\$host\$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name ${SERVER_NAME};
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        client_max_body_size 100M;
        
        location / {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;

            proxy_connect_timeout       300s;
            proxy_send_timeout          300s;
            proxy_read_timeout          300s;
            send_timeout                300s;

            proxy_buffering             off;
            proxy_http_version          1.1;
        }
    }

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/mime.types;
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    sendfile on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
}
EOF
    print_message "nginx配置文件已生成，使用域名: ${SERVER_NAME}"
}

# 检查容器是否存在
check_container_exists() {
    local container_name="$1"
    if docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        return 0
    else
        return 1
    fi
}

# 检查镜像是否存在
check_image_exists() {
    local image_name="$1"
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image_name}$"; then
        return 0
    else
        return 1
    fi
}

# 登录阿里云镜像仓库
login_aliyun_registry() {
    print_message "登录阿里云镜像仓库..."
    if [ -z "$REGISTRY_PASSWORD" ]; then
        print_error "环境变量 REGISTRY_PASSWORD 未设置，无法登录镜像仓库"
        return 1
    fi
    
    echo "$REGISTRY_PASSWORD" | docker login --username=backkom96 --password-stdin crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com
    if [ $? -eq 0 ]; then
        print_message "阿里云镜像仓库登录成功"
        return 0
    else
        print_error "阿里云镜像仓库登录失败"
        return 1
    fi
}

# 拉取镜像
pull_image() {
    print_message "检查镜像: $WEB_IMAGE"
    if check_image_exists "$WEB_IMAGE"; then
        print_message "镜像 $WEB_IMAGE 已存在"
        return 0
    fi
    
    print_message "镜像不存在，开始拉取: $WEB_IMAGE"
    if docker pull "$WEB_IMAGE"; then
        print_message "镜像拉取成功: $WEB_IMAGE"
        return 0
    else
        print_error "镜像拉取失败: $WEB_IMAGE"
        print_message "尝试登录阿里云镜像仓库后重新拉取..."
        if login_aliyun_registry && docker pull "$WEB_IMAGE"; then
            print_message "镜像拉取成功: $WEB_IMAGE"
            return 0
        else
            print_error "镜像拉取失败，请检查网络连接和镜像地址"
            return 1
        fi
    fi
}

# 启动环境（支持重复启动）
start_environment() {
    create_directories
    generate_nginx_conf
    
    # 检查并拉取镜像
    if ! pull_image; then
        print_error "镜像准备失败，无法启动环境"
        return 1
    fi
    
    print_message "启动环境..."
    
    # 检查容器是否已存在并运行
    if check_container_exists "fastapi-health-system-prod"; then
        if docker ps --format "table {{.Names}}" | grep -q "^fastapi-health-system-prod$"; then
            print_warning "容器已在运行中，将重启服务..."
            docker compose restart
        else
            print_message "容器已存在但未运行，启动现有容器..."
            docker compose up -d
        fi
    else
        print_message "创建并启动新容器..."
        docker compose up --build -d
    fi
    
    print_message "环境启动完成！"
    print_message "访问地址: https://${SERVER_NAME}"
    print_message "HTTP访问: http://${SERVER_NAME} (将自动跳转到HTTPS)"
    print_message "查看日志: docker compose logs -f web"
}

stop_services() {
    print_message "停止所有服务..."
    if [ -f "docker-compose.yml" ]; then
        docker compose down
        print_message "服务已停止"
    else
        print_error "未找到 docker-compose.yml 配置文件"
        return 1
    fi
}

restart_services() {
    print_message "重启服务..."
    generate_nginx_conf
    if [ -f "docker-compose.yml" ]; then
        docker compose restart
        print_message "服务已重启"
    else
        print_error "未找到 docker-compose.yml 配置文件"
        return 1
    fi
}

show_logs() {
    print_message "显示服务日志..."
    if [ -f "docker-compose.yml" ]; then
        docker compose logs -f
    else
        print_error "未找到 docker-compose.yml 配置文件"
        return 1
    fi
}

show_status() {
    print_message "服务状态："
    if [ -f "docker-compose.yml" ]; then
        docker compose ps
    else
        print_error "未找到 docker-compose.yml 配置文件"
        return 1
    fi
}

# 清理本项目相关的Docker容器
cleanup_project_containers() {
    print_message "检查本项目相关的容器..."
    
    local containers=$(docker ps -a --filter "name=fastapi-health-system-prod" --filter "name=fastapi-nginx-prod" --format "{{.Names}}" 2>/dev/null)
    
    if [ -z "$containers" ]; then
        print_message "未找到本项目相关的容器"
        return 0
    fi
    
    echo -e "${YELLOW}将要删除以下容器：${NC}"
    echo "$containers"
    echo ""
    read -p "确定要删除这些容器吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "停止并删除项目容器..."
        echo "$containers" | while read -r container; do
            if [ -n "$container" ]; then
                docker stop "$container" 2>/dev/null || true
                docker rm -f "$container" 2>/dev/null || true
                print_message "已删除容器: $container"
            fi
        done
        print_message "项目容器清理完成"
    else
        print_message "操作已取消"
    fi
}

# 清理本项目相关的Docker镜像
cleanup_project_images() {
    print_message "检查本项目相关的镜像..."
    
    # 获取WEB_IMAGE和相关镜像
    local images=""
    if [ -n "$WEB_IMAGE" ]; then
        images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(${WEB_IMAGE%:*}|localm4)" 2>/dev/null || true)
    fi
    
    # 添加nginx镜像（如果是项目专用的）
    local nginx_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "nginx:alpine" 2>/dev/null || true)
    
    if [ -z "$images" ] && [ -z "$nginx_images" ]; then
        print_message "未找到本项目相关的镜像"
        return 0
    fi
    
    echo -e "${YELLOW}将要删除以下镜像：${NC}"
    [ -n "$images" ] && echo "$images"
    [ -n "$nginx_images" ] && echo "$nginx_images"
    echo ""
    read -p "确定要删除这些镜像吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "删除项目镜像..."
        if [ -n "$images" ]; then
            echo "$images" | while read -r image; do
                if [ -n "$image" ]; then
                    docker rmi -f "$image" 2>/dev/null || true
                    print_message "已删除镜像: $image"
                fi
            done
        fi
        print_message "项目镜像清理完成"
    else
        print_message "操作已取消"
    fi
}

# 清理Docker构建缓存
cleanup_build_cache() {
    print_message "检查Docker构建缓存..."
    
    local cache_size=$(docker system df --format "{{.BuildCache}}" 2>/dev/null || echo "0B")
    
    echo -e "${YELLOW}当前构建缓存大小: $cache_size${NC}"
    echo ""
    read -p "确定要清理构建缓存吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "清理构建缓存..."
        docker builder prune -af 2>/dev/null || print_message "构建缓存清理完成"
        print_message "构建缓存已清理"
    else
        print_message "操作已取消"
    fi
}

# 清理临时构建器
cleanup_buildx_builders() {
    print_message "检查buildx构建器..."
    
    local builders=$(docker buildx ls --format "{{.Name}}" | grep -v "default" 2>/dev/null || true)
    
    if [ -z "$builders" ]; then
        print_message "未找到需要清理的buildx构建器"
        return 0
    fi
    
    echo -e "${YELLOW}将要删除以下buildx构建器：${NC}"
    echo "$builders"
    echo ""
    read -p "确定要删除这些构建器吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "清理buildx构建器..."
        echo "$builders" | while read -r builder; do
            if [ -n "$builder" ]; then
                docker buildx rm "$builder" 2>/dev/null || true
                print_message "已删除构建器: $builder"
            fi
        done
        print_message "buildx构建器清理完成"
    else
        print_message "操作已取消"
    fi
}

# 清理本项目测试遗留物
cleanup_project_files() {
    print_message "检查项目测试遗留文件..."
    
    local temp_files=""
    local log_files=""
    
    # 检查temp_uploads目录
    if [ -d "temp_uploads" ]; then
        temp_files=$(find temp_uploads -type f -mtime +1 2>/dev/null | wc -l)
    fi
    
    # 检查logs目录中的旧文件
    if [ -d "logs" ]; then
        log_files=$(find logs -type f -mtime +7 2>/dev/null | wc -l)
    fi
    
    if [ "$temp_files" -eq 0 ] && [ "$log_files" -eq 0 ]; then
        print_message "未找到需要清理的项目文件"
        return 0
    fi
    
    echo -e "${YELLOW}将要清理：${NC}"
    [ "$temp_files" -gt 0 ] && echo "  - temp_uploads目录中超过1天的文件: $temp_files 个"
    [ "$log_files" -gt 0 ] && echo "  - logs目录中超过7天的文件: $log_files 个"
    echo ""
    read -p "确定要清理这些文件吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "清理项目文件..."
        
        if [ "$temp_files" -gt 0 ]; then
            find temp_uploads -type f -mtime +1 -delete 2>/dev/null || true
            print_message "已清理temp_uploads目录中的旧文件"
        fi
        
        if [ "$log_files" -gt 0 ]; then
            find logs -type f -mtime +7 -delete 2>/dev/null || true
            print_message "已清理logs目录中的旧文件"
        fi
        
        print_message "项目文件清理完成"
    else
        print_message "操作已取消"
    fi
}

# 清理所有项目相关资源
cleanup_all_project_resources() {
    print_warning "警告：此操作将清理所有项目相关的Docker资源和文件！"
    echo ""
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "开始清理所有项目资源..."
        cleanup_project_containers
        cleanup_project_images
        cleanup_build_cache
        cleanup_buildx_builders
        cleanup_project_files
        print_message "所有项目资源清理完成！"
    else
        print_message "操作已取消"
    fi
}

# 清理菜单显示
show_cleanup_menu() {
    echo -e "${CYAN}项目资源清理菜单：${NC}"
    echo "  1. 清理项目容器"
    echo "  2. 清理项目镜像"
    echo "  3. 清理构建缓存"
    echo "  4. 清理临时构建器"
    echo "  5. 清理项目文件"
    echo "  6. 清理全部项目资源"
    echo "  0. 返回主菜单"
    echo ""
    echo -n "请输入选项 [0-6]: "
}

# 清理菜单循环
cleanup_menu() {
    while true; do
        print_header
        show_cleanup_menu
        read -r choice
        echo ""
        
        case $choice in
            1)
                cleanup_project_containers
                ;;
            2)
                cleanup_project_images
                ;;
            3)
                cleanup_build_cache
                ;;
            4)
                cleanup_buildx_builders
                ;;
            5)
                cleanup_project_files
                ;;
            6)
                cleanup_all_project_resources
                ;;
            0)
                return 0
                ;;
            *)
                print_error "无效选项，请输入 0-6"
                ;;
        esac
        
        echo ""
        echo -e "${YELLOW}按任意键继续...${NC}"
        read -n 1 -s
    done
}

show_help() {
    echo -e "${CYAN} 用户健康分析系统 - 帮助信息${NC}"
    echo ""
    echo "功能说明："
    echo "  1. 启动环境    - 创建并启动所有服务容器"
    echo "  2. 停止服务    - 停止所有运行中的服务"
    echo "  3. 重启服务    - 重启所有服务"
    echo "  4. 查看日志    - 实时查看服务日志"
    echo "  5. 查看状态    - 显示所有服务的运行状态"
    echo "  6. 清理环境    - 完全清理 Docker 环境"
    echo "  7. 登录仓库    - 登录阿里云镜像仓库"
    echo "  8. 拉取镜像    - 手动拉取最新镜像"
    echo "  9. 查看帮助    - 显示此帮助信息"
    echo "  0. 退出        - 退出程序"
    echo ""
    echo "环境要求："
    echo "  - Docker 和 Docker Compose 已安装"
    echo "  - .env 文件已正确配置"
    echo "  - SSL 证书文件已准备"
    echo ""
}

# 交互式菜单
show_menu() {
    echo -e "${CYAN}请选择操作：${NC}"
    echo "  1. 启动环境"
    echo "  2. 停止服务"
    echo "  3. 重启服务"
    echo "  4. 查看日志"
    echo "  5. 查看状态"
    echo "  6. 清理项目资源"
    echo "  7. 登录阿里云镜像仓库"
    echo "  8. 拉取镜像"
    echo "  9. 查看帮助"
    echo "  0. 退出"
    echo ""
    echo -n "请输入选项 [0-9]: "
}

# 主菜单循环
main_menu() {
    while true; do
        print_header
        show_menu
        read -r choice
        echo ""
        
        case $choice in
            1)
                start_environment
                ;;
            2)
                stop_services
                ;;
            3)
                restart_services
                ;;
            4)
                show_logs
                ;;
            5)
                show_status
                ;;
            6)
                cleanup_menu
                ;;
            7)
                login_aliyun_registry
                ;;
            8)
                pull_image
                ;;
            9)
                show_help
                ;;
            0)
                print_message "退出程序"
                exit 0
                ;;
            *)
                print_error "无效选项，请输入 0-9"
                ;;
        esac
        
        echo ""
        echo -e "${YELLOW}按任意键继续...${NC}"
        read -n 1 -s
    done
}

# 主函数
main() {
    check_docker
    check_env_file
    main_menu
}

# 如果有命令行参数，保持兼容性
if [ $# -gt 0 ]; then
    case "$1" in
        "start"|"dev"|"prod")
            check_env_file
            start_environment
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            check_env_file
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup_docker
            ;;
        "login")
            check_env_file
            login_aliyun_registry
            ;;
        "pull")
            check_env_file
            pull_image
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
else
    main
fi
