# 用户健康分析系统

用户健康数据分析系统，支持用户注册登录、健康数据管理、文件上传等功能。系统采用 Docker 容器化部署，支持 HTTPS 访问和阿里云 OSS 存储。

## 🚀 功能特性

- **用户管理**：用户注册、登录、JWT 认证
- **健康数据分析**：用户健康数据录入和分析
- **文件上传**：支持阿里云 OSS 存储
- **API 文档**：自动生成的 Swagger/OpenAPI 文档
- **容器化部署**：Docker + Docker Compose 一键部署
- **HTTPS 支持**：Nginx 反向代理 + SSL 证书
- **数据库**：MySQL 数据存储
- **日志管理**：完整的应用日志记录

## 📋 系统要求

- Docker >= 20.0
- Docker Compose >= 2.0
- SSL 证书文件（用于 HTTPS）
- 阿里云 OSS 账号（可选，用于文件存储）

## 🛠️ 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd fastapi-health-system
```

### 2. 配置环境变量

创建 `.env` 文件并配置以下变量：

```bash
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DB=fastapi_users

# JWT 配置
SECRET_KEY=your_secret_key_for_jwt
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 应用配置
APP_PORT=8000
SERVER_NAME=your-domain.com
ENABLE_DOCS=false

# Docker 镜像配置
WEB_IMAGE=crpi-i4vze864zm9axbyn.cn-beijing.personal.cr.aliyuncs.com/fastapi-health/web:latest

# 阿里云镜像仓库（可选）
REGISTRY_PASSWORD=your_registry_password

# 阿里云 OSS 配置（可选）
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_REGION=cn-beijing
OSS_BUCKET=your_bucket_name
OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
```

### 3. 准备 SSL 证书

将 SSL 证书文件放在项目根目录：
- `cert.pem` 或 `fullchain.pem`（证书文件）
- `key.pem` 或 `privkey.pem`（私钥文件）

脚本会自动检测并移动到 `nginx/ssl/` 目录。

### 4. 启动系统

```bash
chmod +x start.sh
./start.sh
```

系统将显示交互式菜单，选择 `1. 启动环境` 即可。

## 📖 使用说明

### 交互式菜单

运行 `./start.sh` 后会显示以下菜单选项：

```
请选择操作：
  1. 启动环境
  2. 停止服务
  3. 重启服务
  4. 查看日志
  5. 查看状态
  6. 清理Docker环境
  7. 登录阿里云镜像仓库
  8. 拉取镜像
  9. 查看帮助
  0. 退出
```

### 命令行模式

也支持直接使用命令行参数：

```bash
./start.sh start    # 启动环境
./start.sh stop     # 停止服务
./start.sh restart  # 重启服务
./start.sh logs     # 查看日志
./start.sh status   # 查看状态
./start.sh cleanup  # 清理Docker环境
./start.sh login    # 登录阿里云镜像仓库
./start.sh pull     # 拉取镜像
./start.sh help     # 查看帮助
```

### 访问系统

启动成功后，可通过以下方式访问：

- **HTTPS 访问**：`https://your-domain.com`
- **HTTP 访问**：`http://your-domain.com`（自动跳转到 HTTPS）
- **API 文档**：`https://your-domain.com/docs`（需要设置 `ENABLE_DOCS=true`）

## 🏗️ 项目结构

```
fastapi-health-system/
├── start.sh                 # 启动脚本
├── docker-compose.yml       # Docker Compose 配置
├── .env                     # 环境变量配置
├── nginx/                   # Nginx 配置
│   ├── nginx.conf          # 自动生成的 Nginx 配置
│   └── ssl/                # SSL 证书目录
├── logs/                   # 应用日志目录
├── temp_uploads/           # 临时文件上传目录
└── README.md              # 项目说明文档
```

## 🔧 配置说明

### 环境变量详解

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `MYSQL_HOST` | MySQL 主机地址 | mysql | ✅ |
| `MYSQL_PORT` | MySQL 端口 | 3306 | ❌ |
| `MYSQL_USER` | MySQL 用户名 | root | ✅ |
| `MYSQL_PASSWORD` | MySQL 密码 | - | ✅ |
| `MYSQL_DB` | 数据库名称 | fastapi_users | ✅ |
| `SECRET_KEY` | JWT 密钥 | - | ✅ |
| `SERVER_NAME` | 服务器域名 | - | ✅ |
| `WEB_IMAGE` | Docker 镜像地址 | - | ✅ |
| `APP_PORT` | 应用端口 | 8000 | ❌ |
| `ENABLE_DOCS` | 启用 API 文档 | false | ❌ |
| `REGISTRY_PASSWORD` | 镜像仓库密码 | - | ❌ |

### Docker 服务

系统包含以下 Docker 服务：

- **web**：FastAPI 应用服务
  - 端口：8000
  - 健康检查：`/api/v1/health/hello`
  - 工作进程：4 个

- **nginx**：反向代理服务
  - 端口：80（HTTP）、443（HTTPS）
  - SSL 终止
  - 自动 HTTP 到 HTTPS 重定向

## 🔍 监控和日志

### 查看日志

```bash
# 查看所有服务日志
./start.sh logs

# 或使用 Docker Compose
docker compose logs -f

# 查看特定服务日志
docker compose logs -f web
docker compose logs -f nginx
```

### 健康检查

系统内置健康检查端点：
- **检查地址**：`http://localhost:8000/api/v1/health/hello`
- **检查间隔**：30 秒
- **超时时间**：10 秒
- **重试次数**：3 次

### 服务状态

```bash
# 查看服务状态
./start.sh status

# 或使用 Docker Compose
docker compose ps
```

## 🚨 故障排除

### 常见问题

1. **SSL 证书问题**
   ```bash
   # 检查证书文件是否存在
   ls -la nginx/ssl/
   
   # 确保证书文件权限正确
   chmod 644 nginx/ssl/cert.pem
   chmod 600 nginx/ssl/key.pem
   ```

2. **镜像拉取失败**
   ```bash
   # 手动登录阿里云镜像仓库
   ./start.sh login
   
   # 手动拉取镜像
   ./start.sh pull
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :80
   netstat -tlnp | grep :443
   
   # 停止冲突的服务
   sudo systemctl stop apache2  # 如果有 Apache
   sudo systemctl stop nginx    # 如果有系统 Nginx
   ```

4. **环境变量问题**
   ```bash
   # 验证 .env 文件格式
   cat .env | grep -v '^#' | grep -v '^$'
   
   # 检查必需变量
   source .env && echo $MYSQL_HOST $WEB_IMAGE $SERVER_NAME
   ```

### 日志位置

- **应用日志**：`./logs/`
- **Nginx 日志**：`./nginx/logs/`
- **Docker 日志**：`docker compose logs`

## 🔒 安全注意事项

1. **环境变量安全**
   - 不要将 `.env` 文件提交到版本控制
   - 使用强密码和复杂的 JWT 密钥
   - 定期轮换密钥和密码

2. **SSL 证书**
   - 使用有效的 SSL 证书
   - 定期更新证书
   - 保护私钥文件安全

3. **网络安全**
   - 配置防火墙规则
   - 限制数据库访问
   - 使用 HTTPS 强制访问

## 📝 开发说明

### 本地开发

如需进行本地开发，可以：

1. 修改 `ENABLE_DOCS=true` 启用 API 文档
2. 使用开发模式启动：`docker compose up --build`
3. 挂载代码目录进行实时开发

### API 文档

启用 API 文档后，可访问：
- **Swagger UI**：`https://your-domain.com/docs`
- **ReDoc**：`https://your-domain.com/redoc`

## 📄 许可证

[添加你的许可证信息]

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请联系：[添加联系方式]