version: "4"

services:
  web:
    image: ${WEB_IMAGE}
    container_name: fastapi-health-system-prod
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      - MYSQL_HOST=${MYSQL_HOST:-mysql}
      - MYSQL_PORT=${MYSQL_PORT:-3306}
      - MYSQL_USER=${MYSQL_USER:-root}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-password}
      - MYSQL_DB=${MYSQL_DB:-fastapi_users}
      - SECRET_KEY=${SECRET_KEY:-your_secret_key_for_jwt}
      - ALGORITHM=${ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-1440}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
      - OSS_REGION=${OSS_REGION:-cn-beijing}
      - OSS_BUCKET=${OSS_BUCKET}
      - OSS_ENDPOINT=${OSS_ENDPOINT:-oss-cn-beijing.aliyuncs.com}
      - ENABLE_DOCS=${ENABLE_DOCS:-false}
      - APP_PORT=${APP_PORT:-8000}
      - TZ=Asia/Shanghai
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1", "--log-level", "info"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/hello"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always
    volumes:
      - ./logs:/app/logs
      - ./temp_uploads:/app/temp_uploads
    networks:
      - fastapi-network

  nginx:
    image: nginx:alpine
    container_name: fastapi-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - web
    restart: always
    networks:
      - fastapi-network

networks:
  fastapi-network:
    driver: bridge