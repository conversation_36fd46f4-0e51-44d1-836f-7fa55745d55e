# 健康检测系统 - 软件著作权代码功能介绍

## 项目基本信息

**项目名称：** 健康检测系统
**项目描述：** 基于FastAPI的视频健康检测分析系统
**项目路径：** /Users/<USER>/PycharmProjects/FastAPI_v4/VideoProject
**文档生成时间：** 2025年07月23日 23:01:23

## 系统架构说明

本系统采用FastAPI框架构建，基于Python 3.9开发，采用分层架构设计：
- API接口层：处理HTTP请求响应
- 业务逻辑层：实现核心业务功能
- 数据访问层：封装数据库操作
- 数据模型层：定义数据结构

## 主要功能模块及代码实现

### API接口功能

#### 1. 用户注册功能

**接口地址：** `POST /api/v1/users/register`
**代码文件：** `app/api/v1/user.py`
**代码行数：** 18-50
**功能描述：** 用户注册接口，支持邮箱注册，自动生成JWT token

**核心功能特性：**
- 邮箱唯一性验证
- 密码加密存储
- 自动生成访问令牌
- 用户信息入库

**代码实现：**

```python
@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate):
    """用户注册接口"""
    db = get_db_session()
    try:
        # 检查用户是否已存在
        if crud_user.get_user_by_email(db, user.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 创建用户
        db_user = crud_user.create_user(db, user)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户创建失败"
            )
        
        # 生成token
        token_data = {"sub": str(db_user.uid), "email": db_user.email}
        token = create_access_token(data=token_data)
        
        # 更新用户token
        crud_user.update_user_token(db, db_user.uid, token)
        
        return UserResponse(
            uid=db_user.uid,
            name=db_user.name,
            email=db_user.email,
            token=token
        )
```

#### 2. 用户登录功能

**接口地址：** `POST /api/v1/users/login`
**代码文件：** `app/api/v1/user.py`
**代码行数：** 64-114
**功能描述：** 用户登录接口，支持邮箱密码登录，包含防暴力破解机制

**核心功能特性：**
- 邮箱密码验证
- 登录频率限制
- JWT token生成
- 登录状态管理

**代码实现：**

```python
@router.post("/login", response_model=UserResponse)
async def login_user(user_credentials: UserLogin):
    """用户登录接口"""
    db = get_db_session()
    try:
        # 检查暴力破解防护
        is_allowed, remaining_attempts = login_rate_limiter.is_allowed(user_credentials.email)
        if not is_allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"登录尝试次数过多，请{login_rate_limiter.window_minutes}分钟后再试"
            )
        
        # 验证用户
        user = crud_user.authenticate_user(db, user_credentials.email, user_credentials.password)
        if not user:
            # 记录失败的登录尝试
            login_rate_limiter.record_attempt(user_credentials.email)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"邮箱或密码错误，剩余尝试次数: {remaining_attempts - 1}"
            )
        
        # 登录成功，重置尝试记录
        login_rate_limiter.reset_attempts(user_credentials.email)
        
        # 生成新token
        token_data = {"sub": str(user.uid), "email": user.email}
        token = create_access_token(data=token_data)
        
        # 更新用户token
        crud_user.update_user_token(db, user.uid, token)
        
        return UserResponse(
            uid=user.uid,
            name=user.name,
            email=user.email,
            token=token
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"登录失败: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
    finally:
        close_db_session(db)
```

#### 3. 用户信息获取功能

**接口地址：** `GET /api/v1/users/me`
**代码文件：** `app/api/v1/user.py`
**代码行数：** 116-138
**功能描述：** 获取当前登录用户信息，需要token认证

**核心功能特性：**
- Token身份验证
- 用户信息查询
- 权限验证
- 数据返回格式化

**代码实现：**

```python
@router.get("/me", response_model=UserResponse)
async def get_current_user_info(request: Request):
    """获取当前用户信息 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        user = crud_user.get_user_by_uid(db, int(current_user["sub"]))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse(
            uid=user.uid,
            name=user.name,
            email=user.email,
            token=user.token
        )
    finally:
        close_db_session(db)
```

#### 4. 家庭成员管理功能

**接口地址：** `POST /api/v1/users/addfamily`
**代码文件：** `app/api/v1/user.py`
**代码行数：** 140-187
**功能描述：** 添加家庭成员信息，支持多成员健康管理

**核心功能特性：**
- 家庭成员信息录入
- 关系类型管理
- 权限控制验证
- 成员数据存储

**代码实现：**

```python
@router.post("/addfamily", response_model=FamilyMemberResponse, status_code=status.HTTP_201_CREATED)
async def add_family_member(
    family_member: FamilyMemberCreate,
    request: Request
):
    """添加家庭成员接口 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    # 验证用户权限：确保当前用户只能为自己添加家庭成员
    current_user_id = int(current_user.get("sub", 0))
    if family_member.uid != current_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权为其他用户添加家庭成员"
        )
    
    db = get_db_session()
    try:
        # 创建家庭成员
        db_family_member = crud_family_member.create_family_member(db, family_member)
        if not db_family_member:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="家庭成员创建失败"
            )
        
        return FamilyMemberResponse(
            fuid=db_family_member.fuid,
            uid=db_family_member.uid,
            relationship=db_family_member.relationship,
            name=db_family_member.name,
            gender=db_family_member.gender,
            height=db_family_member.height,
            weight=db_family_member.weight,
            birth_year=db_family_member.birth_year,
            avatar_url=db_family_member.avatar_url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加家庭成员失败: {str(e)}"
        )
    finally:
        close_db_session(db)
```

#### 5. 家庭成员列表功能

**接口地址：** `GET /api/v1/users/{uid}/familylist`
**代码文件：** `app/api/v1/user.py`
**代码行数：** 189-220
**功能描述：** 获取用户的家庭成员列表信息

**核心功能特性：**
- 成员列表查询
- 数据权限控制
- 信息格式化输出
- 关联数据获取

**代码实现：**

```python
@router.get("/{uid}/familylist", response_model=List[FamilyMemberResponse])
async def get_family_list(
    uid: int,
    request: Request
):
    """获取家庭成员列表接口 - 需要token认证"""
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    # 验证用户权限：确保当前用户只能查看自己的家庭成员
    current_user_id = int(current_user.get("sub", 0))
    if uid != current_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权查看其他用户的家庭成员"
        )
    
    db = get_db_session()
    try:
        family_members = crud_family_member.get_family_members_by_uid(db, uid)
        return family_members
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家庭成员列表失败: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.post("/forgotpassword", response_model=PasswordResetResponse)
async def forgot_password(request_data: ForgotPasswordRequest):
```

#### 6. 首页数据获取功能

**接口地址：** `GET /api/v1/home/<USER>
**代码文件：** `app/api/v1/home.py`
**代码行数：** 12-80
**功能描述：** 获取用户首页展示数据，包含健康概览信息

**核心功能特性：**
- 用户健康数据汇总
- 家庭成员健康状态
- 最新检测报告
- 数据可视化支持

**代码实现：**

```python
@router.get("/{uid}")
@router.get("/{uid}/{fuid}")
async def get_home_data(
    uid: int,
    request: Request,
    fuid: Optional[int] = None
) -> Dict[str, Any]:
    """获取首页数据接口 - 需要token认证"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问其他用户的数据"
            )

        # 初始化默认返回数据（只包含实际存在的字段）
        family_member_data = {
            "fuid": 0,
            "uid": uid,
            "relationship": "",
            "name": "",
            "gender": "",
            "height": 0.0,
            "weight": 0.0,
            "birth_year": 0,
            "age": 0,  # 计算得出的年龄
            "avatar_url": ""
        }
        
        health_report_data = None
        bvp_waveform_data = None

        # 如果指定了fuid，获取家庭成员信息
        if fuid is not None:
            family_member = crud_family_member.get_family_member_by_fuid(db, fuid)
            if family_member and family_member.uid == uid:
                # 计算年龄
                current_year = datetime.datetime.now().year
                age = current_year - family_member.birth_year if family_member.birth_year else 0
                
                family_member_data = {
                    "fuid": family_member.fuid,
                    "uid": family_member.uid,
                    "relationship": family_member.relationship or "",
                    "name": family_member.name or "",
                    "gender": family_member.gender or "",
                    "height": family_member.height or 0.0,
                    "weight": family_member.weight or 0.0,
                    "birth_year": family_member.birth_year or 0,
                    "age": age,
                    "avatar_url": family_member.avatar_url or ""
                }
                
                # 获取该家庭成员的最新健康数据
                health_report_data = crud_health_data.get_latest_health_report_by_fuid(db, uid, fuid)
                if health_report_data:
                    bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        else:
            # fuid为None，获取用户本人的最新健康数据
            health_report_data = crud_health_data.get_latest_health_report_by_uid(db, uid)
            if health_report_data:
                bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
```

#### 7. 视频健康分析功能

**接口地址：** `POST /api/v1/health/analyze`
**代码文件：** `app/api/v1/health.py`
**代码行数：** 80-150
**功能描述：** 核心功能：通过视频分析用户健康指标

**核心功能特性：**
- 视频文件上传处理
- rPPG算法健康分析
- 心率血压检测
- 健康风险评估
- 分析结果存储

**代码实现：**

```python
    try:
        form = await request.form()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"表单数据解析失败: {str(e)}")
    
    # 获取文件和表单字段
    file = form.get("file")
    request_data_str = form.get("request_data", "")
    
    # 解析request_data JSON字符串
    data_dict = parse_request_data(request_data_str)
    
    # 从解析后的字典中提取字段
    video_url_str: Optional[str] = safe_parse_string(data_dict.get('video_url', ''))
    name_str: Optional[str] = safe_parse_string(data_dict.get('name', ''))
    gender_str: Optional[str] = safe_parse_string(data_dict.get('gender', ''))
    height_float: Optional[float] = safe_parse_float(data_dict.get('height', ''))
    weight_float: Optional[float] = safe_parse_float(data_dict.get('weight', ''))
    birth_year_int: Optional[int] = safe_parse_int(data_dict.get('birth_year', ''))
    uid_int: Optional[int] = safe_parse_int(data_dict.get('uid', ''))
    fuid_int: Optional[int] = safe_parse_int(data_dict.get('fuid', ''))

    # 验证必填参数
    if not uid_int:
        raise HTTPException(status_code=400, detail="用户ID(uid)为必填参数")
    
    # 验证用户权限：确保当前用户只能为自己创建健康报告
    current_user_id = int(current_user.get("sub", 0))
    if uid_int != current_user_id:
        raise HTTPException(status_code=403, detail="无权为其他用户创建健康报告")

    # 校验参数：file和video_url必须二选一
    if (file is None and not video_url_str) or (file is not None and video_url_str):
        raise HTTPException(status_code=400, detail="请上传视频文件或视频地址，且只能二选一")

    allowed_ext: list = ['.mp4', '.mov']
    # 使用绝对路径确保目录创建成功
    temp_dir: str = os.path.abspath("temp_uploads")
    os.makedirs(temp_dir, exist_ok=True)
    temp_path: Optional[str] = None
    unique_filename: Optional[str] = None
    oss_object_key: Optional[str] = None
    is_oss: bool = False
    oss_client: Optional[OssClient] = None

    try:
        # 1. 处理文件上传或URL下载
        if file is not None:
            # 文件上传处理
            if not hasattr(file, 'filename') or not file.filename:
                raise HTTPException(status_code=400, detail="文件名不能为空")
            
            file_ext = os.path.splitext(file.filename)[-1].lower()
            if file_ext not in allowed_ext:
                raise HTTPException(status_code=400, detail=f"不支持的文件格式，仅支持: {', '.join(allowed_ext)}")
            
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            temp_path = os.path.join(temp_dir, unique_filename)
            
            try:
                content = await file.read()
                size_mb = len(content) / (1024 * 1024)
                if size_mb < 1 or size_mb > 100:
                    raise HTTPException(status_code=400, detail="视频大小需在1MB~100MB之间")
                
                with open(temp_path, "wb") as f:
                    f.write(content)
            except Exception as e:
                logger.error(f"文件保存失败: {e}")
                raise HTTPException(status_code=500, detail="文件保存失败")
        else:
```

#### 8. 健康报告查询功能

**接口地址：** `GET /api/v1/health/reports/{uid}`
**代码文件：** `app/api/v1/health.py`
**代码行数：** 350-400
**功能描述：** 查询用户历史健康检测报告

**核心功能特性：**
- 报告列表查询
- 时间范围筛选
- 详细数据获取
- 趋势分析支持

**代码实现：**

```python
                    logger.info(f"临时文件已删除: {temp_path}")
                else:
                    logger.info(f"临时文件不存在，无需删除: {temp_path}")
            except Exception as e:
                logger.warning(f"临时文件删除失败: {temp_path}, 错误: {e}")

        # 清理OSS文件
        if is_oss and oss_client and oss_object_key:
            try:
                oss_client.delete(oss_object_key)
                logger.info(f"OSS文件已删除: {oss_object_key}")
            except Exception as e:
                logger.warning(f"OSS文件删除失败: {oss_object_key}, 错误: {e}")

        logger.info("资源清理完成")

@router.get("/hello")
def health_check():
    """简单的健康检查端点"""
    return JSONResponse(content={"message": "Hello, World!", "status": "healthy"})

@router.get("/status")
async def detailed_health_check():
    """详细的健康检查端点，包含系统状态信息"""
    try:
        # 检查数据库连接
        db = None
        db_status = "unknown"
        try:
            db = get_db_session()
            # 简单的数据库查询测试
            db.execute("SELECT 1")
            db_status = "healthy"
        except Exception as e:
            db_status = f"error: {str(e)}"
        finally:
            if db:
                try:
                    close_db_session(db)
                except:
                    pass

        # 获取系统资源信息
        system_info = {}
        try:
            memory_info = psutil.Process().memory_info()
            system_info = {
                "memory_rss_mb": round(memory_info.rss / 1024 / 1024, 2),
                "memory_vms_mb": round(memory_info.vms / 1024 / 1024, 2),
                "cpu_percent": psutil.Process().cpu_percent(),
                "timestamp": datetime.datetime.now().isoformat()
```

### 核心模块组件

#### 1. 数据库模型层

**模块路径：** `app/models/`
**功能描述：** 定义用户、健康报告、家庭成员等数据模型
**包含文件：** user.py, health_report.py, family_member.py, bvp_waveform.py

#### 2. 数据访问层

**模块路径：** `app/crud/`
**功能描述：** 数据库操作封装，提供增删改查接口
**包含文件：** user.py, health_data.py, family_member.py

#### 3. 身份认证模块

**模块路径：** `app/core/auth.py`
**功能描述：** JWT token生成验证，用户身份认证
**包含文件：** auth.py

#### 4. 健康分析引擎

**模块路径：** `app/utils/health_analyzer.py`
**功能描述：** rPPG算法实现，视频健康指标分析
**包含文件：** health_analyzer.py, rppg_tool.py

#### 5. 配置管理模块

**模块路径：** `app/core/config.py`
**功能描述：** 系统配置参数管理
**包含文件：** config.py, database.py

### 技术特色与创新点

1. **rPPG视频健康检测技术**
   - 基于远程光电容积脉搏波技术
   - 通过普通摄像头实现非接触式健康检测
   - 支持心率、血压、血氧饱和度等多项指标分析

2. **智能健康风险评估**
   - 心脏风险评估算法
   - 脑血管风险分析
   - 房颤风险检测

3. **多用户家庭健康管理**
   - 支持家庭成员健康数据管理
   - 个性化健康报告生成
   - 历史数据趋势分析

4. **高性能异步处理架构**
   - FastAPI异步框架
   - 并发视频处理能力
   - 数据库连接池优化

### 代码质量保证

- 完整的异常处理机制
- 详细的日志记录系统
- 数据验证与安全防护
- RESTful API设计规范
- 模块化代码组织结构

### 部署与运维

- Docker容器化部署
- 数据库迁移脚本
- 日志轮转配置
- 性能监控机制

---
*本文档由系统自动生成，用于软件著作权申请材料*
