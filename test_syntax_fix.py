#!/usr/bin/env python3
"""
测试语法修复是否成功
"""

def test_imports():
    """测试所有关键模块的导入"""
    print("🧪 测试语法修复")
    print("=" * 50)
    
    try:
        print("📦 测试模块导入...")
        
        # 测试user.py
        from app.api.v1.user import router as user_router
        print("  ✅ app.api.v1.user - 导入成功")
        
        # 测试home.py
        from app.api.v1.home import router as home_router
        print("  ✅ app.api.v1.home - 导入成功")
        
        # 测试router.py
        from app.api.v1.router import api_router
        print("  ✅ app.api.v1.router - 导入成功")
        
        # 测试main.py
        from app.main import app
        print("  ✅ app.main - 导入成功")
        
        print("\n🎉 所有模块导入成功！语法错误已修复！")
        return True
        
    except SyntaxError as e:
        print(f"  ❌ 语法错误: {e}")
        return False
    except ImportError as e:
        print(f"  ❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 其他错误: {e}")
        return False

def test_fastapi_startup():
    """测试FastAPI应用启动"""
    print("\n🚀 测试FastAPI应用启动")
    print("=" * 50)
    
    try:
        from app.main import app
        
        # 检查应用配置
        print(f"  ✅ 应用标题: {app.title}")
        print(f"  ✅ 应用版本: {app.version}")
        print(f"  ✅ 路由数量: {len(app.routes)}")
        
        # 检查API路由
        api_routes = [route for route in app.routes if hasattr(route, 'path') and route.path.startswith('/api')]
        print(f"  ✅ API路由数量: {len(api_routes)}")
        
        print("\n🎉 FastAPI应用配置正常！")
        return True
        
    except Exception as e:
        print(f"  ❌ FastAPI启动错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 语法修复验证测试")
    print("=" * 60)
    
    # 测试导入
    import_success = test_imports()
    
    # 测试FastAPI启动
    fastapi_success = test_fastapi_startup()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 语法修复验证结果")
    print("=" * 60)
    
    if import_success and fastapi_success:
        print("🎉 恭喜！所有语法错误已成功修复！")
        print("\n✅ 修复内容:")
        print("  - 修复了user.py中的字符串引号冲突问题")
        print("  - 解决了'每个用户只能有一个\"本人\"记录'的语法错误")
        print("  - 确保所有API模块可以正常导入")
        print("  - FastAPI应用可以正常启动")
        
        print("\n🚀 现在可以正常启动服务:")
        print("  uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload")
        
        return True
    else:
        print("❌ 仍有问题需要修复")
        print(f"  - 模块导入: {'✅' if import_success else '❌'}")
        print(f"  - FastAPI启动: {'✅' if fastapi_success else '❌'}")
        
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
