/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    EmptyState: typeof import('./src/components/Common/EmptyState.vue')['default']
    HealthMetricCard: typeof import('./src/components/Common/HealthMetricCard.vue')['default']
    LineChart: typeof import('./src/components/Charts/LineChart.vue')['default']
    LoadingSpinner: typeof import('./src/components/Common/LoadingSpinner.vue')['default']
    MainLayout: typeof import('./src/components/Layout/MainLayout.vue')['default']
    PageContainer: typeof import('./src/components/Layout/PageContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
