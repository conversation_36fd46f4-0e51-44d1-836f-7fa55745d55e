<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康扫描功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        button.success {
            background: #28a745;
        }
        button.danger {
            background: #dc3545;
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        video {
            max-width: 400px;
            max-height: 300px;
            border-radius: 8px;
            border: 2px solid #ddd;
            background: #000;
        }
        .test-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .step-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .step-card.active {
            border-color: #007bff;
            background: #f8f9ff;
        }
        .step-card.completed {
            border-color: #28a745;
            background: #f8fff9;
        }
        .step-card.error {
            border-color: #dc3545;
            background: #fff8f8;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            line-height: 30px;
            margin-bottom: 10px;
        }
        .step-card.active .step-number {
            background: #007bff;
        }
        .step-card.completed .step-number {
            background: #28a745;
        }
        .step-card.error .step-number {
            background: #dc3545;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 健康扫描功能测试</h1>
            <p>测试摄像头、录制、上传和分析的完整流程</p>
        </div>

        <!-- 测试步骤 -->
        <div class="test-steps" id="test-steps">
            <div class="step-card" id="step-camera">
                <div class="step-number">1</div>
                <h3>摄像头测试</h3>
                <p>检查摄像头权限和画面显示</p>
                <button onclick="testCamera()">测试摄像头</button>
                <div id="camera-result"></div>
            </div>
            
            <div class="step-card" id="step-recording">
                <div class="step-number">2</div>
                <h3>录制测试</h3>
                <p>测试视频录制和格式</p>
                <button onclick="testRecording()" id="record-btn" disabled>测试录制</button>
                <div id="recording-result"></div>
            </div>
            
            <div class="step-card" id="step-upload">
                <div class="step-number">3</div>
                <h3>上传测试</h3>
                <p>测试视频上传和格式兼容性</p>
                <button onclick="testUpload()" id="upload-btn" disabled>测试上传</button>
                <div id="upload-result"></div>
            </div>
            
            <div class="step-card" id="step-analysis">
                <div class="step-number">4</div>
                <h3>分析测试</h3>
                <p>测试健康分析结果显示</p>
                <button onclick="testAnalysis()" id="analysis-btn" disabled>测试分析</button>
                <div id="analysis-result"></div>
            </div>
        </div>

        <!-- 视频显示区域 -->
        <div class="test-section">
            <h2>📹 视频测试区域</h2>
            <div class="video-container">
                <video id="testVideo" autoplay muted playsinline style="display: none;"></video>
                <video id="playbackVideo" controls style="display: none;"></video>
            </div>
            <div id="video-info"></div>
        </div>

        <!-- 详细日志 -->
        <div class="test-section">
            <h2>📋 测试日志</h2>
            <div id="test-log"></div>
        </div>

        <!-- 快速链接 -->
        <div class="test-section">
            <h2>🔗 快速链接</h2>
            <button onclick="openScanPage()">打开健康扫描页面</button>
            <button onclick="openVueApp()">打开Vue应用</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 测试状态
        let testState = {
            mediaStream: null,
            mediaRecorder: null,
            recordedBlob: null,
            currentStep: 0,
            authToken: null
        };

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `test-result ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新步骤状态
        function updateStepStatus(stepId, status) {
            const stepEl = document.getElementById(stepId);
            stepEl.className = `step-card ${status}`;
        }

        // 初始化
        window.onload = () => {
            log('健康扫描功能测试初始化完成');
            checkAuth();
        };

        function checkAuth() {
            const token = localStorage.getItem('health_detection_token');
            if (token) {
                testState.authToken = token;
                log('已获取认证token', 'success');
            } else {
                log('未找到认证token，请先登录', 'warning');
            }
        }

        // 测试摄像头
        async function testCamera() {
            log('开始测试摄像头...');
            updateStepStatus('step-camera', 'active');
            
            try {
                const constraints = {
                    video: {
                        width: { ideal: 1280, max: 1920 },
                        height: { ideal: 720, max: 1080 },
                        facingMode: 'user',
                        frameRate: { ideal: 30, max: 60 }
                    },
                    audio: false
                };

                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                testState.mediaStream = stream;

                const video = document.getElementById('testVideo');
                video.srcObject = stream;
                video.style.display = 'block';

                await new Promise((resolve) => {
                    video.onloadedmetadata = () => {
                        log(`摄像头启动成功: ${video.videoWidth}x${video.videoHeight}`, 'success');
                        document.getElementById('video-info').innerHTML = `
                            <div class="test-result info">
                                📐 视频尺寸: ${video.videoWidth} x ${video.videoHeight}<br>
                                📱 显示尺寸: ${video.clientWidth} x ${video.clientHeight}<br>
                                📏 宽高比: ${(video.videoWidth / video.videoHeight).toFixed(2)}
                            </div>
                        `;
                        resolve();
                    };
                });

                await video.play();
                
                updateStepStatus('step-camera', 'completed');
                document.getElementById('record-btn').disabled = false;
                document.getElementById('camera-result').innerHTML = '<div class="test-result success">✅ 摄像头测试通过</div>';
                
            } catch (error) {
                log(`摄像头测试失败: ${error.message}`, 'error');
                updateStepStatus('step-camera', 'error');
                document.getElementById('camera-result').innerHTML = `<div class="test-result error">❌ ${error.message}</div>`;
            }
        }

        // 测试录制
        async function testRecording() {
            log('开始测试录制...');
            updateStepStatus('step-recording', 'active');
            
            try {
                if (!testState.mediaStream) {
                    throw new Error('请先启动摄像头');
                }

                // 检测支持的视频格式
                let options = {};
                let mimeType = '';
                
                if (MediaRecorder.isTypeSupported('video/mp4')) {
                    mimeType = 'video/mp4';
                    options = { mimeType: 'video/mp4', videoBitsPerSecond: 1500000 };
                } else if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9')) {
                    mimeType = 'video/webm;codecs=vp9';
                    options = { mimeType: 'video/webm;codecs=vp9', videoBitsPerSecond: 1500000 };
                } else if (MediaRecorder.isTypeSupported('video/webm')) {
                    mimeType = 'video/webm';
                    options = { mimeType: 'video/webm', videoBitsPerSecond: 1500000 };
                } else {
                    throw new Error('浏览器不支持视频录制');
                }

                log(`使用录制格式: ${mimeType}`);

                const mediaRecorder = new MediaRecorder(testState.mediaStream, options);
                const recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: mimeType });
                    testState.recordedBlob = blob;
                    
                    const playbackVideo = document.getElementById('playbackVideo');
                    playbackVideo.src = URL.createObjectURL(blob);
                    playbackVideo.style.display = 'block';
                    
                    log(`录制完成: ${(blob.size / 1024 / 1024).toFixed(2)}MB, 格式: ${mimeType}`, 'success');
                    
                    updateStepStatus('step-recording', 'completed');
                    document.getElementById('upload-btn').disabled = false;
                    document.getElementById('recording-result').innerHTML = `
                        <div class="test-result success">
                            ✅ 录制测试通过<br>
                            文件大小: ${(blob.size / 1024 / 1024).toFixed(2)}MB<br>
                            格式: ${mimeType}
                        </div>
                    `;
                };

                mediaRecorder.start();
                log('开始录制，5秒后自动停止...');
                
                setTimeout(() => {
                    mediaRecorder.stop();
                }, 5000);
                
            } catch (error) {
                log(`录制测试失败: ${error.message}`, 'error');
                updateStepStatus('step-recording', 'error');
                document.getElementById('recording-result').innerHTML = `<div class="test-result error">❌ ${error.message}</div>`;
            }
        }

        // 测试上传
        async function testUpload() {
            log('开始测试上传...');
            updateStepStatus('step-upload', 'active');
            
            try {
                if (!testState.recordedBlob) {
                    throw new Error('请先录制视频');
                }

                if (!testState.authToken) {
                    throw new Error('请先登录获取token');
                }

                // 转换为MP4格式
                const mp4Blob = new Blob([testState.recordedBlob], { type: 'video/mp4' });
                
                const formData = new FormData();
                formData.append('file', mp4Blob, 'health_scan.mp4');
                formData.append('request_data', JSON.stringify({
                    uid: 9,
                    fuid: 1,
                    name: "测试用户",
                    gender: "男",
                    height: 175,
                    weight: 70,
                    birth_year: 1990
                }));

                log('正在上传视频...');
                
                const response = await fetch('/api/v1/health/video', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testState.authToken}`
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    log('上传成功，获取分析结果', 'success');
                    
                    updateStepStatus('step-upload', 'completed');
                    document.getElementById('analysis-btn').disabled = false;
                    document.getElementById('upload-result').innerHTML = `
                        <div class="test-result success">
                            ✅ 上传测试通过<br>
                            状态: ${response.status}<br>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                    
                    // 自动进行分析测试
                    setTimeout(() => testAnalysis(), 1000);
                    
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`HTTP ${response.status}: ${errorData.detail || '上传失败'}`);
                }
                
            } catch (error) {
                log(`上传测试失败: ${error.message}`, 'error');
                updateStepStatus('step-upload', 'error');
                document.getElementById('upload-result').innerHTML = `<div class="test-result error">❌ ${error.message}</div>`;
            }
        }

        // 测试分析
        async function testAnalysis() {
            log('开始测试分析结果显示...');
            updateStepStatus('step-analysis', 'active');
            
            try {
                // 模拟分析结果
                const mockResult = {
                    name: { value: "测试用户", unit: "", label: "姓名" },
                    gender: { value: "男", unit: "", label: "性别" },
                    age: { value: 34, unit: "岁", label: "年龄" },
                    bmi: { value: "22.9", unit: "", label: "BMI" },
                    heart_rate: { value: 72.5, unit: "bpm", label: "心率" },
                    blood_pressure: {
                        value: { SBP: 120, DBP: 80 },
                        unit: "mmHg",
                        label: "血压"
                    },
                    spo2: { value: 98.2, unit: "%", label: "血氧饱和度" },
                    breathing_rate: { value: 16.8, unit: "次/分", label: "呼吸频率" }
                };

                log('分析结果获取成功', 'success');
                
                updateStepStatus('step-analysis', 'completed');
                document.getElementById('analysis-result').innerHTML = `
                    <div class="test-result success">
                        ✅ 分析测试通过<br>
                        <pre>${JSON.stringify(mockResult, null, 2)}</pre>
                    </div>
                `;
                
                log('🎉 所有测试完成！健康扫描功能正常工作', 'success');
                
            } catch (error) {
                log(`分析测试失败: ${error.message}`, 'error');
                updateStepStatus('step-analysis', 'error');
                document.getElementById('analysis-result').innerHTML = `<div class="test-result error">❌ ${error.message}</div>`;
            }
        }

        // 工具函数
        function openScanPage() {
            window.open('http://localhost:3001/scan', '_blank');
        }

        function openVueApp() {
            window.open('http://localhost:3001/', '_blank');
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            log('日志已清除');
        }
    </script>
</body>
</html>
