<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue前端完整功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        button.danger {
            background: #dc3545;
        }
        button.success {
            background: #28a745;
        }
        .login-form {
            display: grid;
            gap: 15px;
            max-width: 400px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background: #28a745;
        }
        .status-offline {
            background: #dc3545;
        }
        .status-unknown {
            background: #6c757d;
        }
        .api-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .api-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .user-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Vue前端完整功能测试</h1>
        
        <!-- 登录状态 -->
        <div class="test-section">
            <h2>🔐 认证状态</h2>
            <div id="auth-status" class="user-info">
                <span class="status-indicator status-offline"></span>
                <span>未登录</span>
            </div>
            
            <!-- 登录表单 -->
            <div id="login-section">
                <h3>登录测试账号</h3>
                <div class="login-form">
                    <div class="form-group">
                        <label for="email">邮箱:</label>
                        <input type="email" id="email" value="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码:</label>
                        <input type="password" id="password" value="string" required>
                    </div>
                    <button onclick="login()">登录</button>
                </div>
            </div>
            
            <!-- 登录后操作 -->
            <div id="logged-in-section" class="hidden">
                <button onclick="logout()">退出登录</button>
                <button onclick="refreshToken()">刷新Token</button>
            </div>
            
            <div id="login-result"></div>
        </div>

        <!-- API连接测试 -->
        <div class="test-section">
            <h2>🌐 API连接测试</h2>
            <button onclick="testAllAPIs()">测试所有API连接</button>
            <div class="api-status" id="api-status"></div>
        </div>

        <!-- 家庭成员管理测试 -->
        <div class="test-section">
            <h2>👨‍👩‍👧‍👦 家庭成员管理测试</h2>
            <div class="test-controls">
                <button onclick="testFamilyList()">获取家庭成员列表</button>
                <button onclick="testAddFamily()">添加测试成员</button>
                <button onclick="testEditFamily()">编辑成员</button>
                <button onclick="testDeleteFamily()">删除成员</button>
            </div>
            <div id="family-result"></div>
            <div id="family-list"></div>
        </div>

        <!-- 健康扫描测试 -->
        <div class="test-section">
            <h2>🏥 健康扫描测试</h2>
            <div class="test-controls">
                <button onclick="testHealthAPI()">测试健康分析接口</button>
                <button onclick="openScanPage()">打开扫描页面</button>
                <button onclick="testVideoUpload()">测试视频上传</button>
            </div>
            <div id="health-result"></div>
        </div>

        <!-- 页面路由测试 -->
        <div class="test-section">
            <h2>🔗 页面路由测试</h2>
            <div class="test-controls">
                <button onclick="testRoutes()">测试所有路由</button>
                <button onclick="openPage('/')">首页</button>
                <button onclick="openPage('/family')">家庭成员</button>
                <button onclick="openPage('/scan')">健康扫描</button>
                <button onclick="openPage('/report')">健康报告</button>
            </div>
            <div id="route-result"></div>
        </div>

        <!-- 综合测试结果 -->
        <div class="test-section">
            <h2>📊 测试总结</h2>
            <button onclick="runFullTest()">运行完整测试流程</button>
            <div id="summary-result"></div>
        </div>
    </div>

    <script>
        // 全局状态
        let currentUser = null;
        let authToken = null;
        let familyMembers = [];
        let testResults = {
            auth: false,
            apis: {},
            family: false,
            health: false,
            routes: false
        };

        // 初始化
        window.onload = () => {
            checkAuthStatus();
            updateUI();
        };

        // 检查认证状态
        function checkAuthStatus() {
            const token = localStorage.getItem('health_detection_token');
            const userInfo = localStorage.getItem('health_detection_user');
            
            if (token && userInfo) {
                authToken = token;
                currentUser = JSON.parse(userInfo);
                updateAuthStatus(true);
            } else {
                updateAuthStatus(false);
            }
        }

        // 更新认证状态显示
        function updateAuthStatus(isLoggedIn) {
            const statusDiv = document.getElementById('auth-status');
            const loginSection = document.getElementById('login-section');
            const loggedInSection = document.getElementById('logged-in-section');
            
            if (isLoggedIn && currentUser) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-online"></span>
                    <span>已登录: ${currentUser.name} (${currentUser.email}) - UID: ${currentUser.uid}</span>
                `;
                loginSection.classList.add('hidden');
                loggedInSection.classList.remove('hidden');
                testResults.auth = true;
            } else {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-offline"></span>
                    <span>未登录</span>
                `;
                loginSection.classList.remove('hidden');
                loggedInSection.classList.add('hidden');
                testResults.auth = false;
            }
        }

        // 登录功能
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('login-result');
            
            if (!email || !password) {
                resultDiv.innerHTML = '<div class="test-result error">❌ 请填写邮箱和密码</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/v1/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 保存认证信息
                    authToken = data.token;
                    currentUser = {
                        uid: data.uid,
                        name: data.name,
                        email: data.email
                    };
                    
                    localStorage.setItem('health_detection_token', authToken);
                    localStorage.setItem('health_detection_user', JSON.stringify(currentUser));
                    
                    updateAuthStatus(true);
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 登录成功<br>
                            用户: ${data.name}<br>
                            UID: ${data.uid}<br>
                            Token: ${data.token.substring(0, 50)}...
                        </div>
                    `;
                    
                    // 自动测试API连接
                    setTimeout(() => {
                        testAllAPIs();
                    }, 1000);
                    
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 登录失败: ${error.message}
                    </div>
                `;
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('health_detection_token');
            localStorage.removeItem('health_detection_user');
            authToken = null;
            currentUser = null;
            updateAuthStatus(false);
            
            document.getElementById('login-result').innerHTML = `
                <div class="test-result info">ℹ️ 已退出登录</div>
            `;
        }

        // 刷新Token
        async function refreshToken() {
            if (!currentUser) {
                document.getElementById('login-result').innerHTML = `
                    <div class="test-result error">❌ 请先登录</div>
                `;
                return;
            }
            
            // 重新登录获取新token
            await login();
        }

        // 测试所有API连接
        async function testAllAPIs() {
            if (!authToken) {
                document.getElementById('api-status').innerHTML = `
                    <div class="test-result warning">⚠️ 请先登录后再测试API</div>
                `;
                return;
            }
            
            const apis = [
                { name: '健康检查', url: '/api/v1/health/hello', method: 'GET', auth: false },
                { name: '家庭成员列表', url: `/api/v1/users/${currentUser.uid}/familylist`, method: 'GET', auth: true },
                { name: '首页数据', url: `/api/v1/home/<USER>'GET', auth: true }
            ];
            
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="test-result info">🔄 正在测试API连接...</div>';
            
            let results = [];
            
            for (const api of apis) {
                try {
                    const headers = {
                        'Content-Type': 'application/json'
                    };
                    
                    if (api.auth && authToken) {
                        headers['Authorization'] = `Bearer ${authToken}`;
                    }
                    
                    const response = await fetch(api.url, {
                        method: api.method,
                        headers
                    });
                    
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? '✅ 正常' : `❌ ${response.status}`;
                    
                    results.push(`
                        <div class="api-item">
                            <strong>${api.name}</strong><br>
                            <small>${api.method} ${api.url}</small><br>
                            <span class="test-result ${status}">${statusText}</span>
                        </div>
                    `);
                    
                    testResults.apis[api.name] = response.ok;
                    
                } catch (error) {
                    results.push(`
                        <div class="api-item">
                            <strong>${api.name}</strong><br>
                            <small>${api.method} ${api.url}</small><br>
                            <span class="test-result error">❌ ${error.message}</span>
                        </div>
                    `);
                    testResults.apis[api.name] = false;
                }
            }
            
            statusDiv.innerHTML = results.join('');
        }

        // 更新UI状态
        function updateUI() {
            // 根据登录状态启用/禁用按钮
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.onclick && button.onclick.toString().includes('test') && !testResults.auth) {
                    // 需要认证的测试按钮
                    if (!button.onclick.toString().includes('login') && !button.onclick.toString().includes('openPage')) {
                        button.disabled = !testResults.auth;
                    }
                }
            });
        }

        // 测试家庭成员列表
        async function testFamilyList() {
            if (!authToken) {
                document.getElementById('family-result').innerHTML = `
                    <div class="test-result error">❌ 请先登录</div>
                `;
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/users/${currentUser.uid}/familylist`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    familyMembers = await response.json();
                    document.getElementById('family-result').innerHTML = `
                        <div class="test-result success">
                            ✅ 获取家庭成员成功，共 ${familyMembers.length} 个成员
                        </div>
                    `;
                    
                    // 显示成员列表
                    const listDiv = document.getElementById('family-list');
                    if (familyMembers.length > 0) {
                        listDiv.innerHTML = `
                            <h4>家庭成员列表:</h4>
                            ${familyMembers.map(member => `
                                <div class="api-item">
                                    <strong>${member.name}</strong> (${member.relationship})<br>
                                    <small>FUID: ${member.fuid}, 性别: ${member.gender}, 年龄: ${new Date().getFullYear() - member.birth_year}岁</small>
                                </div>
                            `).join('')}
                        `;
                    } else {
                        listDiv.innerHTML = '<div class="test-result info">ℹ️ 暂无家庭成员</div>';
                    }
                    
                    testResults.family = true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('family-result').innerHTML = `
                    <div class="test-result error">❌ 获取家庭成员失败: ${error.message}</div>
                `;
            }
        }

        // 测试添加家庭成员
        async function testAddFamily() {
            if (!authToken) {
                document.getElementById('family-result').innerHTML = `
                    <div class="test-result error">❌ 请先登录</div>
                `;
                return;
            }
            
            const testMember = {
                relationship: "其他",
                name: "测试成员" + Date.now(),
                gender: "男",
                height: 175,
                weight: 70,
                birth_year: 1990,
                avatar_url: "test.png",
                uid: currentUser.uid
            };
            
            try {
                const response = await fetch('/api/v1/users/addfamily', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testMember)
                });
                
                if (response.ok) {
                    document.getElementById('family-result').innerHTML = `
                        <div class="test-result success">
                            ✅ 添加家庭成员成功: ${testMember.name}
                        </div>
                    `;
                    
                    // 刷新列表
                    setTimeout(() => {
                        testFamilyList();
                    }, 1000);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('family-result').innerHTML = `
                    <div class="test-result error">❌ 添加家庭成员失败: ${error.message}</div>
                `;
            }
        }

        // 打开页面
        function openPage(path) {
            const url = `http://localhost:3001${path}`;
            window.open(url, '_blank');
            
            document.getElementById('route-result').innerHTML = `
                <div class="test-result info">ℹ️ 已打开页面: ${path}</div>
            `;
        }

        // 运行完整测试流程
        async function runFullTest() {
            const resultDiv = document.getElementById('summary-result');
            resultDiv.innerHTML = '<div class="test-result info">🔄 正在运行完整测试...</div>';
            
            let summary = [];
            
            // 1. 检查认证状态
            if (testResults.auth) {
                summary.push('✅ 用户认证: 通过');
            } else {
                summary.push('❌ 用户认证: 失败 - 请先登录');
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 测试失败: 请先登录<br>
                        ${summary.join('<br>')}
                    </div>
                `;
                return;
            }
            
            // 2. 测试API连接
            await testAllAPIs();
            const apiSuccess = Object.values(testResults.apis).every(result => result);
            summary.push(apiSuccess ? '✅ API连接: 通过' : '❌ API连接: 部分失败');
            
            // 3. 测试家庭成员功能
            await testFamilyList();
            summary.push(testResults.family ? '✅ 家庭成员: 通过' : '❌ 家庭成员: 失败');
            
            // 4. 生成总结
            const allPassed = testResults.auth && apiSuccess && testResults.family;
            
            resultDiv.innerHTML = `
                <div class="test-result ${allPassed ? 'success' : 'warning'}">
                    <h4>${allPassed ? '🎉 所有测试通过!' : '⚠️ 部分测试失败'}</h4>
                    ${summary.join('<br>')}
                    <br><br>
                    <strong>建议下一步:</strong><br>
                    ${allPassed ? 
                        '• 可以开始使用Vue前端应用<br>• 测试健康扫描功能<br>• 测试完整用户流程' : 
                        '• 检查失败的测试项<br>• 确认后端服务正常运行<br>• 检查网络连接'
                    }
                </div>
            `;
        }

        // 其他测试函数的占位符
        function testEditFamily() {
            document.getElementById('family-result').innerHTML = `
                <div class="test-result info">ℹ️ 编辑功能需要先有家庭成员</div>
            `;
        }

        function testDeleteFamily() {
            document.getElementById('family-result').innerHTML = `
                <div class="test-result info">ℹ️ 删除功能需要先有家庭成员</div>
            `;
        }

        function testHealthAPI() {
            document.getElementById('health-result').innerHTML = `
                <div class="test-result info">ℹ️ 健康分析需要上传视频文件</div>
            `;
        }

        function openScanPage() {
            openPage('/scan');
        }

        function testVideoUpload() {
            document.getElementById('health-result').innerHTML = `
                <div class="test-result info">ℹ️ 视频上传功能请在扫描页面测试</div>
            `;
        }

        function testRoutes() {
            const routes = ['/', '/family', '/scan', '/report'];
            routes.forEach((route, index) => {
                setTimeout(() => {
                    openPage(route);
                }, index * 500);
            });
            
            document.getElementById('route-result').innerHTML = `
                <div class="test-result success">✅ 已打开所有页面进行测试</div>
            `;
        }
    </script>
</body>
</html>
