{"version": 3, "sources": ["../../echarts/lib/data/helper/createDimensions.js", "../../echarts/lib/model/referHelper.js", "../../echarts/lib/chart/helper/createSeriesData.js", "../../echarts/lib/label/labelGuideHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\r\n * For outside usage compat (like echarts-gl are using it).\r\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\r\n * This method builds the relationship between:\r\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\r\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\r\n * + \"what the data source provids (see `source`)\".\r\n *\r\n * Some guess strategy will be adapted if user does not define something.\r\n * If no 'value' dimension specified, the first no-named dimension will be\r\n * named as 'value'.\r\n *\r\n * @return The results are always sorted by `storeDimIndex` asc.\r\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Helper for model references.\r\n * There are many manners to refer axis/coordSys.\r\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\r\n * @class\r\n * For example:\r\n * {\r\n *     coordSysName: 'cartesian2d',\r\n *     coordSysDims: ['x', 'y', ...],\r\n *     axisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: yAxisModel\r\n *     }),\r\n *     categoryAxisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: undefined\r\n *     }),\r\n *     // The index of the first category axis in `coordSysDims`.\r\n *     // `null/undefined` means no category axis exists.\r\n *     firstCategoryDimIndex: 1,\r\n *     // To replace user specified encode.\r\n * }\r\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\r\n * Caution: there are side effects to `sourceManager` in this method.\r\n * Should better only be called in `Series['getInitialData']`.\r\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Point, Path, Polyline } from '../util/graphic.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { cubicProjectPoint, quadraticProjectPoint } from 'zrender/lib/core/curve.js';\nimport { defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport { DISPLAY_STATES, SPECIAL_STATES } from '../util/states.js';\nvar PI2 = Math.PI * 2;\nvar CMD = PathProxy.CMD;\nvar DEFAULT_SEARCH_SPACE = ['top', 'right', 'bottom', 'left'];\nfunction getCandidateAnchor(pos, distance, rect, outPt, outDir) {\n  var width = rect.width;\n  var height = rect.height;\n  switch (pos) {\n    case 'top':\n      outPt.set(rect.x + width / 2, rect.y - distance);\n      outDir.set(0, -1);\n      break;\n    case 'bottom':\n      outPt.set(rect.x + width / 2, rect.y + height + distance);\n      outDir.set(0, 1);\n      break;\n    case 'left':\n      outPt.set(rect.x - distance, rect.y + height / 2);\n      outDir.set(-1, 0);\n      break;\n    case 'right':\n      outPt.set(rect.x + width + distance, rect.y + height / 2);\n      outDir.set(1, 0);\n      break;\n  }\n}\nfunction projectPointToArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y, out) {\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  x /= d;\n  y /= d;\n  // Intersect point.\n  var ox = x * r + cx;\n  var oy = y * r + cy;\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    // Is a circle\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n    // Project point is on the arc.\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  var x1 = r * Math.cos(startAngle) + cx;\n  var y1 = r * Math.sin(startAngle) + cy;\n  var x2 = r * Math.cos(endAngle) + cx;\n  var y2 = r * Math.sin(endAngle) + cy;\n  var d1 = (x1 - x) * (x1 - x) + (y1 - y) * (y1 - y);\n  var d2 = (x2 - x) * (x2 - x) + (y2 - y) * (y2 - y);\n  if (d1 < d2) {\n    out[0] = x1;\n    out[1] = y1;\n    return Math.sqrt(d1);\n  } else {\n    out[0] = x2;\n    out[1] = y2;\n    return Math.sqrt(d2);\n  }\n}\nfunction projectPointToLine(x1, y1, x2, y2, x, y, out, limitToEnds) {\n  var dx = x - x1;\n  var dy = y - y1;\n  var dx1 = x2 - x1;\n  var dy1 = y2 - y1;\n  var lineLen = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  dx1 /= lineLen;\n  dy1 /= lineLen;\n  // dot product\n  var projectedLen = dx * dx1 + dy * dy1;\n  var t = projectedLen / lineLen;\n  if (limitToEnds) {\n    t = Math.min(Math.max(t, 0), 1);\n  }\n  t *= lineLen;\n  var ox = out[0] = x1 + t * dx1;\n  var oy = out[1] = y1 + t * dy1;\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nfunction projectPointToRect(x1, y1, width, height, x, y, out) {\n  if (width < 0) {\n    x1 = x1 + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y1 = y1 + height;\n    height = -height;\n  }\n  var x2 = x1 + width;\n  var y2 = y1 + height;\n  var ox = out[0] = Math.min(Math.max(x, x1), x2);\n  var oy = out[1] = Math.min(Math.max(y, y1), y2);\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nvar tmpPt = [];\nfunction nearestPointOnRect(pt, rect, out) {\n  var dist = projectPointToRect(rect.x, rect.y, rect.width, rect.height, pt.x, pt.y, tmpPt);\n  out.set(tmpPt[0], tmpPt[1]);\n  return dist;\n}\n/**\r\n * Calculate min distance corresponding point.\r\n * This method won't evaluate if point is in the path.\r\n */\nfunction nearestPointOnPath(pt, path, out) {\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  var minDist = Infinity;\n  var data = path.data;\n  var x = pt.x;\n  var y = pt.y;\n  for (var i = 0; i < data.length;) {\n    var cmd = data[i++];\n    if (i === 1) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    var d = minDist;\n    switch (cmd) {\n      case CMD.M:\n        // moveTo 命令重新创建一个新的 subpath, 并且更新新的起点\n        // 在 closePath 的时候使用\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        d = projectPointToLine(xi, yi, data[i], data[i + 1], x, y, tmpPt, true);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        d = cubicProjectPoint(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        d = quadraticProjectPoint(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        // TODO Arc 判断的开销比较大\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++];\n        // TODO Arc 旋转\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy;\n        // 不是直接使用 arc 命令\n        if (i <= 1) {\n          // 第一个命令起点还未定义\n          x0 = x1;\n          y0 = y1;\n        }\n        // zr 使用scale来模拟椭圆, 这里也对x做一定的缩放\n        var _x = (x - cx) * ry / rx + cx;\n        d = projectPointToArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y, tmpPt);\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        d = projectPointToRect(x0, y0, width, height, x, y, tmpPt);\n        break;\n      case CMD.Z:\n        d = projectPointToLine(xi, yi, x0, y0, x, y, tmpPt, true);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n    if (d < minDist) {\n      minDist = d;\n      out.set(tmpPt[0], tmpPt[1]);\n    }\n  }\n  return minDist;\n}\n// Temporal variable for intermediate usage.\nvar pt0 = new Point();\nvar pt1 = new Point();\nvar pt2 = new Point();\nvar dir = new Point();\nvar dir2 = new Point();\n/**\r\n * Calculate a proper guide line based on the label position and graphic element definition\r\n * @param label\r\n * @param labelRect\r\n * @param target\r\n * @param targetRect\r\n */\nexport function updateLabelLinePoints(target, labelLineModel) {\n  if (!target) {\n    return;\n  }\n  var labelLine = target.getTextGuideLine();\n  var label = target.getTextContent();\n  // Needs to create text guide in each charts.\n  if (!(label && labelLine)) {\n    return;\n  }\n  var labelGuideConfig = target.textGuideLineConfig || {};\n  var points = [[0, 0], [0, 0], [0, 0]];\n  var searchSpace = labelGuideConfig.candidates || DEFAULT_SEARCH_SPACE;\n  var labelRect = label.getBoundingRect().clone();\n  labelRect.applyTransform(label.getComputedTransform());\n  var minDist = Infinity;\n  var anchorPoint = labelGuideConfig.anchor;\n  var targetTransform = target.getComputedTransform();\n  var targetInversedTransform = targetTransform && invert([], targetTransform);\n  var len = labelLineModel.get('length2') || 0;\n  if (anchorPoint) {\n    pt2.copy(anchorPoint);\n  }\n  for (var i = 0; i < searchSpace.length; i++) {\n    var candidate = searchSpace[i];\n    getCandidateAnchor(candidate, 0, labelRect, pt0, dir);\n    Point.scaleAndAdd(pt1, pt0, dir, len);\n    // Transform to target coord space.\n    pt1.transform(targetInversedTransform);\n    // Note: getBoundingRect will ensure the `path` being created.\n    var boundingRect = target.getBoundingRect();\n    var dist = anchorPoint ? anchorPoint.distance(pt1) : target instanceof Path ? nearestPointOnPath(pt1, target.path, pt2) : nearestPointOnRect(pt1, boundingRect, pt2);\n    // TODO pt2 is in the path\n    if (dist < minDist) {\n      minDist = dist;\n      // Transform back to global space.\n      pt1.transform(targetTransform);\n      pt2.transform(targetTransform);\n      pt2.toArray(points[0]);\n      pt1.toArray(points[1]);\n      pt0.toArray(points[2]);\n    }\n  }\n  limitTurnAngle(points, labelLineModel.get('minTurnAngle'));\n  labelLine.setShape({\n    points: points\n  });\n}\n// Temporal variable for the limitTurnAngle function\nvar tmpArr = [];\nvar tmpProjPoint = new Point();\n/**\r\n * Reduce the line segment attached to the label to limit the turn angle between two segments.\r\n * @param linePoints\r\n * @param minTurnAngle Radian of minimum turn angle. 0 - 180\r\n */\nexport function limitTurnAngle(linePoints, minTurnAngle) {\n  if (!(minTurnAngle <= 180 && minTurnAngle > 0)) {\n    return;\n  }\n  minTurnAngle = minTurnAngle / 180 * Math.PI;\n  // The line points can be\n  //      /pt1----pt2 (label)\n  //     /\n  // pt0/\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt0, pt1);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(dir2);\n  var minTurnAngleCos = Math.cos(minTurnAngle);\n  if (minTurnAngleCos < angleCos) {\n    // Smaller than minTurnAngle\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    // Calculate new projected length with limited minTurnAngle and get the new connect point\n    tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI - minTurnAngle));\n    // Limit the new calculated connect point between pt1 and pt2.\n    var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n    if (isNaN(t)) {\n      return;\n    }\n    if (t < 0) {\n      Point.copy(tmpProjPoint, pt1);\n    } else if (t > 1) {\n      Point.copy(tmpProjPoint, pt2);\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n/**\r\n * Limit the angle of line and the surface\r\n * @param maxSurfaceAngle Radian of minimum turn angle. 0 - 180. 0 is same direction to normal. 180 is opposite\r\n */\nexport function limitSurfaceAngle(linePoints, surfaceNormal, maxSurfaceAngle) {\n  if (!(maxSurfaceAngle <= 180 && maxSurfaceAngle > 0)) {\n    return;\n  }\n  maxSurfaceAngle = maxSurfaceAngle / 180 * Math.PI;\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt1, pt0);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(surfaceNormal);\n  var maxSurfaceAngleCos = Math.cos(maxSurfaceAngle);\n  if (angleCos < maxSurfaceAngleCos) {\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    var HALF_PI = Math.PI / 2;\n    var angle2 = Math.acos(dir2.dot(surfaceNormal));\n    var newAngle = HALF_PI + angle2 - maxSurfaceAngle;\n    if (newAngle >= HALF_PI) {\n      // parallel\n      Point.copy(tmpProjPoint, pt2);\n    } else {\n      // Calculate new projected length with limited minTurnAngle and get the new connect point\n      tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI / 2 - newAngle));\n      // Limit the new calculated connect point between pt1 and pt2.\n      var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n      if (isNaN(t)) {\n        return;\n      }\n      if (t < 0) {\n        Point.copy(tmpProjPoint, pt1);\n      } else if (t > 1) {\n        Point.copy(tmpProjPoint, pt2);\n      }\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\nfunction setLabelLineState(labelLine, ignore, stateName, stateModel) {\n  var isNormal = stateName === 'normal';\n  var stateObj = isNormal ? labelLine : labelLine.ensureState(stateName);\n  // Make sure display.\n  stateObj.ignore = ignore;\n  // Set smooth\n  var smooth = stateModel.get('smooth');\n  if (smooth && smooth === true) {\n    smooth = 0.3;\n  }\n  stateObj.shape = stateObj.shape || {};\n  if (smooth > 0) {\n    stateObj.shape.smooth = smooth;\n  }\n  var styleObj = stateModel.getModel('lineStyle').getLineStyle();\n  isNormal ? labelLine.useStyle(styleObj) : stateObj.style = styleObj;\n}\nfunction buildLabelLinePath(path, shape) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (!points) {\n    return;\n  }\n  path.moveTo(points[0][0], points[0][1]);\n  if (smooth > 0 && points.length >= 3) {\n    var len1 = vector.dist(points[0], points[1]);\n    var len2 = vector.dist(points[1], points[2]);\n    if (!len1 || !len2) {\n      path.lineTo(points[1][0], points[1][1]);\n      path.lineTo(points[2][0], points[2][1]);\n      return;\n    }\n    var moveLen = Math.min(len1, len2) * smooth;\n    var midPoint0 = vector.lerp([], points[1], points[0], moveLen / len1);\n    var midPoint2 = vector.lerp([], points[1], points[2], moveLen / len2);\n    var midPoint1 = vector.lerp([], midPoint0, midPoint2, 0.5);\n    path.bezierCurveTo(midPoint0[0], midPoint0[1], midPoint0[0], midPoint0[1], midPoint1[0], midPoint1[1]);\n    path.bezierCurveTo(midPoint2[0], midPoint2[1], midPoint2[0], midPoint2[1], points[2][0], points[2][1]);\n  } else {\n    for (var i = 1; i < points.length; i++) {\n      path.lineTo(points[i][0], points[i][1]);\n    }\n  }\n}\n/**\r\n * Create a label line if necessary and set it's style.\r\n */\nexport function setLabelLineStyle(targetEl, statesModels, defaultStyle) {\n  var labelLine = targetEl.getTextGuideLine();\n  var label = targetEl.getTextContent();\n  if (!label) {\n    // Not show label line if there is no label.\n    if (labelLine) {\n      targetEl.removeTextGuideLine();\n    }\n    return;\n  }\n  var normalModel = statesModels.normal;\n  var showNormal = normalModel.get('show');\n  var labelIgnoreNormal = label.ignore;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateName = DISPLAY_STATES[i];\n    var stateModel = statesModels[stateName];\n    var isNormal = stateName === 'normal';\n    if (stateModel) {\n      var stateShow = stateModel.get('show');\n      var isLabelIgnored = isNormal ? labelIgnoreNormal : retrieve2(label.states[stateName] && label.states[stateName].ignore, labelIgnoreNormal);\n      if (isLabelIgnored // Not show when label is not shown in this state.\n      || !retrieve2(stateShow, showNormal) // Use normal state by default if not set.\n      ) {\n        var stateObj = isNormal ? labelLine : labelLine && labelLine.states[stateName];\n        if (stateObj) {\n          stateObj.ignore = true;\n        }\n        if (!!labelLine) {\n          setLabelLineState(labelLine, true, stateName, stateModel);\n        }\n        continue;\n      }\n      // Create labelLine if not exists\n      if (!labelLine) {\n        labelLine = new Polyline();\n        targetEl.setTextGuideLine(labelLine);\n        // Reset state of normal because it's new created.\n        // NOTE: NORMAL should always been the first!\n        if (!isNormal && (labelIgnoreNormal || !showNormal)) {\n          setLabelLineState(labelLine, true, 'normal', statesModels.normal);\n        }\n        // Use same state proxy.\n        if (targetEl.stateProxy) {\n          labelLine.stateProxy = targetEl.stateProxy;\n        }\n      }\n      setLabelLineState(labelLine, false, stateName, stateModel);\n    }\n  }\n  if (labelLine) {\n    defaults(labelLine.style, defaultStyle);\n    // Not fill.\n    labelLine.style.fill = null;\n    var showAbove = normalModel.get('showAbove');\n    var labelLineConfig = targetEl.textGuideLineConfig = targetEl.textGuideLineConfig || {};\n    labelLineConfig.showAbove = showAbove || false;\n    // Custom the buildPath.\n    labelLine.buildPath = buildLabelLinePath;\n  }\n}\nexport function getLabelLineStatesModels(itemModel, labelLineName) {\n  labelLineName = labelLineName || 'labelLine';\n  var statesModels = {\n    normal: itemModel.getModel(labelLineName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelLineName]);\n  }\n  return statesModels;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDO,SAAS,iBAAiB,QAAQ,KAAK;AAC5C,SAAO,wBAAwB,QAAQ,GAAG,EAAE;AAC9C;AAae,SAAR,wBAEP,QAAQ,KAAK;AACX,MAAI,CAAC,iBAAiB,MAAM,GAAG;AAC7B,aAAS,iCAAiC,MAAM;AAAA,EAClD;AACA,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,IAAI,mBAAmB,CAAC;AACtC,MAAI,UAAU,IAAI,oBAAoB,OAAO,oBAAoB,CAAC;AAClE,MAAI,kBAAkB,cAAc;AACpC,MAAI,aAAa,CAAC;AAClB,MAAI,WAAW,YAAY,QAAQ,SAAS,SAAS,IAAI,eAAe;AAGxE,MAAI,uBAAuB,IAAI,2BAA2B,2BAA2B,QAAQ;AAC7F,MAAI,6BAA6B,YAAY,OAAO;AACpD,MAAI,iBAAiB,6BAA6B,uBAAuB,MAAM,IAAI,iBAAiB,OAAO;AAC3G,MAAI,YAAY,IAAI;AACpB,MAAI,CAAC,aAAa,IAAI,iBAAiB;AACrC,gBAAY,IAAI,gBAAgB,QAAQ,QAAQ;AAAA,EAClD;AACA,MAAI,eAAe,cAAc,SAAS;AAC1C,MAAI,aAAa,IAAI,eAAe,QAAQ;AAC5C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,eAAW,CAAC,IAAI;AAAA,EAClB;AACA,WAAS,cAAc,QAAQ;AAC7B,QAAI,MAAM,WAAW,MAAM;AAC3B,QAAI,MAAM,GAAG;AACX,UAAI,gBAAgB,QAAQ,MAAM;AAClC,UAAI,aAAa,SAAS,aAAa,IAAI,gBAAgB;AAAA,QACzD,MAAM;AAAA,MACR;AACA,UAAIA,cAAa,IAAI,8BAAsB;AAC3C,UAAI,cAAc,WAAW;AAC7B,UAAI,eAAe,QAAQ,eAAe,IAAI,WAAW,KAAK,MAAM;AAIlE,QAAAA,YAAW,OAAOA,YAAW,cAAc;AAAA,MAC7C;AACA,iBAAW,QAAQ,SAASA,YAAW,OAAO,WAAW;AACzD,iBAAW,eAAe,SAASA,YAAW,cAAc,WAAW;AACvE,UAAI,SAAS,WAAW;AACxB,iBAAW,MAAM,IAAI;AACrB,MAAAA,YAAW,gBAAgB;AAC3B,iBAAW,KAAKA,WAAU;AAC1B,aAAOA;AAAA,IACT;AACA,WAAO,WAAW,GAAG;AAAA,EACvB;AACA,MAAI,CAAC,sBAAsB;AACzB,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,oBAAc,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,eAAa,KAAK,SAAU,aAAaC,WAAU;AACjD,QAAI,WAAW,iBAAiB,WAAW,EAAE,MAAM;AAInD,QAAI,SAAS,WAAW,KAAK,CAAC,SAAS,SAAS,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,GAAG;AACtE,mBAAa,IAAIA,WAAU,KAAK;AAChC;AAAA,IACF;AACA,QAAI,gBAAgB,aAAa,IAAIA,WAAU,CAAC,CAAC;AACjD,SAAK,UAAU,SAAU,oBAAoB,KAAK;AAEhD,UAAIC,gBAAe,SAAS,kBAAkB,IAAI,eAAe,IAAI,kBAAkB,IAAI;AAC3F,UAAIA,iBAAgB,QAAQA,gBAAe,UAAU;AACnD,sBAAc,GAAG,IAAIA;AACrB,iBAAS,cAAcA,aAAY,GAAGD,WAAU,GAAG;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,MAAI,cAAc;AAClB,OAAK,SAAS,SAAU,eAAe;AACrC,QAAIA;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,aAAa,GAAG;AAC3B,MAAAA,YAAW;AACX,mBAAa,CAAC;AAAA,IAChB,OAAO;AACL,mBAAa;AACb,MAAAA,YAAW,WAAW;AACtB,UAAI,cAAc,WAAW;AAC7B,iBAAW,cAAc;AACzB,mBAAa,OAAO,CAAC,GAAG,UAAU;AAClC,iBAAW,cAAc;AAEzB,0BAAoB,WAAW;AAC/B,4BAAsB,WAAW;AACjC,iBAAW,OAAO,WAAW,WAAW,WAAW,gBAAgB,WAAW,UAAU,WAAW,YAAY;AAAA,IACjH;AACA,QAAI,WAAW,aAAa,IAAIA,SAAQ;AAExC,QAAI,aAAa,OAAO;AACtB;AAAA,IACF;AACA,eAAW,iBAAiB,QAAQ;AAEpC,QAAI,CAAC,SAAS,QAAQ;AACpB,eAASE,KAAI,GAAGA,MAAK,qBAAqB,kBAAkB,UAAU,IAAIA,MAAK;AAC7E,eAAO,cAAc,YAAY,cAAc,WAAW,EAAE,YAAY,MAAM;AAC5E;AAAA,QACF;AACA,sBAAc,YAAY,SAAS,KAAK,aAAa;AAAA,MACvD;AAAA,IACF;AAEA,SAAK,UAAU,SAAUD,eAAc,eAAe;AACpD,UAAIF,cAAa,cAAcE,aAAY;AAE3C,UAAI,8BAA8B,WAAW,QAAQ,MAAM;AACzD,QAAAF,YAAW,OAAO,WAAW;AAAA,MAC/B;AACA,eAAS,SAASA,aAAY,UAAU,GAAGC,WAAU,aAAa;AAClE,UAAID,YAAW,QAAQ,QAAQ,mBAAmB;AAChD,YAAI,wBAAwB,kBAAkB,aAAa;AAC3D,SAAC,SAAS,qBAAqB,MAAM,wBAAwB;AAAA,UAC3D,MAAM;AAAA,QACR;AACA,QAAAA,YAAW,OAAOA,YAAW,cAAc,sBAAsB;AACjE,QAAAA,YAAW,iBAAiB,sBAAsB;AAAA,MACpD;AAEA,6BAAuB,SAASA,YAAW,WAAW,mBAAmB;AAAA,IAC3E,CAAC;AAAA,EACH,CAAC;AACD,WAAS,SAASA,aAAYC,WAAU,eAAe;AACrD,QAAI,kBAAkB,IAAIA,SAAQ,KAAK,MAAM;AAC3C,MAAAD,YAAW,UAAUC,SAAQ,IAAI;AAAA,IACnC,OAAO;AACL,MAAAD,YAAW,WAAWC;AACtB,MAAAD,YAAW,gBAAgB;AAC3B,sBAAgB,IAAIC,WAAU,IAAI;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,gBAAgB,IAAI;AACxB,MAAI,qBAAqB,IAAI;AAC7B,MAAI,WAAW,sBAAsB;AACrC,uBAAqB,gBAAgB,sBAAsB,IAAI;AAC/D,MAAI,QAAQ,iBAAiB;AAC7B,WAAS,0BAA0BD,aAAY;AAC7C,QAAIA,YAAW,QAAQ,MAAM;AAE3B,MAAAA,YAAW,OAAOA,YAAW;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,CAAC,sBAAsB;AACzB,aAAS,eAAe,GAAG,eAAe,UAAU,gBAAgB;AAClE,UAAI,aAAa,cAAc,YAAY;AAC3C,UAAI,WAAW,WAAW;AAC1B,UAAI,YAAY,MAAM;AAEpB,mBAAW,WAAW,gBAAgB,OAAO,iBAAiB,QAAQ;AACtE,mBAAW,gBAAgB;AAE3B,YAAI,CAAC,iBAAiB,sBAAsB,GAAG;AAC7C,qBAAW,eAAe;AAAA,QAC5B;AACA;AAAA,MACF;AACA,gCAA0B,UAAU;AACpC,UAAI,WAAW,QAAQ,SAAS,aAAa,QAAQ,YAAY,MAAM,WAAW,QAa/E,WAAW,iBAAiB,WAAW,UAAU,YAAY,QAAQ,WAAW,UAAU,cAAc,QAAQ;AACjH,mBAAW,OAAO;AAAA,MACpB;AAAA,IACF;AAAA,EACF,OAAO;AACL,SAAK,YAAY,SAAUA,aAAY;AAErC,gCAA0BA,WAAU;AAAA,IACtC,CAAC;AAGD,eAAW,KAAK,SAAU,OAAO,OAAO;AACtC,aAAO,MAAM,gBAAgB,MAAM;AAAA,IACrC,CAAC;AAAA,EACH;AACA,oBAAkB,UAAU;AAC5B,SAAO,IAAI,iBAAiB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,EACpB,CAAC;AACH;AACA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,iBAAiB,cAAc;AACnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,MAAM,OAAO,CAAC;AAClB,QAAI,kBAAkB,IAAI;AAC1B,QAAI,QAAQ,eAAe,IAAI,eAAe,KAAK;AACnD,QAAI,QAAQ,GAAG;AAEb,UAAI,OAAO,mBAAmB,QAAQ;AAAA,IACxC;AACA;AACA,mBAAe,IAAI,iBAAiB,KAAK;AAAA,EAC3C;AACF;AAWA,SAAS,YAAY,QAAQ,SAAS,SAAS,aAAa;AAG1D,MAAI,WAAW,KAAK,IAAI,OAAO,2BAA2B,GAAG,QAAQ,QAAQ,QAAQ,QAAQ,eAAe,CAAC;AAC7G,OAAK,SAAS,SAAU,YAAY;AAClC,QAAI;AACJ,QAAI,SAAS,UAAU,MAAM,oBAAoB,WAAW,UAAU;AACpE,iBAAW,KAAK,IAAI,UAAU,kBAAkB,MAAM;AAAA,IACxD;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,MAAMI,MAAK,UAAU;AAC5C,MAAI,YAAYA,KAAI,OAAO,IAAI,GAAG;AAChC,QAAI,IAAI;AACR,WAAOA,KAAI,OAAO,OAAO,CAAC,GAAG;AAC3B;AAAA,IACF;AACA,YAAQ;AAAA,EACV;AACA,EAAAA,KAAI,IAAI,MAAM,IAAI;AAClB,SAAO;AACT;;;ACzPA,IAAI;AAAA;AAAA,EAA4B,2BAAY;AAC1C,aAASC,cAAa,cAAc;AAClC,WAAK,eAAe,CAAC;AACrB,WAAK,UAAU,cAAc;AAC7B,WAAK,kBAAkB,cAAc;AACrC,WAAK,eAAe;AAAA,IACtB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACK,SAAS,wBAAwB,aAAa;AACnD,MAAI,eAAe,YAAY,IAAI,kBAAkB;AACrD,MAAI,SAAS,IAAI,aAAa,YAAY;AAC1C,MAAI,QAAQ,SAAS,YAAY;AACjC,MAAI,OAAO;AACT,UAAM,aAAa,QAAQ,OAAO,SAAS,OAAO,eAAe;AACjE,WAAO;AAAA,EACT;AACF;AACA,IAAI,WAAW;AAAA,EACb,aAAa,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACpE,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,MAAuC;AACzC,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,YAAY,SAAS,YAAY,IAAI,YAAY,GAAG,YAAY,IAAI,SAAS,GAAG,CAAC,IAAI,aAAa;AAAA,MACpH;AACA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,YAAY,SAAS,YAAY,IAAI,YAAY,GAAG,YAAY,IAAI,SAAS,GAAG,CAAC,IAAI,aAAa;AAAA,MACpH;AAAA,IACF;AACA,WAAO,eAAe,CAAC,KAAK,GAAG;AAC/B,YAAQ,IAAI,KAAK,UAAU;AAC3B,YAAQ,IAAI,KAAK,UAAU;AAC3B,QAAI,WAAW,UAAU,GAAG;AAC1B,sBAAgB,IAAI,KAAK,UAAU;AACnC,aAAO,wBAAwB;AAAA,IACjC;AACA,QAAI,WAAW,UAAU,GAAG;AAC1B,sBAAgB,IAAI,KAAK,UAAU;AACnC,aAAO,yBAAyB,SAAS,OAAO,wBAAwB;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,YAAY,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACnE,QAAI,kBAAkB,YAAY,uBAAuB,cAAc,gBAAgB,EAAE,OAAO,CAAC;AACjG,QAAI,MAAuC;AACzC,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAAA,IACF;AACA,WAAO,eAAe,CAAC,QAAQ;AAC/B,YAAQ,IAAI,UAAU,eAAe;AACrC,QAAI,WAAW,eAAe,GAAG;AAC/B,sBAAgB,IAAI,UAAU,eAAe;AAC7C,aAAO,wBAAwB;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AAC9D,QAAI,aAAa,YAAY,uBAAuB,SAAS,gBAAgB,EAAE,OAAO,CAAC;AACvF,QAAI,kBAAkB,WAAW,cAAc,YAAY;AAC3D,QAAI,iBAAiB,WAAW,cAAc,WAAW;AACzD,QAAI,MAAuC;AACzC,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AACA,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,eAAe,CAAC,UAAU,OAAO;AACxC,YAAQ,IAAI,UAAU,eAAe;AACrC,YAAQ,IAAI,SAAS,cAAc;AACnC,QAAI,WAAW,eAAe,GAAG;AAC/B,sBAAgB,IAAI,UAAU,eAAe;AAC7C,aAAO,wBAAwB;AAAA,IACjC;AACA,QAAI,WAAW,cAAc,GAAG;AAC9B,sBAAgB,IAAI,SAAS,cAAc;AAC3C,aAAO,yBAAyB,SAAS,OAAO,wBAAwB;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,KAAK,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AAC5D,WAAO,eAAe,CAAC,OAAO,KAAK;AAAA,EACrC;AAAA,EACA,UAAU,SAAU,aAAa,QAAQ,SAAS,iBAAiB;AACjE,QAAI,UAAU,YAAY;AAC1B,QAAI,gBAAgB,QAAQ,aAAa,YAAY,YAAY,IAAI,eAAe,CAAC;AACrF,QAAI,eAAe,OAAO,eAAe,cAAc,WAAW,MAAM;AACxE,SAAK,cAAc,mBAAmB,SAAU,WAAW,OAAO;AAChE,UAAI,YAAY,QAAQ,aAAa,gBAAgB,SAAS;AAC9D,UAAI,UAAU,aAAa,KAAK;AAChC,cAAQ,IAAI,SAAS,SAAS;AAC9B,UAAI,WAAW,SAAS,GAAG;AACzB,wBAAgB,IAAI,SAAS,SAAS;AACtC,YAAI,OAAO,yBAAyB,MAAM;AACxC,iBAAO,wBAAwB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,WAAW;AAC7B,SAAO,UAAU,IAAI,MAAM,MAAM;AACnC;;;ACxHA,SAAS,mBAAmB,aAAa,cAAc;AACrD,MAAI,eAAe,YAAY,IAAI,kBAAkB;AACrD,MAAI,qBAAqB,yBAAiB,IAAI,YAAY;AAC1D,MAAI;AACJ,MAAI,gBAAgB,aAAa,cAAc;AAC7C,sBAAyB,IAAI,aAAa,cAAc,SAAU,KAAK;AACrE,UAAI,UAAU;AAAA,QACZ,MAAM;AAAA,MACR;AACA,UAAI,YAAY,aAAa,QAAQ,IAAI,GAAG;AAC5C,UAAI,WAAW;AACb,YAAI,WAAW,UAAU,IAAI,MAAM;AACnC,gBAAQ,OAAO,uBAAuB,QAAQ;AAAA,MAChD;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,uBAAuB,mBAAmB,oBAAoB,mBAAmB,kBAAkB,IAAI,mBAAmB,WAAW,MAAM,MAAM,CAAC,KAAK,GAAG;AAAA,EAC9K;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,aAAa,uBAAuB,cAAc;AAC3E,MAAI;AACJ,MAAI;AACJ,kBAAuB,KAAK,aAAa,SAAU,SAAS,UAAU;AACpE,QAAI,WAAW,QAAQ;AACvB,QAAI,oBAAoB,aAAa,gBAAgB,IAAI,QAAQ;AACjE,QAAI,mBAAmB;AACrB,UAAI,yBAAyB,MAAM;AACjC,gCAAwB;AAAA,MAC1B;AACA,cAAQ,cAAc,kBAAkB,eAAe;AACvD,UAAI,uBAAuB;AACzB,gBAAQ,wBAAwB;AAAA,MAClC;AAAA,IACF;AACA,QAAI,QAAQ,UAAU,YAAY,MAAM;AACtC,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI,CAAC,iBAAiB,yBAAyB,MAAM;AACnD,gBAAY,qBAAqB,EAAE,UAAU,WAAW;AAAA,EAC1D;AACA,SAAO;AACT;AAKA,SAAS,iBAAiB,WAAW,aAAa,KAAK;AACrD,QAAM,OAAO,CAAC;AACd,MAAI,gBAAgB,YAAY,iBAAiB;AACjD,MAAI;AACJ,MAAI,mBAAmB;AACvB,MAAI,WAAW;AACb,uBAAmB;AACnB,aAAS,iCAAiC,SAAS;AAAA,EACrD,OAAO;AACL,aAAS,cAAc,UAAU;AAEjC,uBAAmB,OAAO,iBAAiB;AAAA,EAC7C;AACA,MAAI,eAAe,wBAAwB,WAAW;AACtD,MAAI,kBAAkB,mBAAmB,aAAa,YAAY;AAClE,MAAI,qBAAqB,IAAI;AAC7B,MAAI,kBAAyB,WAAW,kBAAkB,IAAI,qBAAqB,qBAA4B,MAAM,iCAAiC,iBAAiB,WAAW,IAAI;AACtL,MAAI,yBAAyB;AAAA,IAC3B,iBAAiB;AAAA,IACjB,eAAe,IAAI;AAAA,IACnB,cAAc,YAAY,UAAU;AAAA,IACpC;AAAA,IACA,yBAAyB,CAAC;AAAA,EAC5B;AACA,MAAI,SAAS,wBAAwB,QAAQ,sBAAsB;AACnE,MAAI,wBAAwB,kBAAkB,OAAO,YAAY,IAAI,uBAAuB,YAAY;AACxG,MAAI,QAAQ,CAAC,mBAAmB,cAAc,mBAAmB,MAAM,IAAI;AAC3E,MAAI,uBAAuB,gBAAgB,aAAa;AAAA,IACtD;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,IAAI,mBAAW,QAAQ,WAAW;AAC7C,OAAK,mBAAmB,oBAAoB;AAC5C,MAAI,iBAAiB,yBAAyB,QAAQ,0BAA0B,MAAM,IAAI,SAAU,SAAS,SAAS,WAAW,UAAU;AAEzI,WAAO,aAAa,wBAAwB,YAAY,KAAK,sBAAsB,SAAS,SAAS,WAAW,QAAQ;AAAA,EAC1H,IAAI;AACJ,OAAK,gBAAgB;AACrB,OAAK;AAAA;AAAA,IAEL,mBAAmB,SAAS;AAAA,IAAO;AAAA,IAAM;AAAA,EAAc;AACvD,SAAO;AACT;AACA,SAAS,0BAA0B,QAAQ;AACzC,MAAI,OAAO,iBAAiB,wBAAwB;AAClD,QAAI,aAAa,iBAAiB,OAAO,QAAQ,CAAC,CAAC;AACnD,WAAO,CAAQ,QAAQ,iBAAiB,UAAU,CAAC;AAAA,EACrD;AACF;AACA,SAAS,iBAAiB,KAAK;AAC7B,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,UAAU,IAAI,CAAC,KAAK,MAAM;AACvC;AAAA,EACF;AACA,SAAO,IAAI,CAAC;AACd;AACA,IAAO,2BAAQ;;;AC9Gf,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,MAAM,kBAAU;AACpB,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,MAAM;AAC5D,SAAS,mBAAmB,KAAK,UAAU,MAAM,OAAO,QAAQ;AAC9D,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,QAAQ;AAC/C,aAAO,IAAI,GAAG,EAAE;AAChB;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,SAAS,QAAQ;AACxD,aAAO,IAAI,GAAG,CAAC;AACf;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,UAAU,KAAK,IAAI,SAAS,CAAC;AAChD,aAAO,IAAI,IAAI,CAAC;AAChB;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,IAAI,SAAS,CAAC;AACxD,aAAO,IAAI,GAAG,CAAC;AACf;AAAA,EACJ;AACF;AACA,SAAS,kBAAkB,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,GAAG,GAAG,KAAK;AACpF,OAAK;AACL,OAAK;AACL,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,OAAK;AACL,OAAK;AAEL,MAAI,KAAK,IAAI,IAAI;AACjB,MAAI,KAAK,IAAI,IAAI;AACjB,MAAI,KAAK,IAAI,aAAa,QAAQ,IAAI,MAAM,MAAM;AAEhD,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,IAAI;AAAA,EACb;AACA,MAAI,eAAe;AACjB,QAAI,MAAM;AACV,iBAAa,gBAAgB,QAAQ;AACrC,eAAW,gBAAgB,GAAG;AAAA,EAChC,OAAO;AACL,iBAAa,gBAAgB,UAAU;AACvC,eAAW,gBAAgB,QAAQ;AAAA,EACrC;AACA,MAAI,aAAa,UAAU;AACzB,gBAAY;AAAA,EACd;AACA,MAAI,QAAQ,KAAK,MAAM,GAAG,CAAC;AAC3B,MAAI,QAAQ,GAAG;AACb,aAAS;AAAA,EACX;AACA,MAAI,SAAS,cAAc,SAAS,YAAY,QAAQ,OAAO,cAAc,QAAQ,OAAO,UAAU;AAEpG,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,IAAI;AAAA,EACb;AACA,MAAI,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI;AACpC,MAAI,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI;AACpC,MAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;AAClC,MAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;AAClC,MAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,KAAK,IAAI;AACX,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB,OAAO;AACL,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB;AACF;AACA,SAAS,mBAAmB,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,aAAa;AAClE,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,KAAK;AACf,MAAI,UAAU,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC7C,SAAO;AACP,SAAO;AAEP,MAAI,eAAe,KAAK,MAAM,KAAK;AACnC,MAAI,IAAI,eAAe;AACvB,MAAI,aAAa;AACf,QAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,EAChC;AACA,OAAK;AACL,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;AAC3B,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;AAC3B,SAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE;AAC5D;AACA,SAAS,mBAAmB,IAAI,IAAI,OAAO,QAAQ,GAAG,GAAG,KAAK;AAC5D,MAAI,QAAQ,GAAG;AACb,SAAK,KAAK;AACV,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,SAAS,GAAG;AACd,SAAK,KAAK;AACV,aAAS,CAAC;AAAA,EACZ;AACA,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC9C,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC9C,SAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE;AAC5D;AACA,IAAI,QAAQ,CAAC;AACb,SAAS,mBAAmB,IAAI,MAAM,KAAK;AACzC,MAAIC,QAAO,mBAAmB,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,KAAK;AACxF,MAAI,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,SAAOA;AACT;AAKA,SAAS,mBAAmB,IAAI,MAAM,KAAK;AACzC,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,OAAO,KAAK;AAChB,MAAI,IAAI,GAAG;AACX,MAAI,IAAI,GAAG;AACX,WAAS,IAAI,GAAG,IAAI,KAAK,UAAS;AAChC,QAAI,MAAM,KAAK,GAAG;AAClB,QAAI,MAAM,GAAG;AACX,WAAK,KAAK,CAAC;AACX,WAAK,KAAK,IAAI,CAAC;AACf,WAAK;AACL,WAAK;AAAA,IACP;AACA,QAAI,IAAI;AACR,YAAQ,KAAK;AAAA,MACX,KAAK,IAAI;AAGP,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK;AACL,aAAK;AACL;AAAA,MACF,KAAK,IAAI;AACP,YAAI,mBAAmB,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,OAAO,IAAI;AACtE,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AACP,YAAI,kBAAkB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK;AAC3G,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AACP,YAAI,sBAAsB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK;AACzF,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AAEP,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AAErB,aAAK;AACL,YAAI,gBAAgB,CAAC,EAAE,IAAI,KAAK,GAAG;AACnC,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAE5B,YAAI,KAAK,GAAG;AAEV,eAAK;AACL,eAAK;AAAA,QACP;AAEA,YAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,YAAI,kBAAkB,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,IAAI,GAAG,KAAK;AACpF,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC;AAAA,MACF,KAAK,IAAI;AACP,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,YAAI,mBAAmB,IAAI,IAAI,OAAO,QAAQ,GAAG,GAAG,KAAK;AACzD;AAAA,MACF,KAAK,IAAI;AACP,YAAI,mBAAmB,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,OAAO,IAAI;AACxD,aAAK;AACL,aAAK;AACL;AAAA,IACJ;AACA,QAAI,IAAI,SAAS;AACf,gBAAU;AACV,UAAI,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,OAAO,IAAI,cAAM;AAQd,SAAS,sBAAsB,QAAQ,gBAAgB;AAC5D,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,YAAY,OAAO,iBAAiB;AACxC,MAAI,QAAQ,OAAO,eAAe;AAElC,MAAI,EAAE,SAAS,YAAY;AACzB;AAAA,EACF;AACA,MAAI,mBAAmB,OAAO,uBAAuB,CAAC;AACtD,MAAI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,MAAI,cAAc,iBAAiB,cAAc;AACjD,MAAI,YAAY,MAAM,gBAAgB,EAAE,MAAM;AAC9C,YAAU,eAAe,MAAM,qBAAqB,CAAC;AACrD,MAAI,UAAU;AACd,MAAI,cAAc,iBAAiB;AACnC,MAAI,kBAAkB,OAAO,qBAAqB;AAClD,MAAI,0BAA0B,mBAAmB,OAAO,CAAC,GAAG,eAAe;AAC3E,MAAI,MAAM,eAAe,IAAI,SAAS,KAAK;AAC3C,MAAI,aAAa;AACf,QAAI,KAAK,WAAW;AAAA,EACtB;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,YAAY,YAAY,CAAC;AAC7B,uBAAmB,WAAW,GAAG,WAAW,KAAK,GAAG;AACpD,kBAAM,YAAY,KAAK,KAAK,KAAK,GAAG;AAEpC,QAAI,UAAU,uBAAuB;AAErC,QAAI,eAAe,OAAO,gBAAgB;AAC1C,QAAIA,QAAO,cAAc,YAAY,SAAS,GAAG,IAAI,kBAAkB,eAAO,mBAAmB,KAAK,OAAO,MAAM,GAAG,IAAI,mBAAmB,KAAK,cAAc,GAAG;AAEnK,QAAIA,QAAO,SAAS;AAClB,gBAAUA;AAEV,UAAI,UAAU,eAAe;AAC7B,UAAI,UAAU,eAAe;AAC7B,UAAI,QAAQ,OAAO,CAAC,CAAC;AACrB,UAAI,QAAQ,OAAO,CAAC,CAAC;AACrB,UAAI,QAAQ,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AACA,iBAAe,QAAQ,eAAe,IAAI,cAAc,CAAC;AACzD,YAAU,SAAS;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,SAAS,CAAC;AACd,IAAI,eAAe,IAAI,cAAM;AAMtB,SAAS,eAAe,YAAY,cAAc;AACvD,MAAI,EAAE,gBAAgB,OAAO,eAAe,IAAI;AAC9C;AAAA,EACF;AACA,iBAAe,eAAe,MAAM,KAAK;AAKzC,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,gBAAM,IAAI,KAAK,KAAK,GAAG;AACvB,gBAAM,IAAI,MAAM,KAAK,GAAG;AACxB,MAAI,OAAO,IAAI,IAAI;AACnB,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B;AAAA,EACF;AACA,MAAI,MAAM,IAAI,IAAI;AAClB,OAAK,MAAM,IAAI,IAAI;AACnB,MAAI,WAAW,IAAI,IAAI,IAAI;AAC3B,MAAI,kBAAkB,KAAK,IAAI,YAAY;AAC3C,MAAI,kBAAkB,UAAU;AAG9B,QAAI,IAAI,mBAAmB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClF,iBAAa,UAAU,MAAM;AAE7B,iBAAa,YAAY,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,YAAY,CAAC;AAEnE,QAAI,IAAI,IAAI,MAAM,IAAI,KAAK,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI;AAC/G,QAAI,MAAM,CAAC,GAAG;AACZ;AAAA,IACF;AACA,QAAI,IAAI,GAAG;AACT,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B,WAAW,IAAI,GAAG;AAChB,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B;AACA,iBAAa,QAAQ,WAAW,CAAC,CAAC;AAAA,EACpC;AACF;AAKO,SAAS,kBAAkB,YAAY,eAAe,iBAAiB;AAC5E,MAAI,EAAE,mBAAmB,OAAO,kBAAkB,IAAI;AACpD;AAAA,EACF;AACA,oBAAkB,kBAAkB,MAAM,KAAK;AAC/C,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,gBAAM,IAAI,KAAK,KAAK,GAAG;AACvB,gBAAM,IAAI,MAAM,KAAK,GAAG;AACxB,MAAI,OAAO,IAAI,IAAI;AACnB,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B;AAAA,EACF;AACA,MAAI,MAAM,IAAI,IAAI;AAClB,OAAK,MAAM,IAAI,IAAI;AACnB,MAAI,WAAW,IAAI,IAAI,aAAa;AACpC,MAAI,qBAAqB,KAAK,IAAI,eAAe;AACjD,MAAI,WAAW,oBAAoB;AAEjC,QAAI,IAAI,mBAAmB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClF,iBAAa,UAAU,MAAM;AAC7B,QAAI,UAAU,KAAK,KAAK;AACxB,QAAI,SAAS,KAAK,KAAK,KAAK,IAAI,aAAa,CAAC;AAC9C,QAAI,WAAW,UAAU,SAAS;AAClC,QAAI,YAAY,SAAS;AAEvB,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B,OAAO;AAEL,mBAAa,YAAY,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,QAAQ,CAAC;AAEnE,UAAI,IAAI,IAAI,MAAM,IAAI,KAAK,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI;AAC/G,UAAI,MAAM,CAAC,GAAG;AACZ;AAAA,MACF;AACA,UAAI,IAAI,GAAG;AACT,sBAAM,KAAK,cAAc,GAAG;AAAA,MAC9B,WAAW,IAAI,GAAG;AAChB,sBAAM,KAAK,cAAc,GAAG;AAAA,MAC9B;AAAA,IACF;AACA,iBAAa,QAAQ,WAAW,CAAC,CAAC;AAAA,EACpC;AACF;AACA,SAAS,kBAAkB,WAAW,QAAQ,WAAW,YAAY;AACnE,MAAI,WAAW,cAAc;AAC7B,MAAI,WAAW,WAAW,YAAY,UAAU,YAAY,SAAS;AAErE,WAAS,SAAS;AAElB,MAAI,SAAS,WAAW,IAAI,QAAQ;AACpC,MAAI,UAAU,WAAW,MAAM;AAC7B,aAAS;AAAA,EACX;AACA,WAAS,QAAQ,SAAS,SAAS,CAAC;AACpC,MAAI,SAAS,GAAG;AACd,aAAS,MAAM,SAAS;AAAA,EAC1B;AACA,MAAI,WAAW,WAAW,SAAS,WAAW,EAAE,aAAa;AAC7D,aAAW,UAAU,SAAS,QAAQ,IAAI,SAAS,QAAQ;AAC7D;AACA,SAAS,mBAAmB,MAAM,OAAO;AACvC,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,MAAM;AACnB,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,OAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC,MAAI,SAAS,KAAK,OAAO,UAAU,GAAG;AACpC,QAAI,OAAc,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3C,QAAI,OAAc,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3C,QAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC;AAAA,IACF;AACA,QAAI,UAAU,KAAK,IAAI,MAAM,IAAI,IAAI;AACrC,QAAI,YAAmB,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,IAAI;AACpE,QAAI,YAAmB,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,IAAI;AACpE,QAAI,YAAmB,KAAK,CAAC,GAAG,WAAW,WAAW,GAAG;AACzD,SAAK,cAAc,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AACrG,SAAK,cAAc,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,EACvG,OAAO;AACL,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AACF;AAIO,SAAS,kBAAkB,UAAU,cAAc,cAAc;AACtE,MAAI,YAAY,SAAS,iBAAiB;AAC1C,MAAI,QAAQ,SAAS,eAAe;AACpC,MAAI,CAAC,OAAO;AAEV,QAAI,WAAW;AACb,eAAS,oBAAoB;AAAA,IAC/B;AACA;AAAA,EACF;AACA,MAAI,cAAc,aAAa;AAC/B,MAAI,aAAa,YAAY,IAAI,MAAM;AACvC,MAAI,oBAAoB,MAAM;AAC9B,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAI,YAAY,eAAe,CAAC;AAChC,QAAI,aAAa,aAAa,SAAS;AACvC,QAAI,WAAW,cAAc;AAC7B,QAAI,YAAY;AACd,UAAI,YAAY,WAAW,IAAI,MAAM;AACrC,UAAI,iBAAiB,WAAW,oBAAoB,UAAU,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,EAAE,QAAQ,iBAAiB;AAC1I,UAAI,kBACD,CAAC,UAAU,WAAW,UAAU,GACjC;AACA,YAAI,WAAW,WAAW,YAAY,aAAa,UAAU,OAAO,SAAS;AAC7E,YAAI,UAAU;AACZ,mBAAS,SAAS;AAAA,QACpB;AACA,YAAI,CAAC,CAAC,WAAW;AACf,4BAAkB,WAAW,MAAM,WAAW,UAAU;AAAA,QAC1D;AACA;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,oBAAY,IAAI,iBAAS;AACzB,iBAAS,iBAAiB,SAAS;AAGnC,YAAI,CAAC,aAAa,qBAAqB,CAAC,aAAa;AACnD,4BAAkB,WAAW,MAAM,UAAU,aAAa,MAAM;AAAA,QAClE;AAEA,YAAI,SAAS,YAAY;AACvB,oBAAU,aAAa,SAAS;AAAA,QAClC;AAAA,MACF;AACA,wBAAkB,WAAW,OAAO,WAAW,UAAU;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,WAAW;AACb,aAAS,UAAU,OAAO,YAAY;AAEtC,cAAU,MAAM,OAAO;AACvB,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,kBAAkB,SAAS,sBAAsB,SAAS,uBAAuB,CAAC;AACtF,oBAAgB,YAAY,aAAa;AAEzC,cAAU,YAAY;AAAA,EACxB;AACF;AACO,SAAS,yBAAyB,WAAW,eAAe;AACjE,kBAAgB,iBAAiB;AACjC,MAAI,eAAe;AAAA,IACjB,QAAQ,UAAU,SAAS,aAAa;AAAA,EAC1C;AACA,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAI,YAAY,eAAe,CAAC;AAChC,iBAAa,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,aAAa,CAAC;AAAA,EACzE;AACA,SAAO;AACT;", "names": ["resultItem", "coordDim", "resultDimIdx", "i", "map", "CoordSysInfo", "dist"]}