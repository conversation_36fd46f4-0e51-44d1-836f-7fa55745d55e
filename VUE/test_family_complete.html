<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家庭成员管理完整测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .member-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .member-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        .member-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .member-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .member-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .member-actions {
            margin-top: 10px;
        }
        .member-actions button {
            font-size: 12px;
            padding: 5px 10px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <h1>家庭成员管理完整测试</h1>
    
    <div class="test-section">
        <h2>1. 获取家庭成员列表</h2>
        <button onclick="loadFamilyMembers()">加载家庭成员</button>
        <div id="load-result"></div>
        <div id="member-list" class="member-list"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 添加新成员测试</h2>
        <button onclick="testAddMember()">添加测试成员</button>
        <div id="add-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 编辑成员测试</h2>
        <p>选择一个成员进行编辑测试（需要先加载成员列表）</p>
        <div id="edit-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 删除成员测试</h2>
        <p>选择一个成员进行删除测试（需要先加载成员列表）</p>
        <div id="delete-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 功能跳转测试</h2>
        <button onclick="testHealthScan()">测试健康检测跳转</button>
        <button onclick="testReportView()">测试报告查看跳转</button>
        <div id="navigation-result"></div>
    </div>

    <script>
        const userId = 9;
        const token = localStorage.getItem('health_detection_token') || 'test-token';
        let familyMembers = [];
        
        async function loadFamilyMembers() {
            const resultDiv = document.getElementById('load-result');
            const memberListDiv = document.getElementById('member-list');
            
            try {
                const response = await fetch(`/api/v1/users/${userId}/familylist`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    familyMembers = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 加载成功，共 ${familyMembers.length} 个家庭成员
                        </div>
                    `;
                    
                    // 渲染成员列表
                    renderMemberList(memberListDiv);
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ 加载失败: ${response.status} ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        function renderMemberList(container) {
            container.innerHTML = familyMembers.map(member => `
                <div class="member-card">
                    <div class="member-header">
                        <div class="member-name">${member.name}</div>
                        <div class="member-actions">
                            <button onclick="editMember(${member.fuid})">编辑</button>
                            <button class="danger" onclick="deleteMember(${member.fuid})">删除</button>
                        </div>
                    </div>
                    <div class="member-info">关系: ${member.relationship}</div>
                    <div class="member-info">性别: ${member.gender}</div>
                    <div class="member-info">年龄: ${new Date().getFullYear() - member.birth_year}岁</div>
                    <div class="member-info">身高: ${member.height}cm</div>
                    <div class="member-info">体重: ${member.weight}kg</div>
                    <div class="member-info">BMI: ${(member.weight / Math.pow(member.height / 100, 2)).toFixed(1)}</div>
                    <div class="member-actions">
                        <button onclick="healthScan(${member.fuid})">健康检测</button>
                        <button onclick="viewReports(${member.fuid})">查看报告</button>
                    </div>
                </div>
            `).join('');
        }
        
        async function testAddMember() {
            const resultDiv = document.getElementById('add-result');
            
            const testMember = {
                name: '测试成员' + Date.now(),
                relationship: '其他',
                gender: '男',
                birth_year: 1990,
                height: 175,
                weight: 70,
                uid: userId
            };
            
            try {
                const response = await fetch('/api/v1/users/familylist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(testMember)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 添加成员成功<br>
                            新成员ID: ${result.fuid || '未返回'}<br>
                            <button onclick="loadFamilyMembers()">刷新列表</button>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ 添加失败: ${response.status} ${errorData.detail || response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function editMember(fuid) {
            const resultDiv = document.getElementById('edit-result');
            const member = familyMembers.find(m => m.fuid === fuid);
            
            if (!member) {
                resultDiv.innerHTML = '<div class="test-result error">❌ 未找到成员</div>';
                return;
            }
            
            const updatedMember = {
                ...member,
                name: member.name + '(已编辑)',
                height: member.height + 1,
                weight: member.weight + 1
            };
            
            try {
                const response = await fetch(`/api/v1/users/familylist/${fuid}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(updatedMember)
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 编辑成员成功: ${member.name}<br>
                            <button onclick="loadFamilyMembers()">刷新列表</button>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ 编辑失败: ${response.status} ${errorData.detail || response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function deleteMember(fuid) {
            const resultDiv = document.getElementById('delete-result');
            const member = familyMembers.find(m => m.fuid === fuid);
            
            if (!member) {
                resultDiv.innerHTML = '<div class="test-result error">❌ 未找到成员</div>';
                return;
            }
            
            if (!confirm(`确定要删除成员"${member.name}"吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/users/familylist/${fuid}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 删除成员成功: ${member.name}<br>
                            <button onclick="loadFamilyMembers()">刷新列表</button>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ 删除失败: ${response.status} ${errorData.detail || response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        function healthScan(fuid) {
            const url = `/scan?fuid=${fuid}`;
            window.open(url, '_blank');
        }
        
        function viewReports(fuid) {
            const url = `/report?fuid=${fuid}`;
            window.open(url, '_blank');
        }
        
        function testHealthScan() {
            const resultDiv = document.getElementById('navigation-result');
            if (familyMembers.length > 0) {
                const member = familyMembers[0];
                healthScan(member.fuid);
                resultDiv.innerHTML = `
                    <div class="test-result info">
                        ℹ️ 已打开健康检测页面 (成员: ${member.name})
                    </div>
                `;
            } else {
                resultDiv.innerHTML = '<div class="test-result error">❌ 请先加载家庭成员列表</div>';
            }
        }
        
        function testReportView() {
            const resultDiv = document.getElementById('navigation-result');
            if (familyMembers.length > 0) {
                const member = familyMembers[0];
                viewReports(member.fuid);
                resultDiv.innerHTML = `
                    <div class="test-result info">
                        ℹ️ 已打开报告查看页面 (成员: ${member.name})
                    </div>
                `;
            } else {
                resultDiv.innerHTML = '<div class="test-result error">❌ 请先加载家庭成员列表</div>';
            }
        }
        
        // 页面加载时自动加载家庭成员
        window.onload = () => {
            loadFamilyMembers();
        };
    </script>
</body>
</html>
