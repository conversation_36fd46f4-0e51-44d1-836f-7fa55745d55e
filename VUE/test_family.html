<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家庭成员功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>家庭成员功能测试</h1>
    
    <div class="test-section">
        <h2>1. API连接测试</h2>
        <button onclick="testFamilyAPI()">测试家庭成员API</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 添加成员表单测试</h2>
        <form id="addMemberForm">
            <div class="form-group">
                <label for="name">姓名:</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="relationship">关系:</label>
                <select id="relationship" name="relationship" required>
                    <option value="">请选择关系</option>
                    <option value="父亲">父亲</option>
                    <option value="母亲">母亲</option>
                    <option value="配偶">配偶</option>
                    <option value="儿子">儿子</option>
                    <option value="女儿">女儿</option>
                    <option value="兄弟">兄弟</option>
                    <option value="姐妹">姐妹</option>
                    <option value="其他">其他</option>
                </select>
            </div>
            <div class="form-group">
                <label for="gender">性别:</label>
                <select id="gender" name="gender" required>
                    <option value="">请选择性别</option>
                    <option value="男">男</option>
                    <option value="女">女</option>
                </select>
            </div>
            <div class="form-group">
                <label for="birth_year">出生年份:</label>
                <input type="number" id="birth_year" name="birth_year" min="1900" max="2024" required>
            </div>
            <div class="form-group">
                <label for="height">身高(cm):</label>
                <input type="number" id="height" name="height" min="50" max="250" required>
            </div>
            <div class="form-group">
                <label for="weight">体重(kg):</label>
                <input type="number" id="weight" name="weight" min="10" max="300" required>
            </div>
            <button type="button" onclick="testAddMember()">测试添加成员</button>
        </form>
        <div id="add-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 表单验证测试</h2>
        <button onclick="testValidation()">测试表单验证</button>
        <div id="validation-result"></div>
    </div>

    <script>
        // 模拟用户ID（实际应用中从localStorage获取）
        const userId = 9;
        const token = localStorage.getItem('health_detection_token') || 'test-token';
        
        async function testFamilyAPI() {
            const resultDiv = document.getElementById('api-result');
            
            try {
                const response = await fetch(`/api/v1/users/${userId}/familylist`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ API连接成功<br>
                            📊 家庭成员数量: ${data.length}<br>
                            📋 数据: ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ API请求失败: ${response.status} ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testAddMember() {
            const resultDiv = document.getElementById('add-result');
            const form = document.getElementById('addMemberForm');
            const formData = new FormData(form);
            
            // 验证表单
            if (!form.checkValidity()) {
                resultDiv.innerHTML = '<div class="test-result error">❌ 请填写所有必填字段</div>';
                return;
            }
            
            const memberData = {
                name: formData.get('name'),
                relationship: formData.get('relationship'),
                gender: formData.get('gender'),
                birth_year: parseInt(formData.get('birth_year')),
                height: parseInt(formData.get('height')),
                weight: parseInt(formData.get('weight')),
                uid: userId
            };
            
            try {
                const response = await fetch('/api/v1/users/familylist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(memberData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 添加成员成功<br>
                            📋 返回数据: ${JSON.stringify(result, null, 2)}
                        </div>
                    `;
                    form.reset();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ 添加失败: ${response.status} ${errorData.detail || response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        function testValidation() {
            const resultDiv = document.getElementById('validation-result');
            const form = document.getElementById('addMemberForm');
            
            // 测试各种验证场景
            const testCases = [
                { name: '', expected: '姓名不能为空' },
                { name: 'a', expected: '姓名太短' },
                { name: 'a'.repeat(20), expected: '姓名太长' },
                { height: 30, expected: '身高太小' },
                { height: 300, expected: '身高太大' },
                { weight: 5, expected: '体重太小' },
                { weight: 400, expected: '体重太大' },
                { birth_year: 1800, expected: '出生年份太早' },
                { birth_year: 2030, expected: '出生年份太晚' }
            ];
            
            let results = [];
            
            testCases.forEach((testCase, index) => {
                const isValid = validateField(testCase);
                results.push(`测试 ${index + 1}: ${isValid ? '✅' : '❌'} ${Object.keys(testCase)[0]} = ${Object.values(testCase)[0]}`);
            });
            
            resultDiv.innerHTML = `
                <div class="test-result info">
                    📝 表单验证测试结果:<br>
                    ${results.join('<br>')}
                </div>
            `;
        }
        
        function validateField(testData) {
            if ('name' in testData) {
                const name = testData.name;
                return name.length >= 2 && name.length <= 10;
            }
            if ('height' in testData) {
                const height = testData.height;
                return height >= 50 && height <= 250;
            }
            if ('weight' in testData) {
                const weight = testData.weight;
                return weight >= 10 && weight <= 300;
            }
            if ('birth_year' in testData) {
                const year = testData.birth_year;
                const currentYear = new Date().getFullYear();
                return year >= (currentYear - 120) && year <= currentYear;
            }
            return true;
        }
        
        // 页面加载时自动测试API
        window.onload = () => {
            testFamilyAPI();
        };
    </script>
</body>
</html>
