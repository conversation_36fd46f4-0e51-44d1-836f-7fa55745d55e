<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康扫描测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        video {
            max-width: 400px;
            max-height: 300px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>健康扫描功能测试</h1>
    
    <div class="test-section">
        <h2>1. 摄像头权限测试</h2>
        <button onclick="testCamera()">测试摄像头</button>
        <div id="camera-result"></div>
        <div class="video-container">
            <video id="testVideo" autoplay muted playsinline style="display: none;"></video>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. 录制功能测试</h2>
        <button onclick="startRecording()" id="recordBtn">开始录制</button>
        <button onclick="stopRecording()" id="stopBtn" disabled>停止录制</button>
        <div id="recording-result"></div>
        <div class="video-container">
            <video id="playbackVideo" controls style="display: none;"></video>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. 视频尺寸测试</h2>
        <div id="size-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 移动端适配测试</h2>
        <button onclick="testResponsive()">测试响应式</button>
        <div id="responsive-result"></div>
    </div>

    <script>
        let mediaStream = null;
        let mediaRecorder = null;
        let recordedChunks = [];
        
        async function testCamera() {
            const resultDiv = document.getElementById('camera-result');
            const video = document.getElementById('testVideo');
            
            try {
                const constraints = {
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'user'
                    },
                    audio: false
                };
                
                mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = mediaStream;
                video.style.display = 'block';
                
                resultDiv.innerHTML = '<div class="test-result success">✅ 摄像头启动成功</div>';
                
                // 测试视频尺寸
                video.onloadedmetadata = () => {
                    const sizeDiv = document.getElementById('size-result');
                    sizeDiv.innerHTML = `
                        <div class="test-result info">
                            📐 视频尺寸: ${video.videoWidth} x ${video.videoHeight}<br>
                            📱 显示尺寸: ${video.clientWidth} x ${video.clientHeight}<br>
                            📏 宽高比: ${(video.videoWidth / video.videoHeight).toFixed(2)}
                        </div>
                    `;
                };
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ 摄像头启动失败: ${error.message}</div>`;
            }
        }
        
        async function startRecording() {
            const resultDiv = document.getElementById('recording-result');
            
            if (!mediaStream) {
                resultDiv.innerHTML = '<div class="test-result error">❌ 请先启动摄像头</div>';
                return;
            }
            
            try {
                recordedChunks = [];
                
                const options = {
                    mimeType: 'video/webm;codecs=vp9',
                    videoBitsPerSecond: 2500000
                };
                
                mediaRecorder = new MediaRecorder(mediaStream, options);
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: 'video/webm' });
                    const url = URL.createObjectURL(blob);
                    
                    const playbackVideo = document.getElementById('playbackVideo');
                    playbackVideo.src = url;
                    playbackVideo.style.display = 'block';
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 录制完成<br>
                            📁 文件大小: ${(blob.size / 1024 / 1024).toFixed(2)} MB<br>
                            🎬 格式: ${blob.type}
                        </div>
                    `;
                    
                    document.getElementById('recordBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                };
                
                mediaRecorder.start();
                resultDiv.innerHTML = '<div class="test-result info">🔴 录制中...</div>';
                
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ 录制失败: ${error.message}</div>`;
            }
        }
        
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
            }
        }
        
        function testResponsive() {
            const resultDiv = document.getElementById('responsive-result');
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            
            let deviceType = '';
            if (screenWidth <= 480) {
                deviceType = '📱 小屏手机';
            } else if (screenWidth <= 768) {
                deviceType = '📱 平板/大屏手机';
            } else {
                deviceType = '💻 桌面端';
            }
            
            resultDiv.innerHTML = `
                <div class="test-result info">
                    ${deviceType}<br>
                    📐 屏幕尺寸: ${screenWidth} x ${screenHeight}<br>
                    📱 设备像素比: ${window.devicePixelRatio}<br>
                    🌐 用户代理: ${navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备'}
                </div>
            `;
        }
        
        // 页面加载时自动测试响应式
        window.onload = () => {
            testResponsive();
        };
        
        // 窗口大小改变时重新测试
        window.onresize = () => {
            testResponsive();
        };
    </script>
</body>
</html>
