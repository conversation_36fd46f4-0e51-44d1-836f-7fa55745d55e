{"name": "health-detection-frontend", "version": "1.0.0", "description": "健康检测系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.0", "echarts": "^5.6.0", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0"}, "engines": {"node": ">=16.0.0"}}