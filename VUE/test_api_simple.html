<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    
    <div>
        <h2>1. 测试健康检查接口</h2>
        <button onclick="testHealthCheck()">测试 /api/v1/health/hello</button>
        <div id="health-result"></div>
    </div>
    
    <div>
        <h2>2. 测试登录接口</h2>
        <button onclick="testLogin()">测试登录 (<EMAIL> / string)</button>
        <div id="login-result"></div>
    </div>
    
    <div>
        <h2>3. 测试家庭成员接口</h2>
        <button onclick="testFamilyList()">测试家庭成员列表</button>
        <div id="family-result"></div>
    </div>
    
    <div>
        <h2>4. 测试添加家庭成员</h2>
        <button onclick="testAddFamily()">测试添加家庭成员</button>
        <div id="add-family-result"></div>
    </div>

    <script>
        let authToken = null;
        let currentUser = null;

        async function testHealthCheck() {
            const resultDiv = document.getElementById('health-result');
            
            try {
                const response = await fetch('/api/v1/health/hello');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 健康检查成功<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 健康检查失败: ${error.message}
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            
            try {
                const response = await fetch('/api/v1/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'string'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    currentUser = data;
                    
                    // 保存到localStorage
                    localStorage.setItem('health_detection_token', authToken);
                    localStorage.setItem('health_detection_user', JSON.stringify(currentUser));
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 登录成功<br>
                            用户: ${data.name} (UID: ${data.uid})<br>
                            Token: ${data.token.substring(0, 50)}...<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 登录失败: ${error.message}
                    </div>
                `;
            }
        }

        async function testFamilyList() {
            const resultDiv = document.getElementById('family-result');
            
            if (!authToken) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 请先登录获取Token
                    </div>
                `;
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/users/${currentUser.uid}/familylist`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 获取家庭成员成功 (共${data.length}个成员)<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 获取家庭成员失败: ${error.message}
                    </div>
                `;
            }
        }

        async function testAddFamily() {
            const resultDiv = document.getElementById('add-family-result');
            
            if (!authToken) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 请先登录获取Token
                    </div>
                `;
                return;
            }
            
            const testMember = {
                relationship: "其他",
                name: "API测试成员" + Date.now(),
                gender: "男",
                height: 175,
                weight: 70,
                birth_year: 1990,
                avatar_url: "test.png",
                uid: currentUser.uid
            };
            
            try {
                const response = await fetch('/api/v1/users/addfamily', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testMember)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 添加家庭成员成功<br>
                            成员: ${testMember.name}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ 添加家庭成员失败: ${error.message}
                    </div>
                `;
            }
        }

        // 页面加载时检查是否有保存的token
        window.onload = () => {
            const savedToken = localStorage.getItem('health_detection_token');
            const savedUser = localStorage.getItem('health_detection_user');
            
            if (savedToken && savedUser) {
                authToken = savedToken;
                currentUser = JSON.parse(savedUser);
                
                document.getElementById('login-result').innerHTML = `
                    <div class="test-result info">
                        ℹ️ 已从localStorage加载登录信息<br>
                        用户: ${currentUser.name} (UID: ${currentUser.uid})
                    </div>
                `;
            }
        };
    </script>
</body>
</html>
