/**
 * 头像匹配工具函数
 * 根据家庭成员的性别、年龄等信息智能匹配合适的头像
 */

/**
 * 计算年龄
 * @param {number} birthYear - 出生年份
 * @returns {number} 年龄
 */
export function calculateAge(birthYear) {
  if (!birthYear) return 0
  return new Date().getFullYear() - birthYear
}

/**
 * 根据家庭成员信息获取匹配的头像URL
 * @param {Object} member - 家庭成员信息
 * @param {string} member.gender - 性别 ('男' | '女')
 * @param {number} member.birth_year - 出生年份
 * @param {string} member.relationship - 家庭关系
 * @param {string} resolution - 分辨率 ('1x' | '2x' | '3x')
 * @returns {string} 头像URL
 */
export function getAvatarUrl(member, resolution = '1x') {
  if (!member) {
    return getDefaultAvatar(resolution)
  }

  const { gender, birth_year, relationship } = member
  const age = birth_year ? calculateAge(birth_year) : 0

  // 根据关系和年龄智能匹配头像
  let avatarType = 'default'

  if (gender === '女' || gender === '女性') {
    if (age >= 60 || relationship === '奶奶' || relationship === '外婆' || relationship === '母亲' && age >= 50) {
      avatarType = 'elderly_female'
    } else {
      avatarType = 'young_female'
    }
  } else if (gender === '男' || gender === '男性') {
    if (age >= 60 || relationship === '爷爷' || relationship === '外公' || relationship === '父亲' && age >= 50) {
      avatarType = 'elderly_male'
    } else {
      avatarType = 'young_male'
    }
  } else {
    // 如果性别未知，根据关系推断
    if (relationship === '父亲' || relationship === '爷爷' || relationship === '外公') {
      avatarType = age >= 60 ? 'elderly_male' : 'young_male'
    } else if (relationship === '母亲' || relationship === '奶奶' || relationship === '外婆') {
      avatarType = age >= 60 ? 'elderly_female' : 'young_female'
    } else if (relationship === '儿子') {
      avatarType = 'young_male'
    } else if (relationship === '女儿') {
      avatarType = 'young_female'
    } else {
      // 默认使用年轻男性头像
      avatarType = 'young_male'
    }
  }

  return getAvatarByType(avatarType, resolution)
}

/**
 * 根据头像类型获取头像URL
 * @param {string} type - 头像类型
 * @param {string} resolution - 分辨率
 * @returns {string} 头像URL
 */
export function getAvatarByType(type, resolution = '1x') {
  const avatarMap = {
    'young_male': '卡通头像_男士',
    'young_female': '卡通头像_女生',
    'elderly_male': '卡通头像_老头',
    'elderly_female': '卡通头像_老太太',
    'default': '卡通头像_男士'
  }

  const avatarName = avatarMap[type] || avatarMap['default']
  const suffix = resolution === '1x' ? '' : `@${resolution}`
  
  return `/avatar/${avatarName}${suffix}.png`
}

/**
 * 获取默认头像
 * @param {string} resolution - 分辨率
 * @returns {string} 默认头像URL
 */
export function getDefaultAvatar(resolution = '1x') {
  return getAvatarByType('default', resolution)
}

/**
 * 获取所有可用的头像选项
 * @param {string} resolution - 分辨率
 * @returns {Array} 头像选项数组
 */
export function getAllAvatarOptions(resolution = '1x') {
  return [
    {
      type: 'young_male',
      name: '年轻男性',
      url: getAvatarByType('young_male', resolution),
      description: '适合年轻男性用户'
    },
    {
      type: 'young_female',
      name: '年轻女性',
      url: getAvatarByType('young_female', resolution),
      description: '适合年轻女性用户'
    },
    {
      type: 'elderly_male',
      name: '年长男性',
      url: getAvatarByType('elderly_male', resolution),
      description: '适合年长男性用户'
    },
    {
      type: 'elderly_female',
      name: '年长女性',
      url: getAvatarByType('elderly_female', resolution),
      description: '适合年长女性用户'
    }
  ]
}

/**
 * 根据性别和年龄推荐头像
 * @param {string} gender - 性别
 * @param {number} age - 年龄
 * @returns {Array} 推荐的头像选项
 */
export function getRecommendedAvatars(gender, age) {
  const allOptions = getAllAvatarOptions()
  
  if (gender === '女' || gender === '女性') {
    if (age >= 60) {
      return [allOptions[3], allOptions[1]] // 年长女性优先，年轻女性备选
    } else {
      return [allOptions[1], allOptions[3]] // 年轻女性优先，年长女性备选
    }
  } else if (gender === '男' || gender === '男性') {
    if (age >= 60) {
      return [allOptions[2], allOptions[0]] // 年长男性优先，年轻男性备选
    } else {
      return [allOptions[0], allOptions[2]] // 年轻男性优先，年长男性备选
    }
  } else {
    // 性别未知时返回所有选项
    return allOptions
  }
}

/**
 * 验证头像URL是否有效
 * @param {string} url - 头像URL
 * @returns {Promise<boolean>} 是否有效
 */
export async function validateAvatarUrl(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    console.error('头像URL验证失败:', error)
    return false
  }
}

/**
 * 获取高分辨率头像URL（用于高DPI屏幕）
 * @param {Object} member - 家庭成员信息
 * @returns {string} 高分辨率头像URL
 */
export function getHighResAvatarUrl(member) {
  // 检测设备像素比
  const devicePixelRatio = window.devicePixelRatio || 1
  
  if (devicePixelRatio >= 3) {
    return getAvatarUrl(member, '3x')
  } else if (devicePixelRatio >= 2) {
    return getAvatarUrl(member, '2x')
  } else {
    return getAvatarUrl(member, '1x')
  }
}

/**
 * 预加载头像资源
 * @param {Array} members - 家庭成员列表
 * @returns {Promise<void>}
 */
export async function preloadAvatars(members) {
  const promises = members.map(member => {
    const avatarUrl = getHighResAvatarUrl(member)
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => resolve() // 即使失败也继续
      img.src = avatarUrl
    })
  })
  
  await Promise.all(promises)
}
