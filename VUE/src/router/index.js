import { createRouter, createWebHistory } from 'vue-router'
import Test from '@/views/Test.vue'

// 简化的路由配置用于测试
const routes = [
  {
    path: '/',
    name: 'Test',
    component: Test
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 最简单的路由守卫
router.beforeEach((to, from, next) => {
  document.title = '健康检测系统'
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  ElMessage.error('页面加载失败，请刷新重试')
})

export default router

// 导出路由配置供其他地方使用
export { routes }
