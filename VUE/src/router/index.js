import { createRouter, createWebHistory } from 'vue-router'
import Login from '@/views/Login.vue'
import Register from '@/views/Register.vue'
import Home from '@/views/Home.vue'
import Scan from '@/views/Scan.vue'
import Profile from '@/views/Profile.vue'
import Family from '@/views/Family.vue'
import Report from '@/views/Report.vue'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: true,
      icon: 'House'
    }
  },
  {
    path: '/test',
    name: 'HomeTest',
    component: () => import('@/views/HomeTest.vue'),
    meta: {
      title: '测试页面',
      requiresAuth: true,
      icon: 'House'
    }
  },
  {
    path: '/scan',
    name: 'Scan',
    component: Scan,
    meta: {
      title: '健康扫描',
      requiresAuth: true,
      icon: 'VideoCamera'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      requiresAuth: true,
      icon: 'User'
    }
  },
  {
    path: '/family',
    name: 'Family',
    component: Family,
    meta: {
      title: '家庭成员',
      requiresAuth: true,
      icon: 'UserFilled'
    }
  },
  {
    path: '/report/:reportId?',
    name: 'Report',
    component: Report,
    meta: {
      title: '健康报告',
      requiresAuth: true,
      icon: 'Document'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/login'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 健康检测系统`
  } else {
    document.title = '健康检测系统'
  }

  // 检查认证状态
  if (to.meta.requiresAuth) {
    // 检查是否有token
    const token = localStorage.getItem('health_detection_token')
    const userInfo = localStorage.getItem('health_detection_user')

    console.log('路由守卫检查:', {
      path: to.path,
      requiresAuth: to.meta.requiresAuth,
      token: !!token,
      userInfo: !!userInfo
    })

    if (!token || !userInfo) {
      console.log('重定向到登录页面')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 已登录用户访问登录/注册页面时重定向到首页
  if (to.meta.hideForAuth) {
    const token = localStorage.getItem('health_detection_token')
    if (token) {
      next('/home')
      return
    }
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  ElMessage.error('页面加载失败，请刷新重试')
})

export default router

// 导出路由配置供其他地方使用
export { routes }
