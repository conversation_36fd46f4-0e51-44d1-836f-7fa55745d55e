<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="logo-section">
          <el-icon class="logo-icon" :size="28" color="#409eff">
            <Monitor />
          </el-icon>
          <h1 class="app-title">健康检测系统</h1>
        </div>

        <!-- 导航菜单 -->
        <el-menu
          :default-active="activeMenu"
          mode="horizontal"
          class="nav-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="/home">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="/scan">
            <el-icon><VideoCamera /></el-icon>
            <span>健康扫描</span>
          </el-menu-item>
          <el-menu-item index="/family">
            <el-icon><UserFilled /></el-icon>
            <span>家庭成员</span>
          </el-menu-item>
        </el-menu>

        <!-- 用户信息和操作 -->
        <div class="user-section">
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userStore.userName || '用户' }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <div class="content-wrapper">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </el-main>

    <!-- 底部信息 -->
    <el-footer class="footer">
      <div class="footer-content">
        <p>&copy; 2025 健康检测系统. 基于rPPG技术的视频健康分析平台</p>
      </div>
    </el-footer>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import {
  Monitor,
  House,
  VideoCamera,
  UserFilled,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 用户头像（暂时使用默认）
const userAvatar = computed(() => {
  return '' // 可以从用户信息中获取头像URL
})

// 处理菜单选择
const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      // 暂时跳转到个人中心
      router.push('/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '退出确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // 执行退出登录
        await userStore.logout()

        // 强制跳转到登录页面
        await router.replace('/login')

        // 强制刷新页面确保完全清理
        window.location.reload()

      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
          ElMessage.error('退出登录失败，请重试')
        }
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.nav-menu {
  flex: 1;
  margin: 0 40px;
  border-bottom: none;
}

.nav-menu .el-menu-item {
  height: 60px;
  line-height: 60px;
  border-bottom: none;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.dropdown-icon {
  font-size: 12px;
  color: #909399;
}

.main-content {
  flex: 1;
  padding: 0;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.content-wrapper {
  min-height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.footer {
  background: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 0;
  height: 50px !important;
}

.footer-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }
  
  .nav-menu {
    margin: 0 20px;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .content-wrapper {
    padding: 15px;
  }
  
  .username {
    display: none;
  }
}

@media (max-width: 480px) {
  .logo-section .app-title {
    display: none;
  }
  
  .nav-menu {
    margin: 0 10px;
  }
  
  .nav-menu .el-menu-item span {
    display: none;
  }
}
</style>
