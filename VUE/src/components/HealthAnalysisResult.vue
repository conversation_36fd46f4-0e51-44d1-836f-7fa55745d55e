<template>
  <div class="health-analysis-result">
    <!-- 基本信息卡片 -->
    <div class="basic-info-section">
      <h3>📋 基本信息</h3>
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :lg="4" v-for="item in basicInfo" :key="item.key">
          <div class="info-card">
            <div class="info-label">{{ item.label }}</div>
            <div class="info-value">{{ item.value }}{{ item.unit }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 生理指标卡片 -->
    <div class="metrics-section">
      <h3>💓 生理指标</h3>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6" v-for="metric in physiologicalMetrics" :key="metric.key">
          <div class="metric-card" :class="getMetricStatusClass(metric.status)">
            <div class="metric-icon">{{ metric.icon }}</div>
            <div class="metric-info">
              <h4>{{ metric.label }}</h4>
              <p class="metric-value">{{ metric.value }} <span>{{ metric.unit }}</span></p>
              <p class="metric-status" :class="metric.status">{{ getStatusText(metric.status) }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 风险评估 -->
    <div class="risk-assessment-section">
      <h3>⚠️ 风险评估</h3>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" v-for="risk in riskAssessments" :key="risk.key">
          <div class="risk-card" :class="getRiskLevelClass(risk.level)">
            <div class="risk-icon">{{ risk.icon }}</div>
            <div class="risk-info">
              <h4>{{ risk.label }}</h4>
              <p class="risk-level">{{ risk.level }}</p>
              <p class="risk-description">{{ getRiskDescription(risk.level) }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- HRV分析 -->
    <div class="hrv-section" v-if="hrvData">
      <h3>📊 心率变异性分析 (HRV)</h3>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" v-for="domain in hrvDomains" :key="domain.key">
          <div class="hrv-domain-card">
            <h4>{{ domain.label }}</h4>
            <div class="hrv-metrics">
              <div v-for="metric in domain.metrics" :key="metric.key" class="hrv-metric">
                <span class="hrv-metric-name">{{ metric.name }}:</span>
                <span class="hrv-metric-value">{{ metric.value }} {{ metric.unit }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- BVP波形图表 -->
    <div class="bvp-chart-section" v-if="bvpData">
      <h3>📈 BVP波形图</h3>
      <div class="chart-container">
        <v-chart 
          ref="bvpChart" 
          :option="bvpChartOption" 
          :style="{ height: '400px', width: '100%' }"
          autoresize
        />
      </div>
      <div class="chart-info">
        <p><strong>采样率:</strong> {{ bvpData.sampling_rate }} Hz</p>
        <p><strong>数据点数:</strong> {{ bvpData.bvp?.length || 0 }}</p>
        <p><strong>时长:</strong> {{ ((bvpData.bvp?.length || 0) / bvpData.sampling_rate).toFixed(1) }} 秒</p>
      </div>
    </div>

    <!-- 信号质量 -->
    <div class="signal-quality-section" v-if="signalQuality !== null">
      <h3>📶 信号质量</h3>
      <div class="quality-indicator">
        <el-progress 
          :percentage="Math.round(signalQuality * 100)" 
          :color="getQualityColor(signalQuality)"
          :stroke-width="20"
          text-inside
        />
        <p class="quality-text">{{ getQualityText(signalQuality) }}</p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="result-actions">
      <el-button type="primary" size="large" @click="exportToPDF" :loading="isExporting">
        <el-icon><Document /></el-icon>
        导出PDF报告
      </el-button>
      <el-button type="success" size="large" @click="saveToHistory">
        <el-icon><FolderAdd /></el-icon>
        保存到历史
      </el-button>
      <el-button size="large" @click="shareReport">
        <el-icon><Share /></el-icon>
        分享报告
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, FolderAdd, Share } from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { 
  TitleComponent, 
  TooltipComponent, 
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
])

// Props
const props = defineProps({
  analysisData: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['save-report', 'share-report'])

// 响应式数据
const isExporting = ref(false)
const bvpChart = ref(null)

// 计算属性
const basicInfo = computed(() => {
  const data = props.analysisData
  return [
    { key: 'name', label: '姓名', value: data.name?.value || '未知', unit: '' },
    { key: 'gender', label: '性别', value: data.gender?.value || '未知', unit: '' },
    { key: 'age', label: '年龄', value: data.age?.value || '未知', unit: data.age?.unit || '' },
    { key: 'height', label: '身高', value: data.height?.value || '未知', unit: data.height?.unit || '' },
    { key: 'weight', label: '体重', value: data.weight?.value || '未知', unit: data.weight?.unit || '' },
    { key: 'bmi', label: 'BMI', value: data.bmi?.value || '未知', unit: data.bmi?.unit || '' }
  ]
})

const physiologicalMetrics = computed(() => {
  const data = props.analysisData
  return [
    {
      key: 'heart_rate',
      icon: '❤️',
      label: '心率',
      value: formatValue(data.heart_rate?.value),
      unit: data.heart_rate?.unit || 'bpm',
      status: getHeartRateStatus(data.heart_rate?.value)
    },
    {
      key: 'blood_pressure',
      icon: '🩸',
      label: '血压',
      value: data.blood_pressure?.value ? 
        `${formatValue(data.blood_pressure.value.SBP)}/${formatValue(data.blood_pressure.value.DBP)}` : 
        '未知',
      unit: data.blood_pressure?.unit || 'mmHg',
      status: getBloodPressureStatus(data.blood_pressure?.value)
    },
    {
      key: 'spo2',
      icon: '🫁',
      label: '血氧饱和度',
      value: formatValue(data.spo2?.value),
      unit: data.spo2?.unit || '%',
      status: getSpO2Status(data.spo2?.value)
    },
    {
      key: 'breathing_rate',
      icon: '💨',
      label: '呼吸频率',
      value: formatValue(data.breathing_rate?.value),
      unit: data.breathing_rate?.unit || '次/分',
      status: getBreathingRateStatus(data.breathing_rate?.value)
    }
  ]
})

const riskAssessments = computed(() => {
  const data = props.analysisData
  return [
    {
      key: 'cardiac_risk',
      icon: '💔',
      label: '心脏风险',
      level: data.cardiac_risk?.value || '未知'
    },
    {
      key: 'brain_risk',
      icon: '🧠',
      label: '脑风险',
      level: data.brain_risk?.value || '未知'
    },
    {
      key: 'afib',
      icon: '⚡',
      label: '房颤风险',
      level: data.afib?.value || '未知'
    }
  ]
})

const hrvData = computed(() => {
  return props.analysisData.hrv
})

const hrvDomains = computed(() => {
  if (!hrvData.value) return []
  
  const domains = []
  
  if (hrvData.value.time_domain?.value) {
    domains.push({
      key: 'time_domain',
      label: '时域指标',
      metrics: Object.entries(hrvData.value.time_domain.value).map(([key, value]) => ({
        key,
        name: getHRVMetricName(key),
        value: formatValue(value),
        unit: getHRVMetricUnit(key)
      }))
    })
  }
  
  if (hrvData.value.frequency_domain?.value) {
    domains.push({
      key: 'frequency_domain',
      label: '频域指标',
      metrics: Object.entries(hrvData.value.frequency_domain.value).map(([key, value]) => ({
        key,
        name: getHRVMetricName(key),
        value: formatValue(value),
        unit: getHRVMetricUnit(key)
      }))
    })
  }
  
  if (hrvData.value.nonlinear?.value) {
    domains.push({
      key: 'nonlinear',
      label: '非线性指标',
      metrics: Object.entries(hrvData.value.nonlinear.value).map(([key, value]) => ({
        key,
        name: getHRVMetricName(key),
        value: formatValue(value),
        unit: getHRVMetricUnit(key)
      }))
    })
  }
  
  return domains
})

const bvpData = computed(() => {
  return props.analysisData.bvp_waveform?.value
})

const signalQuality = computed(() => {
  return props.analysisData.signal_quality?.value
})

const bvpChartOption = computed(() => {
  if (!bvpData.value || !bvpData.value.bvp || !bvpData.value.timestamps) {
    return {}
  }

  const data = bvpData.value.bvp.map((value, index) => [
    bvpData.value.timestamps[index],
    value
  ])

  return {
    title: {
      text: 'BVP波形图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const point = params[0]
        return `时间: ${point.value[0].toFixed(2)}s<br/>BVP: ${point.value[1].toFixed(3)}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'value',
      name: '时间 (秒)',
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'value',
      name: 'BVP值',
      nameLocation: 'middle',
      nameGap: 40
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ],
    series: [
      {
        name: 'BVP',
        type: 'line',
        data: data,
        smooth: true,
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        symbol: 'none'
      }
    ]
  }
})

// 方法
const formatValue = (value) => {
  if (value === null || value === undefined) return '未知'
  if (typeof value === 'number') {
    return value.toFixed(1)
  }
  return value
}

const getHeartRateStatus = (value) => {
  if (!value) return 'unknown'
  if (value < 60) return 'low'
  if (value > 100) return 'high'
  return 'normal'
}

const getBloodPressureStatus = (value) => {
  if (!value || !value.SBP || !value.DBP) return 'unknown'
  if (value.SBP > 140 || value.DBP > 90) return 'high'
  if (value.SBP < 90 || value.DBP < 60) return 'low'
  return 'normal'
}

const getSpO2Status = (value) => {
  if (!value) return 'unknown'
  if (value < 95) return 'low'
  return 'normal'
}

const getBreathingRateStatus = (value) => {
  if (!value) return 'unknown'
  if (value < 12 || value > 20) return 'abnormal'
  return 'normal'
}

const getMetricStatusClass = (status) => {
  return `metric-${status}`
}

const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    low: '偏低',
    high: '偏高',
    abnormal: '异常',
    unknown: '未知'
  }
  return statusMap[status] || '未知'
}

const getRiskLevelClass = (level) => {
  if (level === '低' || level === '低风险') return 'risk-low'
  if (level === '中' || level === '中风险') return 'risk-medium'
  if (level === '高' || level === '高风险') return 'risk-high'
  return 'risk-unknown'
}

const getRiskDescription = (level) => {
  const descriptions = {
    '低': '风险较低，继续保持健康生活方式',
    '低风险': '风险较低，继续保持健康生活方式',
    '中': '存在一定风险，建议定期检查',
    '中风险': '存在一定风险，建议定期检查',
    '高': '风险较高，建议及时就医',
    '高风险': '风险较高，建议及时就医'
  }
  return descriptions[level] || '请咨询医生'
}

const getHRVMetricName = (key) => {
  const names = {
    ibi: 'IBI',
    sdnn: 'SDNN',
    rmssd: 'RMSSD',
    pnn20: 'pNN20',
    pnn50: 'pNN50',
    hr_mad: 'HR MAD',
    VLF: 'VLF',
    LF: 'LF',
    HF: 'HF',
    TP: 'TP',
    'LF/HF': 'LF/HF',
    sd1: 'SD1',
    sd2: 'SD2',
    s: 'S',
    'sd1/sd2': 'SD1/SD2'
  }
  return names[key] || key.toUpperCase()
}

const getHRVMetricUnit = (key) => {
  const units = {
    ibi: 'ms',
    sdnn: 'ms',
    rmssd: 'ms',
    pnn20: '%',
    pnn50: '%',
    hr_mad: 'bpm',
    VLF: 'ms²',
    LF: 'ms²',
    HF: 'ms²',
    TP: 'ms²',
    'LF/HF': '',
    sd1: 'ms',
    sd2: 'ms',
    s: 'ms²',
    'sd1/sd2': ''
  }
  return units[key] || ''
}

const getQualityColor = (quality) => {
  if (quality >= 0.8) return '#67C23A'
  if (quality >= 0.6) return '#E6A23C'
  return '#F56C6C'
}

const getQualityText = (quality) => {
  if (quality >= 0.8) return '信号质量优秀'
  if (quality >= 0.6) return '信号质量良好'
  if (quality >= 0.4) return '信号质量一般'
  return '信号质量较差'
}

const exportToPDF = async () => {
  try {
    isExporting.value = true
    ElMessage.info('正在生成PDF报告...')

    // 等待图表渲染完成
    await nextTick()
    
    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    
    // 添加标题
    pdf.setFontSize(20)
    pdf.text('健康分析报告', pageWidth / 2, 20, { align: 'center' })
    
    // 添加生成时间
    pdf.setFontSize(12)
    pdf.text(`生成时间: ${new Date().toLocaleString()}`, pageWidth / 2, 30, { align: 'center' })
    
    // 捕获页面内容
    const element = document.querySelector('.health-analysis-result')
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true
    })
    
    const imgData = canvas.toDataURL('image/png')
    const imgWidth = pageWidth - 20
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    
    // 添加图片到PDF
    pdf.addImage(imgData, 'PNG', 10, 40, imgWidth, imgHeight)
    
    // 保存PDF
    const fileName = `健康分析报告_${new Date().toISOString().slice(0, 10)}.pdf`
    pdf.save(fileName)
    
    ElMessage.success('PDF报告导出成功')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败，请重试')
  } finally {
    isExporting.value = false
  }
}

const saveToHistory = () => {
  emit('save-report', props.analysisData)
}

const shareReport = () => {
  emit('share-report', props.analysisData)
}

onMounted(() => {
  // 确保图表正确渲染
  nextTick(() => {
    if (bvpChart.value) {
      bvpChart.value.resize()
    }
  })
})
</script>

<style scoped>
.health-analysis-result {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.basic-info-section,
.metrics-section,
.risk-assessment-section,
.hrv-section,
.bvp-chart-section,
.signal-quality-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.basic-info-section h3,
.metrics-section h3,
.risk-assessment-section h3,
.hrv-section h3,
.bvp-chart-section h3,
.signal-quality-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.info-card {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.info-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.info-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.metric-normal {
  border-color: #67c23a;
}

.metric-low,
.metric-high,
.metric-abnormal {
  border-color: #f56c6c;
}

.metric-unknown {
  border-color: #909399;
}

.metric-icon {
  font-size: 32px;
  margin-right: 15px;
}

.metric-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 16px;
}

.metric-value {
  margin: 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.metric-value span {
  font-size: 14px;
  color: #909399;
}

.metric-status {
  margin: 0;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.metric-status.normal {
  background: #f0f9ff;
  color: #67c23a;
}

.metric-status.low,
.metric-status.high,
.metric-status.abnormal {
  background: #fef0f0;
  color: #f56c6c;
}

.risk-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 15px;
}

.risk-low {
  background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 100%);
  border-left: 4px solid #67c23a;
}

.risk-medium {
  background: linear-gradient(135deg, #fffbf0 0%, #fff3e0 100%);
  border-left: 4px solid #e6a23c;
}

.risk-high {
  background: linear-gradient(135deg, #fef0f0 0%, #ffebee 100%);
  border-left: 4px solid #f56c6c;
}

.risk-unknown {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  border-left: 4px solid #909399;
}

.risk-icon {
  font-size: 32px;
  margin-right: 15px;
}

.risk-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 16px;
}

.risk-level {
  margin: 5px 0;
  font-size: 20px;
  font-weight: 600;
}

.risk-description {
  margin: 0;
  font-size: 12px;
  color: #606266;
}

.hrv-domain-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.hrv-domain-card h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  text-align: center;
}

.hrv-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 5px 0;
  border-bottom: 1px solid #e4e7ed;
}

.hrv-metric:last-child {
  border-bottom: none;
}

.hrv-metric-name {
  font-weight: 500;
  color: #606266;
}

.hrv-metric-value {
  color: #303133;
  font-weight: 600;
}

.chart-container {
  margin-bottom: 15px;
}

.chart-info {
  display: flex;
  justify-content: space-around;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.chart-info p {
  margin: 0;
  color: #606266;
}

.quality-indicator {
  text-align: center;
}

.quality-text {
  margin-top: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.result-actions {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.result-actions .el-button {
  margin: 0 10px;
}

@media (max-width: 768px) {
  .health-analysis-result {
    padding: 10px;
  }
  
  .metric-card,
  .risk-card {
    flex-direction: column;
    text-align: center;
  }
  
  .metric-icon,
  .risk-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .result-actions .el-button {
    width: 100%;
    margin: 5px 0;
  }
  
  .chart-info {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
