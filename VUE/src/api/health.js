import { http } from '@/utils/request'

/**
 * 健康相关API接口
 */
export const healthApi = {
  /**
   * 视频健康分析
   * @param {FormData} formData - 包含视频文件和分析参数的FormData
   * @param {Function} onUploadProgress - 上传进度回调
   */
  analyzeVideo(formData, onUploadProgress) {
    return http.upload('/health/video', formData, onUploadProgress)
  },

  /**
   * 获取首页数据
   * @param {number} uid - 用户ID
   * @param {number} fuid - 家庭成员ID（可选）
   */
  getHomeData(uid, fuid = null) {
    const url = fuid ? `/home/<USER>/${fuid}` : `/home/<USER>
    return http.get(url)
  },

  /**
   * 获取健康报告历史
   * @param {number} uid - 用户ID
   * @param {number} fuid - 家庭成员ID（可选）
   * @param {number} limit - 限制数量
   */
  getReportHistory(uid, fuid = null, limit = 10) {
    const params = { limit }
    if (fuid) {
      params.fuid = fuid
    }
    return http.get(`/health/reports/${uid}`, params)
  },

  /**
   * 获取健康报告详情
   * @param {string} reportId - 报告ID
   */
  getReportDetail(reportId) {
    return http.get(`/health/report/${reportId}`)
  },

  /**
   * 删除健康报告
   * @param {string} reportId - 报告ID
   */
  deleteReport(reportId) {
    return http.delete(`/health/report/${reportId}`)
  },

  /**
   * 获取BVP波形数据
   * @param {string} reportId - 报告ID
   */
  getBvpWaveform(reportId) {
    return http.get(`/health/bvp/${reportId}`)
  },

  /**
   * 健康检查
   */
  healthCheck() {
    return http.get('/health/hello')
  },

  /**
   * 系统状态检查
   */
  systemStatus() {
    return http.get('/health/status')
  },

  /**
   * 导出健康报告
   * @param {string} reportId - 报告ID
   * @param {string} format - 导出格式 (pdf, excel)
   */
  exportReport(reportId, format = 'pdf') {
    return http.get(`/health/export/${reportId}`, { format })
  },

  /**
   * 批量分析视频
   * @param {Array} videoList - 视频列表
   */
  batchAnalyze(videoList) {
    return http.post('/health/batch-analyze', { videos: videoList })
  },

  /**
   * 获取分析任务状态
   * @param {string} taskId - 任务ID
   */
  getTaskStatus(taskId) {
    return http.get(`/health/task/${taskId}`)
  },

  /**
   * 取消分析任务
   * @param {string} taskId - 任务ID
   */
  cancelTask(taskId) {
    return http.delete(`/health/task/${taskId}`)
  }
}
