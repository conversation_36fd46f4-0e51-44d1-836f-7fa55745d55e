import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  getToken, 
  setToken, 
  removeToken, 
  getUserInfo, 
  setUserInfo, 
  removeUserInfo,
  clearAuth,
  isTokenValid 
} from '@/utils/auth'
import { userApi } from '@/api/user'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  const userInfo = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!token.value && !!userInfo.value
  })

  const userId = computed(() => {
    return userInfo.value?.uid || null
  })

  const userName = computed(() => {
    return userInfo.value?.name || ''
  })

  const userEmail = computed(() => {
    return userInfo.value?.email || ''
  })

  // 初始化认证状态
  const initializeAuth = () => {
    const savedToken = getToken()
    const savedUserInfo = getUserInfo()
    
    if (savedToken && isTokenValid(savedToken) && savedUserInfo) {
      token.value = savedToken
      userInfo.value = savedUserInfo
    } else {
      // 清除无效的认证信息
      clearAuth()
    }
  }

  // 用户登录
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await userApi.login(credentials)
      
      if (response.token) {
        // 保存认证信息
        token.value = response.token
        userInfo.value = {
          uid: response.uid,
          name: response.name,
          email: response.email
        }
        
        // 持久化存储
        setToken(response.token)
        setUserInfo(userInfo.value)
        
        ElMessage.success('登录成功')
        return response
      } else {
        throw new Error('登录响应格式错误')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户注册
  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await userApi.register(userData)
      
      if (response.token) {
        // 注册成功后自动登录
        token.value = response.token
        userInfo.value = {
          uid: response.uid,
          name: response.name,
          email: response.email
        }
        
        // 持久化存储
        setToken(response.token)
        setUserInfo(userInfo.value)
        
        ElMessage.success('注册成功')
        return response
      } else {
        throw new Error('注册响应格式错误')
      }
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      isLoading.value = true
      const response = await userApi.getCurrentUser()
      
      userInfo.value = {
        uid: response.uid,
        name: response.name,
        email: response.email
      }
      
      // 更新本地存储
      setUserInfo(userInfo.value)
      
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token无效，执行登出
      logout()
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户登出
  const logout = () => {
    try {
      // 清除状态
      token.value = ''
      userInfo.value = null

      // 清除本地存储
      clearAuth()

      // 清除所有localStorage数据
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.startsWith('health_') || key.startsWith('user_') || key.startsWith('family_'))) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))

      // 清除sessionStorage
      sessionStorage.clear()

      // 清除其他store的数据
      const { useHealthStore } = await import('./health')
      const { useFamilyStore } = await import('./family')

      const healthStore = useHealthStore()
      const familyStore = useFamilyStore()

      healthStore.clearAllData()
      familyStore.clearAllData()

      ElMessage.success('已退出登录')
    } catch (error) {
      console.error('退出登录时清理数据失败:', error)
      // 即使清理失败也要清除基本认证信息
      token.value = ''
      userInfo.value = null
      clearAuth()
      ElMessage.success('已退出登录')
    }
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    setUserInfo(userInfo.value)
  }

  // 检查认证状态
  const checkAuth = () => {
    const savedToken = getToken()
    if (!savedToken || !isTokenValid(savedToken)) {
      logout()
      return false
    }
    return true
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userId,
    userName,
    userEmail,
    
    // 方法
    initializeAuth,
    login,
    register,
    fetchUserInfo,
    logout,
    updateUserInfo,
    checkAuth
  }
})
