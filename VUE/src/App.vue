<template>
  <div id="app">
    <MainLayout />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import MainLayout from '@/components/Layout/MainLayout.vue'

const userStore = useUserStore()

onMounted(() => {
  // 应用启动时检查本地存储的token
  try {
    userStore.initializeAuth()
  } catch (error) {
    console.error('初始化认证失败:', error)
  }
})
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f7fa;
}
</style>
