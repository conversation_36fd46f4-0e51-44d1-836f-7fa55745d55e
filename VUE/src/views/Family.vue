<template>
  <MainLayout>
    <PageContainer title="家庭成员" description="管理您的家庭成员信息">
      <template #extra>
        <el-button type="primary" @click="showAddMemberDialog">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </template>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <p>加载中...</p>
    </div>

    <!-- 家庭成员列表 -->
    <div v-else-if="familyMembers.length > 0" class="family-content">
      <el-row :gutter="20">
        <el-col
          v-for="member in familyMembers"
          :key="member.fuid"
          :xs="24" :sm="12" :lg="8" :xl="6"
        >
          <div class="member-card">
            <div class="member-header">
              <el-avatar :size="60" :src="member.avatar_url">
                <el-icon :size="30"><User /></el-icon>
              </el-avatar>
              <div class="member-actions">
                <el-dropdown @command="(command) => handleMemberAction(command, member)">
                  <el-button type="text" size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="scan">健康检测</el-dropdown-item>
                      <el-dropdown-item command="reports">查看报告</el-dropdown-item>
                      <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="member-info">
              <h4>{{ member.name }}</h4>
              <p class="relationship">{{ member.relationship }}</p>
              <div class="member-details">
                <el-tag :type="member.gender === '男' ? 'primary' : 'danger'" size="small">
                  {{ member.gender }}
                </el-tag>
                <span v-if="member.birth_year" class="age">
                  {{ calculateAge(member.birth_year) }}岁
                </span>
              </div>
              <div v-if="member.height && member.weight" class="physical-info">
                <span>{{ member.height }}cm</span>
                <span>{{ member.weight }}kg</span>
                <span>BMI: {{ calculateBMI(member.height, member.weight) }}</span>
              </div>
            </div>

            <div class="member-footer">
              <el-button size="small" type="primary" @click="scanForMember(member)">
                健康检测
              </el-button>
              <el-button size="small" @click="viewMemberReports(member)">
                查看报告
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无家庭成员">
        <el-button type="primary" @click="showAddMemberDialog">添加成员</el-button>
      </el-empty>
    </div>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMemberDialogVisible"
      :title="isEditMode ? '编辑家庭成员' : '添加家庭成员'"
      width="500px"
      :before-close="handleAddDialogClose"
    >
      <el-form
        ref="addMemberFormRef"
        :model="addMemberForm"
        :rules="addMemberRules"
        label-width="80px"
        size="large"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="addMemberForm.name" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="关系" prop="relationship">
          <el-select v-model="addMemberForm.relationship" placeholder="请选择关系" style="width: 100%">
            <el-option label="父亲" value="父亲" />
            <el-option label="母亲" value="母亲" />
            <el-option label="配偶" value="配偶" />
            <el-option label="儿子" value="儿子" />
            <el-option label="女儿" value="女儿" />
            <el-option label="兄弟" value="兄弟" />
            <el-option label="姐妹" value="姐妹" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="addMemberForm.gender">
            <el-radio value="男">男</el-radio>
            <el-radio value="女">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="出生年份" prop="birth_year">
          <el-date-picker
            v-model="addMemberForm.birth_year"
            type="year"
            placeholder="请选择出生年份"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </el-form-item>

        <el-form-item label="身高" prop="height">
          <el-input v-model.number="addMemberForm.height" placeholder="请输入身高">
            <template #suffix>cm</template>
          </el-input>
        </el-form-item>

        <el-form-item label="体重" prop="weight">
          <el-input v-model.number="addMemberForm.weight" placeholder="请输入体重">
            <template #suffix>kg</template>
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleAddDialogClose">取消</el-button>
          <el-button type="primary" @click="submitAddMember" :loading="isSubmitting">
            {{ isSubmitting ? (isEditMode ? '更新中...' : '添加中...') : (isEditMode ? '更新' : '确定') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员健康报告查看对话框 -->
    <el-dialog
      v-model="showReportDialog"
      :title="`${selectedMember?.name || ''}的健康报告`"
      width="90%"
      :close-on-click-modal="false"
      class="report-dialog"
    >
      <div v-if="memberReportData" class="member-report-content">
        <HealthAnalysisResult
          :analysis-data="memberReportData"
          @save-report="handleSaveMemberReport"
          @share-report="handleShareMemberReport"
        />
      </div>
      <div v-else class="no-report-data">
        <el-empty description="该成员暂无健康报告数据">
          <el-button type="primary" @click="scanForMember(selectedMember)">
            立即检测
          </el-button>
        </el-empty>
      </div>
    </el-dialog>
    </PageContainer>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { calculateAge, calculateBMI } from '@/utils/helpers'
import { ElMessage } from 'element-plus'
import { Plus, Edit, Delete, User, MoreFilled } from '@element-plus/icons-vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import PageContainer from '@/components/Layout/PageContainer.vue'
import HealthAnalysisResult from '@/components/HealthAnalysisResult.vue'

const router = useRouter()
const userStore = useUserStore()

// 状态管理
const familyMembers = ref([])
const isLoading = ref(false)

// 添加成员相关状态
const addMemberDialogVisible = ref(false)
const addMemberFormRef = ref(null)
const isSubmitting = ref(false)

// 编辑模式状态
const isEditMode = ref(false)
const editingMemberId = ref(null)

// 报告查看相关状态
const showReportDialog = ref(false)
const selectedMember = ref(null)
const memberReportData = ref(null)

// 添加成员表单数据
const addMemberForm = ref({
  name: '',
  relationship: '',
  gender: '',
  birth_year: null,
  height: null,
  weight: null
})

// 表单验证规则
const addMemberRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  relationship: [
    { required: true, message: '请选择关系', trigger: 'change' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  birth_year: [
    { required: true, message: '请选择出生年份', trigger: 'change' }
  ],
  height: [
    { required: true, message: '请输入身高', trigger: 'blur' },
    { type: 'number', min: 50, max: 250, message: '身高应在 50-250cm 之间', trigger: 'blur' }
  ],
  weight: [
    { required: true, message: '请输入体重', trigger: 'blur' },
    { type: 'number', min: 10, max: 300, message: '体重应在 10-300kg 之间', trigger: 'blur' }
  ]
}



// 处理成员操作
const handleMemberAction = async (command, member) => {
  switch (command) {
    case 'edit':
      editMember(member)
      break
    case 'scan':
      scanForMember(member)
      break
    case 'reports':
      viewMemberReports(member)
      break
    case 'delete':
      await deleteMember(member)
      break
  }
}

// 显示添加成员对话框
const showAddMemberDialog = () => {
  // 重置表单
  addMemberForm.value = {
    name: '',
    relationship: '',
    gender: '',
    birth_year: null,
    height: null,
    weight: null
  }

  // 重置编辑模式
  isEditMode.value = false
  editingMemberId.value = null

  // 清除表单验证
  if (addMemberFormRef.value) {
    addMemberFormRef.value.clearValidate()
  }

  addMemberDialogVisible.value = true
}

// 关闭添加成员对话框
const handleAddDialogClose = () => {
  addMemberDialogVisible.value = false
}

// 日期禁用函数
const disabledDate = (time) => {
  const currentYear = new Date().getFullYear()
  const year = time.getFullYear()
  return year > currentYear || year < (currentYear - 120)
}

// 提交添加成员
const submitAddMember = async () => {
  if (!addMemberFormRef.value) return

  try {
    // 验证表单
    await addMemberFormRef.value.validate()

    isSubmitting.value = true

    // 准备提交数据
    const submitData = {
      ...addMemberForm.value,
      birth_year: addMemberForm.value.birth_year ? addMemberForm.value.birth_year.getFullYear() : null,
      uid: userStore.userId
    }

    // 调用API添加或更新成员
    let response
    if (isEditMode.value) {
      // 编辑模式：更新成员 (暂时使用添加接口，因为文档中没有更新接口)
      ElMessage.warning('编辑功能暂未实现，请删除后重新添加')
      return
    } else {
      // 添加模式：新增成员 - 使用正确的接口路径
      response = await fetch('/api/v1/users/addfamily', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('health_detection_token')}`
        },
        body: JSON.stringify({
          ...submitData,
          avatar_url: 'default.png' // 添加必需的avatar_url字段
        })
      })
    }

    if (response.ok) {
      const result = await response.json()
      ElMessage.success(isEditMode.value ? '更新成员成功' : '添加成员成功')

      // 关闭对话框
      addMemberDialogVisible.value = false

      // 刷新家庭成员列表
      await loadFamilyMembers()
    } else {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || (isEditMode.value ? '更新成员失败' : '添加成员失败'))
    }
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error(`添加成员失败：${error.message}`)
  } finally {
    isSubmitting.value = false
  }
}

// 编辑成员
const editMember = (member) => {
  // 填充表单数据
  addMemberForm.value = {
    name: member.name,
    relationship: member.relationship,
    gender: member.gender,
    birth_year: new Date(member.birth_year, 0, 1), // 转换为Date对象
    height: member.height,
    weight: member.weight
  }

  // 设置编辑模式
  isEditMode.value = true
  editingMemberId.value = member.fuid

  // 清除表单验证
  if (addMemberFormRef.value) {
    addMemberFormRef.value.clearValidate()
  }

  addMemberDialogVisible.value = true
}

// 删除成员
const deleteMember = async (member) => {
  ElMessage.warning('删除成员功能暂未实现，请联系管理员')
  // 根据接口文档，暂时没有删除家庭成员的API接口
  console.log('删除成员请求:', member)
}

// 为成员进行健康检测
const scanForMember = (member) => {
  router.push(`/scan?fuid=${member.fuid}`)
}

// 查看成员健康报告
const viewMemberReports = async (member) => {
  selectedMember.value = member
  showReportDialog.value = true

  // 尝试获取成员的健康报告数据
  try {
    // 首先尝试从API获取最新的健康报告
    const token = localStorage.getItem('health_detection_token')
    const response = await fetch(`http://localhost:8000/api/v1/home/<USER>
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      if (data.health_report) {
        // 格式化数据为HealthAnalysisResult组件所需的格式
        memberReportData.value = formatMemberHealthData(data.health_report, data.family_member, data.bvp_waveform)
      } else {
        memberReportData.value = null
      }
    } else {
      // API调用失败，尝试从本地存储获取
      const localReports = JSON.parse(localStorage.getItem('health_reports') || '[]')
      const memberReport = localReports.find(report =>
        report.data.name?.value === member.name ||
        report.userId === member.fuid
      )

      if (memberReport) {
        memberReportData.value = memberReport.data
      } else {
        memberReportData.value = null
      }
    }
  } catch (error) {
    console.error('获取成员健康报告失败:', error)
    memberReportData.value = null
  }
}

// 格式化成员健康数据
const formatMemberHealthData = (healthReport, familyMember, bvpWaveform) => {
  if (!healthReport && !familyMember) {
    // 返回空状态数据，保持界面结构
    return {
      name: { value: selectedMember.value?.name || '未知用户', unit: '', label: '姓名' },
      gender: { value: selectedMember.value?.gender || '未知', unit: '', label: '性别' },
      age: { value: selectedMember.value?.birth_year ? calculateAge(selectedMember.value.birth_year) : '未知', unit: '岁', label: '年龄' },
      height: { value: selectedMember.value?.height || '未知', unit: 'cm', label: '身高' },
      weight: { value: selectedMember.value?.weight || '未知', unit: 'kg', label: '体重' },
      bmi: { value: selectedMember.value?.height && selectedMember.value?.weight ? calculateBMI(selectedMember.value.height, selectedMember.value.weight) : '未知', unit: '', label: 'BMI' },
      heart_rate: { value: null, unit: 'bpm', label: '心率' },
      blood_pressure: { value: { SBP: null, DBP: null }, unit: 'mmHg', label: '血压' },
      spo2: { value: null, unit: '%', label: '血氧饱和度' },
      breathing_rate: { value: null, unit: '次/分', label: '呼吸频率' },
      cardiac_risk: { value: '未知', unit: '', label: '心脏风险' },
      brain_risk: { value: '未知', unit: '', label: '脑风险' },
      afib: { value: '未知', unit: '', label: '房颤风险' },
      signal_quality: { value: null, unit: '', label: '信号质量' }
    }
  }

  // 有数据时返回格式化的真实数据
  return {
    name: { value: familyMember?.name || selectedMember.value?.name || '未知用户', unit: '', label: '姓名' },
    gender: { value: familyMember?.gender || selectedMember.value?.gender || '未知', unit: '', label: '性别' },
    age: { value: familyMember?.age || (selectedMember.value?.birth_year ? calculateAge(selectedMember.value.birth_year) : '未知'), unit: '岁', label: '年龄' },
    height: { value: familyMember?.height || selectedMember.value?.height || '未知', unit: 'cm', label: '身高' },
    weight: { value: familyMember?.weight || selectedMember.value?.weight || '未知', unit: 'kg', label: '体重' },
    bmi: { value: healthReport?.bmi || (selectedMember.value?.height && selectedMember.value?.weight ? calculateBMI(selectedMember.value.height, selectedMember.value.weight) : '未知'), unit: '', label: 'BMI' },
    heart_rate: { value: healthReport?.heart_rate, unit: 'bpm', label: '心率' },
    blood_pressure: {
      value: healthReport?.blood_pressure ? {
        SBP: healthReport.blood_pressure.systolic || healthReport.blood_pressure.SBP,
        DBP: healthReport.blood_pressure.diastolic || healthReport.blood_pressure.DBP
      } : { SBP: null, DBP: null },
      unit: 'mmHg',
      label: '血压'
    },
    spo2: { value: healthReport?.spo2, unit: '%', label: '血氧饱和度' },
    breathing_rate: { value: healthReport?.breathing_rate, unit: '次/分', label: '呼吸频率' },
    cardiac_risk: { value: healthReport?.cardiac_risk || '未知', unit: '', label: '心脏风险' },
    brain_risk: { value: healthReport?.brain_risk || '未知', unit: '', label: '脑风险' },
    afib: { value: healthReport?.afib || '未知', unit: '', label: '房颤风险' },
    signal_quality: { value: healthReport?.signal_quality, unit: '', label: '信号质量' },
    // 添加HRV数据（如果有）
    hrv: healthReport?.hrv ? {
      time_domain: { value: healthReport.hrv.time_domain, unit: '', label: '时域指标' },
      frequency_domain: { value: healthReport.hrv.frequency_domain, unit: '', label: '频域指标' },
      nonlinear: { value: healthReport.hrv.nonlinear, unit: '', label: '非线性指标' }
    } : null,
    // 添加BVP波形数据（如果有）
    bvp_waveform: bvpWaveform ? {
      value: bvpWaveform,
      unit: '',
      label: 'BVP波形'
    } : null
  }
}

// 处理成员报告保存
const handleSaveMemberReport = (analysisData) => {
  try {
    const reports = JSON.parse(localStorage.getItem('health_reports') || '[]')
    const newReport = {
      id: Date.now(),
      data: analysisData,
      timestamp: new Date().toISOString(),
      userId: selectedMember.value?.fuid,
      memberName: selectedMember.value?.name
    }
    reports.unshift(newReport)

    // 只保留最近50个报告
    if (reports.length > 50) {
      reports.splice(50)
    }

    localStorage.setItem('health_reports', JSON.stringify(reports))
    ElMessage.success(`${selectedMember.value?.name}的报告保存成功`)
  } catch (error) {
    console.error('保存报告失败:', error)
    ElMessage.error('保存报告失败')
  }
}

// 处理成员报告分享
const handleShareMemberReport = (analysisData) => {
  if (navigator.share) {
    navigator.share({
      title: `${selectedMember.value?.name}的健康分析报告`,
      text: `${selectedMember.value?.name}的健康分析报告 - 心率: ${analysisData.heart_rate?.value || '未知'}bpm`,
      url: window.location.href
    }).catch(console.error)
  } else {
    navigator.clipboard.writeText(window.location.href).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.info('请手动复制链接分享')
    })
  }
}

// 加载家庭成员数据
const loadFamilyMembers = async () => {
  try {
    isLoading.value = true
    const response = await fetch(`/api/v1/users/${userStore.userId}/familylist`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('health_detection_token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      familyMembers.value = data
      console.log('家庭成员数据加载成功:', data)
    } else {
      console.error('API请求失败:', response.status)
    }
  } catch (error) {
    console.error('加载家庭成员失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await loadFamilyMembers()
})
</script>

<style scoped>
.family-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  color: #303133;
}

.page-header p {
  margin: 0 0 20px 0;
  color: #909399;
  font-size: 16px;
}

.family-content {
  padding: 20px 0;
}

.member-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.member-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.member-actions {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.member-card:hover .member-actions {
  opacity: 1;
}

.member-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.relationship {
  margin: 0 0 12px 0;
  color: #909399;
  font-size: 14px;
}

.member-details {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.age {
  font-size: 14px;
  color: #606266;
}

.physical-info {
  display: flex;
  gap: 12px;
  font-size: 13px;
  color: #909399;
  margin-bottom: 15px;
}

.member-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.loading-state {
  padding: 40px 20px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .member-card {
    margin-bottom: 16px;
    padding: 16px;
  }

  .member-footer {
    flex-direction: column;
    gap: 8px;
  }

  .member-footer .el-button {
    width: 100%;
  }
}
</style>

<style scoped>
.family-content {
  min-height: 400px;
}

.member-list {
  margin-bottom: 20px;
}

.member-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.member-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.member-actions {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.member-card:hover .member-actions {
  opacity: 1;
}

.member-info {
  flex: 1;
  text-align: center;
  margin-bottom: 16px;
}

.member-info h4 {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.relationship {
  margin: 0 0 12px;
  font-size: 14px;
  color: #606266;
}

.member-details {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.age {
  font-size: 12px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.physical-info {
  display: flex;
  justify-content: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.physical-info span {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.member-footer {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.member-footer .el-button {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-card {
    padding: 16px;
  }

  .member-info h4 {
    font-size: 16px;
  }

  .member-footer {
    flex-direction: column;
  }

  .member-footer .el-button {
    width: 100%;
  }

  .physical-info {
    flex-direction: column;
    gap: 4px;
  }

  .member-details {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .family-page {
    padding: 15px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .member-header {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .member-actions {
    opacity: 1;
  }

  .member-info h4 {
    font-size: 18px;
    text-align: center;
  }

  .relationship {
    text-align: center;
  }
}

/* 对话框移动端适配 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .el-dialog__body {
    padding: 20px 15px !important;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-size: 14px;
  }

  .el-input__inner,
  .el-select__inner {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

@media (max-width: 480px) {
  .el-dialog {
    width: 98% !important;
    margin: 2vh auto !important;
  }

  .el-dialog__header {
    padding: 15px 20px 10px !important;
  }

  .el-dialog__title {
    font-size: 18px !important;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px !important;
  }

  .dialog-footer .el-button {
    width: 48%;
    margin: 0 1%;
  }
}

/* 报告对话框样式 */
.report-dialog .el-dialog__body {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.member-report-content {
  min-height: 400px;
}

.no-report-data {
  text-align: center;
  padding: 40px 20px;
}

@media (max-width: 768px) {
  .report-dialog {
    width: 95% !important;
  }

  .report-dialog .el-dialog__body {
    padding: 15px;
  }
}
</style>
