<template>
  <PageContainer title="健康扫描" description="使用摄像头录制视频进行健康检测分析">
    <template #extra>
      <el-button @click="goHome">
        <el-icon><House /></el-icon>
        返回首页
      </el-button>
    </template>

      <!-- 扫描步骤指示器 -->
      <div class="scan-steps">
        <el-steps :active="currentStep" align-center>
          <el-step title="摄像头准备" description="启动摄像头" />
          <el-step title="录制视频" description="录制30-60秒视频" />
          <el-step title="上传分析" description="上传并分析视频" />
          <el-step title="查看结果" description="查看健康分析结果" />
        </el-steps>
      </div>

      <!-- 步骤内容 -->
      <div class="scan-content">
      <!-- 步骤1: 摄像头准备 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>摄像头准备</h3>
        <div class="camera-section">
          <div class="camera-container">
            <video 
              ref="videoElement" 
              :class="{ 'camera-active': isCameraActive }"
              autoplay 
              muted 
              playsinline
            ></video>
            <div v-if="!isCameraActive" class="camera-placeholder">
              <el-icon :size="80"><VideoCamera /></el-icon>
              <p>点击下方按钮启动摄像头</p>
            </div>
          </div>
          
          <div class="camera-controls">
            <el-button 
              v-if="!isCameraActive" 
              type="primary" 
              size="large" 
              @click="startCamera"
              :loading="isStartingCamera"
            >
              <el-icon><VideoCamera /></el-icon>
              启动摄像头
            </el-button>
            
            <div v-else class="camera-actions">
              <el-button type="success" size="large" @click="nextStep">
                <el-icon><Right /></el-icon>
                开始录制
              </el-button>
              <el-button size="large" @click="stopCamera">
                <el-icon><Close /></el-icon>
                关闭摄像头
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 录制视频 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>录制视频</h3>
        <div class="recording-section">
          <div class="camera-container">
            <video
              ref="recordingVideoElement"
              :class="{ 'camera-active': isCameraActive }"
              autoplay
              muted
              playsinline
              @loadedmetadata="onVideoLoaded"
            ></video>

            <!-- 摄像头未启动时的占位符 -->
            <div v-if="!isCameraActive" class="camera-placeholder">
              <el-icon :size="64" color="#909399"><VideoCamera /></el-icon>
              <p>摄像头未启动</p>
            </div>
            
            <!-- 录制状态覆盖层 -->
            <div v-if="isRecording" class="recording-overlay">
              <div class="recording-indicator">
                <div class="recording-dot"></div>
                <span>录制中 {{ formatTime(recordingTime) }}</span>
              </div>
            </div>
          </div>
          
          <div class="recording-controls">
            <div class="recording-info">
              <p v-if="!isRecording">请保持面部在画面中央，录制时间建议30-60秒</p>
              <p v-else>录制中... {{ formatTime(recordingTime) }} / {{ formatTime(maxRecordingTime) }}</p>
            </div>
            
            <div class="recording-actions">
              <el-button 
                v-if="!isRecording" 
                type="danger" 
                size="large" 
                @click="startRecording"
                :disabled="!isCameraActive"
              >
                <el-icon><VideoPlay /></el-icon>
                开始录制
              </el-button>
              
              <el-button 
                v-else 
                type="success" 
                size="large" 
                @click="stopRecording"
              >
                <el-icon><VideoPause /></el-icon>
                停止录制
              </el-button>
              
              <el-button size="large" @click="prevStep">
                <el-icon><ArrowLeft /></el-icon>
                返回上一步
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3: 上传分析 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>上传分析</h3>
        <div class="upload-section">
          <div v-if="recordedBlob" class="video-preview">
            <video 
              ref="previewElement"
              controls
              :src="previewUrl"
              class="preview-video"
            ></video>
            <div class="video-info">
              <p>录制时长: {{ formatTime(recordedDuration) }}</p>
              <p>文件大小: {{ formatFileSize(recordedBlob.size) }}</p>
            </div>
          </div>
          
          <div class="upload-controls">
            <el-button 
              type="primary" 
              size="large" 
              @click="uploadAndAnalyze"
              :loading="isUploading"
              :disabled="!recordedBlob"
            >
              <el-icon><Upload /></el-icon>
              {{ isUploading ? '上传分析中...' : '上传并分析' }}
            </el-button>
            
            <el-button size="large" @click="prevStep">
              <el-icon><ArrowLeft /></el-icon>
              重新录制
            </el-button>
          </div>
          
          <!-- 上传进度 -->
          <div v-if="isUploading" class="upload-progress">
            <el-progress 
              :percentage="uploadProgress" 
              :status="uploadProgress === 100 ? 'success' : ''"
            />
            <p class="progress-text">{{ uploadStatusText }}</p>
          </div>
        </div>
      </div>

      <!-- 步骤4: 查看结果 -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>分析结果</h3>
        <div class="results-section">
          <div v-if="isAnalyzing" class="analyzing-state">
            <el-icon class="analyzing-icon" :size="60"><Loading /></el-icon>
            <p>正在分析视频，请稍候...</p>
            <el-progress :percentage="analysisProgress" :show-text="false" />
          </div>
          
          <div v-else-if="analysisResult" class="analysis-results">
            <HealthAnalysisResult
              :analysis-data="analysisResult"
              @save-report="handleSaveReport"
              @share-report="handleShareReport"
            />

            <!-- 额外操作按钮 -->
            <div class="additional-actions">
              <el-button size="large" @click="startNewScan">
                <el-icon><Refresh /></el-icon>
                重新检测
              </el-button>
              <el-button size="large" @click="goToReports">
                <el-icon><FolderOpened /></el-icon>
                查看历史报告
              </el-button>
            </div>
          </div>

          <div v-else class="no-results">
            <el-empty description="暂无分析结果">
              <el-button type="primary" @click="startNewScan">重新开始</el-button>
            </el-empty>
          </div>
        </div>
      </div>
      </div>
  </PageContainer>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { formatFileSize } from '@/utils/helpers'
import { ElMessage } from 'element-plus'
import { House, VideoCamera, Document, Refresh, FolderOpened } from '@element-plus/icons-vue'
import HealthAnalysisResult from '@/components/HealthAnalysisResult.vue'
import PageContainer from '@/components/Layout/PageContainer.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const currentStep = ref(0)
const videoElement = ref(null)
const recordingVideoElement = ref(null)
const previewElement = ref(null)

// 摄像头相关状态
const isCameraActive = ref(false)
const isStartingCamera = ref(false)
const mediaStream = ref(null)

// 录制相关状态
const isRecording = ref(false)
const recordingTime = ref(0)
const maxRecordingTime = ref(60)
const minRecordingTime = ref(10) // 最少录制10秒以确保文件大小足够
const mediaRecorder = ref(null)
const recordedChunks = ref([])
const recordedBlob = ref(null)
const recordedDuration = ref(0)
const previewUrl = ref('')

// 上传和分析状态
const isUploading = ref(false)
const uploadProgress = ref(0)
const uploadStatusText = ref('')
const isAnalyzing = ref(false)
const analysisProgress = ref(0)
const analysisResult = ref(null)

// 定时器
const recordingTimer = ref(null)

// 摄像头功能
const startCamera = async () => {
  try {
    isStartingCamera.value = true

    const constraints = {
      video: {
        width: { ideal: 1280, max: 1920 },
        height: { ideal: 720, max: 1080 },
        facingMode: 'user',
        frameRate: { ideal: 30, max: 60 }
      },
      audio: false
    }

    const stream = await navigator.mediaDevices.getUserMedia(constraints)
    mediaStream.value = stream

    if (videoElement.value) {
      videoElement.value.srcObject = stream

      // 等待视频元数据加载完成
      await new Promise((resolve) => {
        videoElement.value.onloadedmetadata = () => {
          console.log('视频元数据加载完成:', {
            videoWidth: videoElement.value.videoWidth,
            videoHeight: videoElement.value.videoHeight
          })
          resolve()
        }
      })

      // 确保视频开始播放
      await videoElement.value.play()
      isCameraActive.value = true
    }

    ElMessage.success('摄像头启动成功')
  } catch (error) {
    console.error('启动摄像头失败:', error)
    let errorMessage = '启动摄像头失败'

    if (error.name === 'NotAllowedError') {
      errorMessage = '摄像头权限被拒绝，请允许访问摄像头'
    } else if (error.name === 'NotFoundError') {
      errorMessage = '未找到摄像头设备'
    } else if (error.name === 'NotReadableError') {
      errorMessage = '摄像头被其他应用占用'
    }

    ElMessage.error(errorMessage)
  } finally {
    isStartingCamera.value = false
  }
}

// 视频加载完成回调
const onVideoLoaded = () => {
  console.log('视频加载完成')
}

const stopCamera = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
    mediaStream.value = null
  }

  if (videoElement.value) {
    videoElement.value.srcObject = null
  }

  if (recordingVideoElement.value) {
    recordingVideoElement.value.srcObject = null
  }

  isCameraActive.value = false
  ElMessage.info('摄像头已关闭')
}

// 录制功能
const startRecording = async () => {
  if (!mediaStream.value) {
    ElMessage.error('请先启动摄像头')
    return
  }

  try {
    recordedChunks.value = []

    // 检测支持的视频格式，优先使用MP4
    let options = {}
    let mimeType = ''
    let fileExtension = ''

    // 尝试不同的MP4编码格式
    const mp4Formats = [
      'video/mp4;codecs=avc1.42E01E',
      'video/mp4;codecs=avc1.4D401E',
      'video/mp4;codecs=avc1.640028',
      'video/mp4'
    ]

    let mp4Supported = false
    for (const format of mp4Formats) {
      if (MediaRecorder.isTypeSupported(format)) {
        mimeType = format
        fileExtension = 'mp4'
        options = {
          mimeType: format,
          videoBitsPerSecond: 1200000 // 降低比特率以减小文件大小和提高兼容性
        }
        mp4Supported = true
        console.log('使用MP4格式:', format)
        break
      }
    }

    if (!mp4Supported) {
      // 如果MP4不支持，使用WebM格式
      if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9')) {
        mimeType = 'video/webm;codecs=vp9'
        fileExtension = 'webm'
        options = {
          mimeType: 'video/webm;codecs=vp9',
          videoBitsPerSecond: 1200000
        }
        console.log('MP4不支持，使用WebM VP9格式')
      } else if (MediaRecorder.isTypeSupported('video/webm')) {
        mimeType = 'video/webm'
        fileExtension = 'webm'
        options = {
          mimeType: 'video/webm',
          videoBitsPerSecond: 1200000
        }
        console.log('MP4不支持，使用WebM格式')
      } else {
        throw new Error('浏览器不支持视频录制')
      }
    }

    console.log('使用录制格式:', mimeType)
    mediaRecorder.value = new MediaRecorder(mediaStream.value, options)

    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunks.value.push(event.data)
      }
    }

    mediaRecorder.value.onstop = () => {
      const blob = new Blob(recordedChunks.value, { type: mimeType })
      recordedBlob.value = blob
      recordedDuration.value = recordingTime.value
      previewUrl.value = URL.createObjectURL(blob)

      const sizeInMB = blob.size / 1024 / 1024
      console.log('录制完成:', {
        size: sizeInMB.toFixed(2) + 'MB',
        duration: recordingTime.value + 's',
        type: mimeType,
        actualSize: blob.size + ' bytes'
      })

      // 检查文件大小是否符合后端要求（1MB-100MB）
      if (sizeInMB < 1) {
        ElMessage.error('录制的视频文件太小，请录制更长时间的视频（至少需要1MB）')
        return
      }

      if (sizeInMB > 100) {
        ElMessage.error('录制的视频文件太大，请录制较短的视频（不超过100MB）')
        return
      }

      nextStep()
    }

    mediaRecorder.value.start()
    isRecording.value = true
    recordingTime.value = 0

    recordingTimer.value = setInterval(() => {
      recordingTime.value++

      if (recordingTime.value >= maxRecordingTime.value) {
        stopRecording()
      }
    }, 1000)

    ElMessage.success('开始录制')
  } catch (error) {
    console.error('录制失败:', error)
    ElMessage.error('录制失败，请重试')
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    // 检查录制时间是否足够
    if (recordingTime.value < minRecordingTime.value) {
      ElMessage.warning(`请至少录制${minRecordingTime.value}秒以确保文件大小足够`)
      return
    }

    mediaRecorder.value.stop()
    isRecording.value = false

    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }

    ElMessage.success('录制完成')
  }
}

// 视频格式转换函数
const convertVideoToMp4 = async (blob) => {
  return new Promise((resolve, reject) => {
    // 如果已经是MP4格式，直接返回
    if (blob.type.includes('video/mp4')) {
      console.log('视频已经是MP4格式，无需转换')
      resolve(blob)
      return
    }

    console.log('开始转换视频格式:', blob.type, '->', 'video/mp4')

    try {
      // 对于WebM格式，我们需要重新录制为MP4格式
      // 这里先简单地改变MIME类型，但保持原始数据
      // 在实际生产环境中，应该使用专业的转码工具

      // 创建一个新的MP4 blob，并确保文件扩展名正确
      const mp4Blob = new Blob([blob], { type: 'video/mp4' })

      console.log('视频格式转换完成:', {
        原始大小: (blob.size / 1024 / 1024).toFixed(2) + 'MB',
        转换后大小: (mp4Blob.size / 1024 / 1024).toFixed(2) + 'MB',
        原始类型: blob.type,
        转换后类型: mp4Blob.type
      })

      // 验证转换后的文件大小
      if (mp4Blob.size === 0) {
        throw new Error('转换后的视频文件为空')
      }

      const sizeInMB = mp4Blob.size / 1024 / 1024
      if (sizeInMB < 1) {
        throw new Error('视频文件太小，请录制更长时间的视频')
      }

      if (sizeInMB > 100) { // 100MB限制，与后端保持一致
        throw new Error('视频文件过大，请录制较短的视频')
      }

      resolve(mp4Blob)
    } catch (error) {
      console.error('视频格式转换失败:', error)
      reject(new Error(`视频格式转换失败: ${error.message}`))
    }
  })
}

// 上传和分析功能
const uploadAndAnalyze = async () => {
  if (!recordedBlob.value) {
    ElMessage.error('没有录制的视频')
    return
  }

  try {
    isUploading.value = true
    uploadProgress.value = 0
    uploadStatusText.value = '准备上传...'

    // 转换视频格式为MP4
    const videoBlob = await convertVideoToMp4(recordedBlob.value)

    const formData = new FormData()
    formData.append('file', videoBlob, 'health_scan.mp4')

    const requestData = {
      uid: userStore.userId,
      fuid: route.query.fuid ? parseInt(route.query.fuid) : null,
      name: userStore.userInfo?.name || '用户',
      gender: '男',
      height: 175,
      weight: 70,
      birth_year: 1990
    }

    formData.append('request_data', JSON.stringify(requestData))

    const uploadInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
        uploadStatusText.value = `上传中... ${uploadProgress.value}%`
      }
    }, 200)

    const response = await fetch('/api/v1/health/video', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('health_detection_token')}`
      },
      body: formData
    })

    clearInterval(uploadInterval)
    uploadProgress.value = 100
    uploadStatusText.value = '上传完成，开始分析...'

    if (response.ok) {
      const result = await response.json()
      analysisResult.value = result

      isAnalyzing.value = true
      analysisProgress.value = 0

      const analysisInterval = setInterval(() => {
        analysisProgress.value += 20
        if (analysisProgress.value >= 100) {
          clearInterval(analysisInterval)
          isAnalyzing.value = false
          nextStep()
        }
      }, 500)

      ElMessage.success('视频上传成功，正在分析...')
    } else {
      if (response.status === 404) {
        ElMessage.warning('后端接口暂未实现，使用模拟数据进行测试')

        const mockResult = {
          name: { value: requestData.name, unit: '', label: '姓名' },
          gender: { value: requestData.gender, unit: '', label: '性别' },
          age: { value: new Date().getFullYear() - requestData.birth_year, unit: '岁', label: '年龄' },
          bmi: { value: (requestData.weight / Math.pow(requestData.height / 100, 2)).toFixed(1), unit: '', label: 'BMI' },
          heart_rate: { value: 72.5, unit: 'bpm', label: '心率' },
          blood_pressure: {
            value: { SBP: 120, DBP: 80 },
            unit: 'mmHg',
            label: '血压'
          },
          spo2: { value: 98.2, unit: '%', label: '血氧饱和度' },
          breathing_rate: { value: 16.8, unit: '次/分', label: '呼吸频率' }
        }

        analysisResult.value = mockResult

        isAnalyzing.value = true
        analysisProgress.value = 0

        const analysisInterval = setInterval(() => {
          analysisProgress.value += 20
          if (analysisProgress.value >= 100) {
            clearInterval(analysisInterval)
            isAnalyzing.value = false
            nextStep()
          }
        }, 500)

        return
      }

      const errorData = await response.json().catch(() => ({}))

      // 特殊处理视频格式错误
      if (response.status === 400 && errorData.detail &&
          (errorData.detail.includes('format') || errorData.detail.includes('格式'))) {
        throw new Error('视频格式不支持，请确保录制的视频为MP4或MOV格式')
      }

      throw new Error(errorData.detail || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)

    // 根据错误类型给出不同的提示
    let errorMessage = error.message
    if (error.message.includes('format') || error.message.includes('格式')) {
      errorMessage = '视频格式不支持，请重新录制视频'
    } else if (error.message.includes('size') || error.message.includes('大小')) {
      errorMessage = '视频文件过大，请录制较短的视频'
    } else if (error.message.includes('network') || error.message.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络后重试'
    }

    ElMessage.error(`上传失败：${errorMessage}`)
  } finally {
    isUploading.value = false
  }
}

// 步骤控制
const nextStep = async () => {
  if (currentStep.value < 3) {
    currentStep.value++

    // 如果从步骤1切换到步骤2（录制），需要确保录制视频元素也有摄像头流
    if (currentStep.value === 1 && mediaStream.value && recordingVideoElement.value) {
      await nextTick() // 等待DOM更新
      recordingVideoElement.value.srcObject = mediaStream.value

      // 确保录制视频元素开始播放
      try {
        await recordingVideoElement.value.play()
        console.log('录制视频元素摄像头流设置成功')
      } catch (error) {
        console.error('录制视频元素播放失败:', error)
      }
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 工具函数
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatMetricValue = (value) => {
  if (value === null || value === undefined) return '--'
  if (typeof value === 'number') {
    return value.toFixed(1)
  }
  return value
}

// 结果操作
const handleSaveReport = (analysisData) => {
  // 保存报告到本地存储或发送到服务器
  try {
    const reports = JSON.parse(localStorage.getItem('health_reports') || '[]')
    const newReport = {
      id: Date.now(),
      data: analysisData,
      timestamp: new Date().toISOString(),
      userId: userStore.user?.uid
    }
    reports.unshift(newReport)

    // 只保留最近50个报告
    if (reports.length > 50) {
      reports.splice(50)
    }

    localStorage.setItem('health_reports', JSON.stringify(reports))
    ElMessage.success('报告保存成功')
  } catch (error) {
    console.error('保存报告失败:', error)
    ElMessage.error('保存报告失败')
  }
}

const handleShareReport = (analysisData) => {
  // 分享报告功能
  if (navigator.share) {
    navigator.share({
      title: '健康分析报告',
      text: `我的健康分析报告 - 心率: ${analysisData.heart_rate?.value || '未知'}bpm`,
      url: window.location.href
    }).catch(console.error)
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.info('请手动复制链接分享')
    })
  }
}

const startNewScan = () => {
  currentStep.value = 0
  stopCamera()
  isRecording.value = false
  recordedBlob.value = null
  analysisResult.value = null

  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }

  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
}

const goToReports = () => {
  router.push('/report')
}

const goHome = () => {
  router.push('/home')
}

// 生命周期
onMounted(() => {
  const fuid = route.query.fuid
  if (fuid) {
    console.log('为家庭成员进行健康检测:', fuid)
  }
})

onUnmounted(() => {
  stopCamera()

  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
  }

  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
})

// 组件注册
defineOptions({
  components: {
    HealthAnalysisResult
  }
})
</script>

<style scoped>
.scan-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.scan-steps {
  margin-bottom: 40px;
}

.step-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.step-content h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #303133;
  text-align: center;
}

/* 摄像头相关样式 */
.camera-section {
  text-align: center;
}

.camera-container {
  position: relative;
  width: 100%;
  max-width: 640px;
  margin: 0 auto 20px;
  border-radius: 12px;
  overflow: hidden;
  background: #f5f7fa;
  aspect-ratio: 16/9;
}

.camera-container video {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.camera-container video.camera-active {
  background: #000;
}

.camera-placeholder {
  padding: 80px 20px;
  color: #909399;
}

.camera-placeholder p {
  margin: 20px 0 0 0;
  font-size: 16px;
}

.camera-controls {
  text-align: center;
}

.camera-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 录制相关样式 */
.recording-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background-color: #f56c6c;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.recording-info {
  margin-bottom: 20px;
  text-align: center;
}

.recording-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.recording-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 上传相关样式 */
.video-preview {
  text-align: center;
  margin-bottom: 20px;
}

.preview-video {
  width: 100%;
  max-width: 400px;
  max-height: 300px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-info {
  margin-top: 10px;
}

.video-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.upload-controls {
  text-align: center;
  margin-bottom: 20px;
}

.upload-controls .el-button {
  margin: 0 10px;
}

.upload-progress {
  max-width: 400px;
  margin: 0 auto;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

/* 分析结果样式 */
.analyzing-state {
  text-align: center;
  padding: 40px 20px;
}

.analyzing-icon {
  color: #409eff;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.analyzing-state p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 16px;
}

/* 基本信息样式 */
.basic-info-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.basic-info-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

/* 健康指标卡片样式 */
.analysis-results h4 {
  margin: 30px 0 20px 0;
  color: #303133;
  font-size: 18px;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #f0f0f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.metric-info h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
  border: none;
  padding: 0;
}

.metric-value {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.metric-value span {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.metric-status {
  margin: 0;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.metric-status.normal {
  background: #f0f9ff;
  color: #409eff;
}

/* 操作按钮样式 */
.result-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.result-actions .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scan-page {
    padding: 15px;
  }

  .step-content {
    padding: 20px;
  }

  .camera-container {
    max-width: 100%;
    margin: 0 auto 15px;
  }

  .preview-video {
    max-width: 100%;
    max-height: 250px;
  }

  .camera-actions,
  .recording-actions,
  .upload-controls .el-button,
  .result-actions .el-button {
    width: 100%;
    margin: 5px 0;
  }

  .camera-actions,
  .recording-actions {
    flex-direction: column;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .metric-card {
    margin-bottom: 15px;
  }

  .upload-controls {
    margin-bottom: 15px;
  }

  .upload-controls .el-button {
    margin: 5px 0;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 24px;
  }

  .step-content h3 {
    font-size: 18px;
  }

  .metric-value {
    font-size: 20px;
  }
}
</style>
