<template>
  <PageContainer title="健康概览" description="查看您和家人的健康状态">
    <template #extra>
      <el-button type="primary" @click="goToScan">
        <el-icon><VideoCamera /></el-icon>
        开始检测
      </el-button>
    </template>

      <!-- 加载状态 -->
      <LoadingSpinner v-if="healthStore.isLoading" text="加载健康数据中..." />

      <!-- 主要内容 -->
      <div v-else class="home-content">
        <!-- 快速操作区域 -->
        <div class="quick-actions">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="action-card" @click="goToScan">
                <el-icon class="action-icon" :size="32" color="#409eff">
                  <VideoCamera />
                </el-icon>
                <h4>健康扫描</h4>
                <p>开始视频健康检测</p>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="action-card" @click="goToFamily">
                <el-icon class="action-icon" :size="32" color="#67c23a">
                  <UserFilled />
                </el-icon>
                <h4>家庭成员</h4>
                <p>管理家庭成员信息</p>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="action-card" @click="goToReports">
                <el-icon class="action-icon" :size="32" color="#e6a23c">
                  <Document />
                </el-icon>
                <h4>健康报告</h4>
                <p>查看历史检测报告</p>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="action-card" @click="goToProfile">
                <el-icon class="action-icon" :size="32" color="#f56c6c">
                  <User />
                </el-icon>
                <h4>个人中心</h4>
                <p>管理个人信息</p>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 家庭成员选择 -->
        <div class="member-selector" v-if="familyMembers.length > 0">
          <el-card>
            <div class="selector-header">
              <h3>查看健康数据</h3>
              <el-select
                v-model="selectedMemberFuid"
                placeholder="选择家庭成员"
                @change="onMemberChange"
                style="width: 200px;"
              >
                <el-option
                  v-for="member in familyMembers"
                  :key="member.fuid"
                  :label="member.name + (member.relationship === '本人' ? ' (本人)' : ` (${member.relationship})`)"
                  :value="member.fuid"
                />
              </el-select>
            </div>
          </el-card>
        </div>

        <!-- 健康概览 -->
        <div class="health-overview">
          <!-- 使用HealthAnalysisResult组件显示完整的健康数据 -->
          <HealthAnalysisResult
            v-if="formattedHealthData"
            :analysis-data="formattedHealthData"
            @save-report="handleSaveReport"
            @share-report="handleShareReport"
          />

          <div v-else-if="isLoadingData" class="loading-data">
            <el-empty description="正在加载健康数据...">
              <el-icon class="is-loading"><Loading /></el-icon>
            </el-empty>
          </div>

          <div v-else class="no-data">
            <el-empty description="暂无健康数据">
              <template #description>
                <p>{{ selectedMemberName }}还没有健康检测记录</p>
              </template>
              <el-button type="primary" @click="goToScan">
                立即检测
              </el-button>
            </el-empty>
          </div>
        </div>

        <!-- 家庭成员健康状态 -->
        <div v-if="familyStore.hasFamilyMembers" class="family-health">
          <h3 class="section-title">家庭成员</h3>
          <div class="family-members">
            <el-row :gutter="20">
              <el-col 
                v-for="member in familyStore.familyMembers" 
                :key="member.fuid"
                :xs="24" :sm="12" :lg="8"
              >
                <div class="member-card" @click="viewMemberHealth(member)">
                  <div class="member-info">
                    <el-avatar :size="48" :src="member.avatar_url">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div class="member-details">
                      <h4>{{ member.name }}</h4>
                      <p>{{ member.relationship }}</p>
                      <el-tag size="small" :type="member.gender === '男' ? 'primary' : 'danger'">
                        {{ member.gender }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="member-actions">
                    <el-button size="small" type="primary" @click.stop="scanForMember(member)">
                      检测
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 最近检测记录 -->
        <div v-if="recentReports.length > 0" class="recent-reports">
          <h3 class="section-title">最近检测</h3>
          <el-table :data="recentReports" style="width: 100%">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="create_time" label="检测时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.create_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="heart_rate" label="心率" width="100">
              <template #default="{ row }">
                {{ row.heart_rate || '--' }} bpm
              </template>
            </el-table-column>
            <el-table-column prop="risk_assessment" label="风险评估" width="120">
              <template #default="{ row }">
                <el-tag :type="getRiskStatusType(row.risk_assessment)" size="small">
                  {{ row.risk_assessment || '未知' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewReport(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useHealthStore } from '@/stores/health'
import { useFamilyStore } from '@/stores/family'
import { ElMessage } from 'element-plus'
import { VideoCamera, UserFilled, Document, User, Loading } from '@element-plus/icons-vue'
import PageContainer from '@/components/Layout/PageContainer.vue'
import LoadingSpinner from '@/components/Common/LoadingSpinner.vue'
import EmptyState from '@/components/Common/EmptyState.vue'
import HealthMetricCard from '@/components/Common/HealthMetricCard.vue'
import HealthAnalysisResult from '@/components/HealthAnalysisResult.vue'
import { formatDateTime, getHeartRateCategory, getBloodPressureCategory, getRiskColor } from '@/utils/helpers'

const router = useRouter()
const userStore = useUserStore()
const healthStore = useHealthStore()
const familyStore = useFamilyStore()

// 响应式数据
const recentReports = ref([])
const selectedMemberFuid = ref(null)
const isLoadingData = ref(false)

// 计算属性
const homeData = computed(() => healthStore.homeData)
const familyMembers = computed(() => familyStore.familyMembers || [])
const selectedMemberName = computed(() => {
  if (!selectedMemberFuid.value) return '当前用户'
  const member = familyMembers.value.find(m => m.fuid === selectedMemberFuid.value)
  return member ? member.name : '当前用户'
})

// 格式化健康数据为HealthAnalysisResult组件所需的格式
const formattedHealthData = computed(() => {
  const data = homeData.value?.health_report
  const familyMember = homeData.value?.family_member
  const bvpWaveform = homeData.value?.bvp_waveform

  if (!data && !familyMember) {
    // 返回空状态数据，保持界面结构
    return {
      name: { value: '未知用户', unit: '', label: '姓名' },
      gender: { value: '未知', unit: '', label: '性别' },
      age: { value: '未知', unit: '', label: '年龄' },
      height: { value: '未知', unit: 'cm', label: '身高' },
      weight: { value: '未知', unit: 'kg', label: '体重' },
      bmi: { value: '未知', unit: '', label: 'BMI' },
      heart_rate: { value: null, unit: 'bpm', label: '心率' },
      blood_pressure: {
        value: { SBP: null, DBP: null },
        unit: 'mmHg',
        label: '血压'
      },
      spo2: { value: null, unit: '%', label: '血氧饱和度' },
      breathing_rate: { value: null, unit: '次/分', label: '呼吸频率' },
      cardiac_risk: { value: '未知', unit: '', label: '心脏风险' },
      brain_risk: { value: '未知', unit: '', label: '脑风险' },
      afib: { value: '未知', unit: '', label: '房颤风险' },
      signal_quality: { value: null, unit: '', label: '信号质量' }
    }
  }

  // 有数据时返回格式化的真实数据
  return {
    name: {
      value: familyMember?.name || '未知用户',
      unit: '',
      label: '姓名'
    },
    gender: {
      value: familyMember?.gender || '未知',
      unit: '',
      label: '性别'
    },
    age: {
      value: familyMember?.age || '未知',
      unit: '岁',
      label: '年龄'
    },
    height: {
      value: familyMember?.height || '未知',
      unit: 'cm',
      label: '身高'
    },
    weight: {
      value: familyMember?.weight || '未知',
      unit: 'kg',
      label: '体重'
    },
    bmi: {
      value: data?.bmi || '未知',
      unit: '',
      label: 'BMI'
    },
    heart_rate: {
      value: data?.heart_rate,
      unit: 'bpm',
      label: '心率'
    },
    blood_pressure: {
      value: data?.blood_pressure ? {
        SBP: data.blood_pressure.systolic || data.blood_pressure.SBP,
        DBP: data.blood_pressure.diastolic || data.blood_pressure.DBP
      } : { SBP: null, DBP: null },
      unit: 'mmHg',
      label: '血压'
    },
    spo2: {
      value: data?.spo2,
      unit: '%',
      label: '血氧饱和度'
    },
    breathing_rate: {
      value: data?.breathing_rate,
      unit: '次/分',
      label: '呼吸频率'
    },
    cardiac_risk: {
      value: data?.cardiac_risk || '未知',
      unit: '',
      label: '心脏风险'
    },
    brain_risk: {
      value: data?.brain_risk || '未知',
      unit: '',
      label: '脑风险'
    },
    afib: {
      value: data?.afib || '未知',
      unit: '',
      label: '房颤风险'
    },
    signal_quality: {
      value: data?.signal_quality,
      unit: '',
      label: '信号质量'
    },
    // 添加HRV数据（如果有）
    hrv: data?.hrv ? {
      time_domain: { value: data.hrv.time_domain, unit: '', label: '时域指标' },
      frequency_domain: { value: data.hrv.frequency_domain, unit: '', label: '频域指标' },
      nonlinear: { value: data.hrv.nonlinear, unit: '', label: '非线性指标' }
    } : null,
    // 添加BVP波形数据（如果有）
    bvp_waveform: bvpWaveform ? {
      value: bvpWaveform,
      unit: '',
      label: 'BVP波形'
    } : null
  }
})

// 获取心率状态
const getHeartRateStatus = (heartRate) => {
  if (!heartRate) return '未知'
  const category = getHeartRateCategory(heartRate)
  return category.text
}

const getHeartRateStatusType = (heartRate) => {
  if (!heartRate) return 'info'
  const category = getHeartRateCategory(heartRate)
  if (category.text === '正常') return 'success'
  if (category.text === '偏快' || category.text === '偏慢') return 'warning'
  return 'danger'
}

// 获取血压显示文本
const getBloodPressureDisplay = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return '--/--'
  }
  return `${bloodPressure.systolic}/${bloodPressure.diastolic}`
}

// 获取血压状态
const getBloodPressureStatus = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return '未知'
  }
  const category = getBloodPressureCategory(bloodPressure.systolic, bloodPressure.diastolic)
  return category.text
}

const getBloodPressureStatusType = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return 'info'
  }
  const category = getBloodPressureCategory(bloodPressure.systolic, bloodPressure.diastolic)
  if (category.text === '正常') return 'success'
  if (category.text === '正常高值' || category.text === '轻度高血压') return 'warning'
  return 'danger'
}

// 获取血氧状态
const getSpo2Status = (spo2) => {
  if (!spo2) return '未知'
  if (spo2 >= 95) return '正常'
  if (spo2 >= 90) return '偏低'
  return '过低'
}

const getSpo2StatusType = (spo2) => {
  if (!spo2) return 'info'
  if (spo2 >= 95) return 'success'
  if (spo2 >= 90) return 'warning'
  return 'danger'
}

// 获取风险状态类型
const getRiskStatusType = (risk) => {
  if (!risk) return 'info'
  const riskLower = risk.toLowerCase()
  if (riskLower.includes('低') || riskLower.includes('low')) return 'success'
  if (riskLower.includes('中') || riskLower.includes('medium')) return 'warning'
  if (riskLower.includes('高') || riskLower.includes('high')) return 'danger'
  return 'info'
}

// 页面跳转方法
const goToScan = () => {
  router.push('/scan')
}

const goToFamily = () => {
  router.push('/family')
}

const goToReports = () => {
  router.push('/report')
}

const goToProfile = () => {
  router.push('/profile')
}

// HealthAnalysisResult组件事件处理
const handleSaveReport = (analysisData) => {
  // 保存报告到本地存储
  try {
    const reports = JSON.parse(localStorage.getItem('health_reports') || '[]')
    const newReport = {
      id: Date.now(),
      data: analysisData,
      timestamp: new Date().toISOString(),
      userId: userStore.user?.uid
    }
    reports.unshift(newReport)

    // 只保留最近50个报告
    if (reports.length > 50) {
      reports.splice(50)
    }

    localStorage.setItem('health_reports', JSON.stringify(reports))
    ElMessage.success('报告保存成功')
  } catch (error) {
    console.error('保存报告失败:', error)
    ElMessage.error('保存报告失败')
  }
}

const handleShareReport = (analysisData) => {
  // 分享报告功能
  if (navigator.share) {
    navigator.share({
      title: '健康分析报告',
      text: `我的健康分析报告 - 心率: ${analysisData.heart_rate?.value || '未知'}bpm`,
      url: window.location.href
    }).catch(console.error)
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.info('请手动复制链接分享')
    })
  }
}

const goToReportDetail = () => {
  if (homeData.value?.health_report?.report_id) {
    router.push(`/report/${homeData.value.health_report.report_id}`)
  }
}

// 查看家庭成员健康状态
const viewMemberHealth = (member) => {
  router.push(`/report?fuid=${member.fuid}`)
}

// 为家庭成员进行检测
const scanForMember = (member) => {
  router.push(`/scan?fuid=${member.fuid}`)
}

// 查看报告详情
const viewReport = (report) => {
  router.push(`/report/${report.report_id}`)
}

// 家庭成员选择处理
const onMemberChange = async (fuid) => {
  if (fuid === selectedMemberFuid.value) return

  selectedMemberFuid.value = fuid
  await loadMemberData(fuid)
}

// 加载指定成员的数据
const loadMemberData = async (fuid) => {
  try {
    isLoadingData.value = true
    await healthStore.fetchHomeData(userStore.userId, fuid)
  } catch (error) {
    console.error('加载成员数据失败:', error)
    ElMessage.error('加载健康数据失败')
  } finally {
    isLoadingData.value = false
  }
}

// 加载数据
const loadData = async () => {
  try {
    isLoadingData.value = true

    // 先加载家庭成员列表
    await familyStore.fetchFamilyMembers(userStore.userId)

    // 查找"本人"记录
    const selfMember = familyStore.familyMembers.find(member => member.relationship === '本人')

    if (selfMember) {
      // 如果有"本人"记录，默认选择并加载该成员的健康数据
      selectedMemberFuid.value = selfMember.fuid
      await healthStore.fetchHomeData(userStore.userId, selfMember.fuid)
    } else if (familyStore.familyMembers.length > 0) {
      // 如果没有"本人"记录但有其他家庭成员，选择第一个
      const firstMember = familyStore.familyMembers[0]
      selectedMemberFuid.value = firstMember.fuid
      await healthStore.fetchHomeData(userStore.userId, firstMember.fuid)
    } else {
      // 如果没有任何家庭成员记录，加载用户的基本数据（可能为空）
      await healthStore.fetchHomeData(userStore.userId)
    }

    // 加载最近的检测记录
    // recentReports.value = await healthStore.fetchReportHistory(userStore.userId, null, 5)

  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    isLoadingData.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.home-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.member-selector {
  margin-bottom: 20px;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-header h3 {
  margin: 0;
  color: #303133;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.action-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.action-icon {
  margin-bottom: 12px;
}

.action-card h4 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.action-card p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.section-title {
  margin: 0 0 20px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.health-metrics {
  margin-bottom: 20px;
}

.no-health-data {
  background: #fff;
  border-radius: 8px;
  padding: 40px;
}

.family-members {
  margin-bottom: 20px;
}

.member-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.member-details h4 {
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.member-details p {
  margin: 0 0 8px;
  font-size: 14px;
  color: #606266;
}

.member-actions {
  display: flex;
  gap: 8px;
}

.recent-reports {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-content {
    gap: 20px;
  }

  .action-card {
    height: 120px;
    padding: 20px;
  }

  .action-card h4 {
    font-size: 14px;
  }

  .action-card p {
    font-size: 11px;
  }

  .member-card {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .member-info {
    justify-content: center;
  }

  .member-actions {
    justify-content: center;
  }
}
</style>
