<template>
  <PageContainer title="个人中心" description="管理您的个人信息和账户设置">
      <div class="profile-content">
        <!-- 用户信息卡片 -->
        <div class="user-info-card">
          <div class="user-header">
            <el-avatar :size="80" :src="selfMember ? getAvatarUrl(selfMember) : getDefaultAvatar()">
              <el-icon :size="40"><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <h2>{{ userStore.user?.email || '—' }}</h2>
              <p>用户邮箱: {{ userStore.user?.email || '—' }}</p>
              <el-tag type="success" size="small">已认证</el-tag>
            </div>
            <div class="user-actions">
              <el-button type="primary" @click="showEditProfile">
                编辑资料
              </el-button>
            </div>
          </div>
        </div>

        <!-- 个人基本信息卡片 -->
        <div class="personal-info-card">
          <div class="card-header">
            <h3>个人基本信息</h3>
            <el-button v-if="!selfMember" type="primary" @click="showAddSelfInfo">
              添加个人信息
            </el-button>
            <el-button v-else type="text" @click="showEditSelfInfo">
              编辑
            </el-button>
          </div>
          <div class="personal-info-content">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">姓名:</span>
                  <span class="info-value">{{ selfMember?.name || '—' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">性别:</span>
                  <span class="info-value">{{ selfMember?.gender || '—' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">年龄:</span>
                  <span class="info-value">{{ selfMember ? calculateAge(selfMember.birth_year) : '—' }}{{ selfMember ? '岁' : '' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">身高:</span>
                  <span class="info-value">{{ selfMember?.height || '—' }}{{ selfMember?.height ? 'cm' : '' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">体重:</span>
                  <span class="info-value">{{ selfMember?.weight || '—' }}{{ selfMember?.weight ? 'kg' : '' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="8">
                <div class="info-item">
                  <span class="info-label">BMI:</span>
                  <span class="info-value">{{ selfMember && selfMember.height && selfMember.weight ? calculateBMI(selfMember.height, selfMember.weight) : '—' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 功能菜单 -->
        <div class="feature-menu">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="menu-item" @click="goToReports">
                <el-icon class="menu-icon" :size="32" color="#409eff">
                  <Document />
                </el-icon>
                <h4>健康报告</h4>
                <p>查看历史检测报告</p>
                <el-badge :value="reportCount" :max="99" class="menu-badge" />
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="menu-item" @click="goToFamily">
                <el-icon class="menu-icon" :size="32" color="#67c23a">
                  <UserFilled />
                </el-icon>
                <h4>家庭成员</h4>
                <p>管理家庭成员信息</p>
                <el-badge :value="familyStore.familyMemberCount" :max="99" class="menu-badge" />
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="menu-item" @click="showPasswordDialog">
                <el-icon class="menu-icon" :size="32" color="#e6a23c">
                  <Lock />
                </el-icon>
                <h4>修改密码</h4>
                <p>更改登录密码</p>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="menu-item" @click="showSettings">
                <el-icon class="menu-icon" :size="32" color="#f56c6c">
                  <Setting />
                </el-icon>
                <h4>系统设置</h4>
                <p>个性化设置</p>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activity">
          <h3 class="section-title">最近活动</h3>
          <div v-if="recentActivities.length > 0" class="activity-list">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon :size="20" :color="activity.iconColor">
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-title">{{ activity.title }}</p>
                <p class="activity-time">{{ formatDateTime(activity.time) }}</p>
              </div>
            </div>
          </div>
          <EmptyState 
            v-else
            icon="Clock"
            title="暂无活动记录"
            description="开始使用系统后，这里会显示您的活动记录"
          />
        </div>

        <!-- 统计信息 -->
        <div class="statistics">
          <h3 class="section-title">使用统计</h3>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ statistics.totalScans }}</div>
                <div class="stat-label">总检测次数</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ statistics.thisMonthScans }}</div>
                <div class="stat-label">本月检测</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ familyStore.familyMemberCount }}</div>
                <div class="stat-label">家庭成员</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ daysSinceRegistration }}</div>
                <div class="stat-label">使用天数</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 编辑资料对话框 -->
      <el-dialog
        v-model="editProfileVisible"
        title="编辑个人资料"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="80px"
        >
          <el-form-item label="用户名" prop="name">
            <el-input v-model="profileForm.name" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="profileForm.email" placeholder="请输入邮箱" disabled />
            <div class="form-tip">邮箱地址不可修改</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="editProfileVisible = false">取消</el-button>
          <el-button type="primary" :loading="isUpdating" @click="updateProfile">
            保存
          </el-button>
        </template>
      </el-dialog>

      <!-- 修改密码对话框 -->
      <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
        >
          <el-form-item label="当前密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入当前密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码（至少6位）"
              show-password
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="isChangingPassword" @click="changePassword">
            修改密码
          </el-button>
        </template>
      </el-dialog>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useFamilyStore } from '@/stores/family'
import PageContainer from '@/components/Layout/PageContainer.vue'
import EmptyState from '@/components/Common/EmptyState.vue'
import { formatDateTime, validateEmail, validatePassword, calculateAge, calculateBMI } from '@/utils/helpers'
import { getAvatarUrl, getDefaultAvatar } from '@/utils/avatarUtils'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const familyStore = useFamilyStore()

// 计算属性：获取"本人"家庭成员信息
const selfMember = computed(() => {
  return familyStore.familyMembers.find(member => member.relationship === '本人')
})

// 表单引用
const profileFormRef = ref(null)
const passwordFormRef = ref(null)

// 对话框状态
const editProfileVisible = ref(false)
const passwordDialogVisible = ref(false)
const isUpdating = ref(false)
const isChangingPassword = ref(false)

// 用户头像
const userAvatar = ref('')

// 报告数量
const reportCount = ref(0)

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '完成健康检测',
    time: new Date(),
    icon: 'VideoCamera',
    iconColor: '#409eff'
  },
  {
    id: 2,
    title: '添加家庭成员',
    time: new Date(Date.now() - 86400000),
    icon: 'UserFilled',
    iconColor: '#67c23a'
  }
])

// 统计信息
const statistics = reactive({
  totalScans: 0,
  thisMonthScans: 0
})

// 编辑资料表单
const profileForm = reactive({
  name: '',
  email: ''
})

// 修改密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const profileRules = {
  name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2-20个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const result = validatePassword(value)
        if (!result.valid) {
          callback(new Error(result.message))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const daysSinceRegistration = computed(() => {
  // 这里应该从用户信息中获取注册时间
  // 暂时返回一个示例值
  return 30
})

// 显示编辑资料对话框
const showEditProfile = () => {
  profileForm.name = userStore.userName
  profileForm.email = userStore.userEmail
  editProfileVisible.value = true
}

// 更新个人资料
const updateProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    isUpdating.value = true

    // 调用更新用户信息API
    // await userApi.updateUser({ name: profileForm.name })

    // 更新本地状态
    userStore.updateUserInfo({ name: profileForm.name })

    ElMessage.success('个人资料更新成功')
    editProfileVisible.value = false

  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    isUpdating.value = false
  }
}

// 显示修改密码对话框
const showPasswordDialog = () => {
  // 重置表单
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordDialogVisible.value = true
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    isChangingPassword.value = true

    // 调用修改密码API
    // await userApi.changePassword({
    //   old_password: passwordForm.oldPassword,
    //   new_password: passwordForm.newPassword
    // })

    ElMessage.success('密码修改成功，请重新登录')
    passwordDialogVisible.value = false

    // 退出登录
    setTimeout(() => {
      userStore.logout()
      router.push('/login')
    }, 1000)

  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    isChangingPassword.value = false
  }
}

// 个人信息管理
const showAddSelfInfo = () => {
  router.push('/family?action=add&relationship=本人')
}

const showEditSelfInfo = () => {
  if (selfMember.value) {
    router.push(`/family?action=edit&fuid=${selfMember.value.fuid}`)
  }
}

// 页面跳转
const goToReports = () => {
  router.push('/report')
}

const goToFamily = () => {
  router.push('/family')
}

const showSettings = () => {
  ElMessage.info('设置功能开发中...')
}

// 加载数据
const loadData = async () => {
  try {
    // 加载家庭成员数量
    await familyStore.fetchFamilyMembers(userStore.userId)

    // 加载统计信息
    // statistics.totalScans = await getStatistics()

  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.user-info-card {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personal-info-card {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.personal-info-content {
  padding: 16px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
  font-weight: 600;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-details {
  flex: 1;
}

.user-details h2 {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.user-details p {
  margin: 0 0 12px;
  font-size: 14px;
  color: #606266;
}

.feature-menu {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.menu-item {
  position: relative;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.menu-item:hover {
  background: #fff;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.menu-icon {
  margin-bottom: 12px;
}

.menu-item h4 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.menu-item p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.menu-badge {
  position: absolute;
  top: 16px;
  right: 16px;
}

.section-title {
  margin: 0 0 20px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.recent-activity {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-content {
  flex: 1;
}

.activity-title {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.activity-time {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.statistics {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    gap: 20px;
  }

  .user-info-card,
  .feature-menu,
  .recent-activity,
  .statistics {
    padding: 20px;
  }

  .user-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .user-details h2 {
    font-size: 20px;
  }

  .menu-item {
    height: 120px;
    padding: 20px;
  }

  .menu-item h4 {
    font-size: 14px;
  }

  .menu-item p {
    font-size: 11px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>
