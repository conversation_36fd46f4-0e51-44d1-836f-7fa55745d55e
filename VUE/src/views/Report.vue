<template>
  <PageContainer
    :title="pageTitle"
    :description="pageDescription"
    :breadcrumb="breadcrumbItems"
  >
      <template #extra>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button v-if="currentReport" type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </template>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 报告列表模式 -->
      <div v-else-if="!selectedReportId" class="report-list-mode">
        <!-- 筛选控件 -->
        <div class="filter-section">
          <el-row :gutter="20" align="middle">
            <el-col :span="8">
              <el-select v-model="selectedMember" placeholder="选择家庭成员" @change="loadReports">
                <el-option label="本人" value="self" />
                <el-option
                  v-for="member in familyMembers"
                  :key="member.fuid"
                  :label="member.name"
                  :value="member.fuid"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="loadReports">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 报告卡片列表 -->
        <div v-if="reports.length > 0" class="reports-grid">
          <div
            v-for="report in reports"
            :key="report.report_id"
            class="report-card"
            @click="viewReportDetail(report)"
          >
            <div class="report-card-header">
              <h4>{{ formatDateTime(report.create_time) }}</h4>
              <el-tag :type="getReportStatusType(report)" size="small">
                {{ getReportStatus(report) }}
              </el-tag>
            </div>
            <div class="report-summary">
              <div class="metric-row">
                <div class="metric-item">
                  <span class="metric-label">心率:</span>
                  <span class="metric-value">{{ report.heart_rate || '—' }}{{ report.heart_rate ? ' bpm' : '' }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">血氧:</span>
                  <span class="metric-value">{{ report.spo2 || '—' }}{{ report.spo2 ? '%' : '' }}</span>
                </div>
              </div>
              <div class="metric-row">
                <div class="metric-item">
                  <span class="metric-label">血压:</span>
                  <span class="metric-value">
                    {{ report.blood_pressure ? `${report.blood_pressure.SBP}/${report.blood_pressure.DBP} mmHg` : '—' }}
                  </span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">风险:</span>
                  <span class="metric-value">{{ report.cardiac_risk || '—' }}</span>
                </div>
              </div>
            </div>
            <div class="report-actions">
              <el-button type="text" size="small">查看详情</el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <el-empty :description="emptyDescription">
            <el-button type="primary" @click="goToScan">
              立即检测
            </el-button>
          </el-empty>
        </div>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 报告详情模式 -->
      <div v-else-if="currentReport" class="report-detail-mode">
        <div class="detail-header">
          <el-button @click="backToList">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <h3>{{ formatDateTime(currentReport.create_time) }} 检测报告</h3>
        </div>

        <!-- 使用HealthAnalysisResult组件显示详细报告 -->
        <HealthAnalysisResult
          v-if="formattedReportData"
          :analysis-data="formattedReportData"
          @save-report="handleSaveReport"
          @share-report="handleShareReport"
        />
      </div>

      <!-- 原有的报告详情内容（兼容性保留） -->
      <div v-else-if="currentReport" class="report-content">
        <!-- 报告基本信息 -->
        <div class="report-header">
          <div class="report-info">
            <h2>{{ currentReport.name }}的健康报告</h2>
            <div class="report-meta">
              <span>检测时间：{{ formatDateTime(currentReport.create_time) }}</span>
              <span>报告ID：{{ currentReport.report_id }}</span>
              <el-tag :type="getRiskStatusType(currentReport.risk_assessment)" size="small">
                {{ currentReport.risk_assessment || '未知风险' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 核心健康指标 -->
        <div class="health-metrics">
          <h3 class="section-title">核心健康指标</h3>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="6">
              <HealthMetricCard
                title="心率"
                :value="currentReport.heart_rate || 0"
                unit="bpm"
                icon="TrendCharts"
                :status="getHeartRateStatus(currentReport.heart_rate)"
                :status-type="getHeartRateStatusType(currentReport.heart_rate)"
              />
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <HealthMetricCard
                title="血压"
                :value="getBloodPressureDisplay(currentReport.blood_pressure)"
                unit="mmHg"
                icon="Monitor"
                :status="getBloodPressureStatus(currentReport.blood_pressure)"
                :status-type="getBloodPressureStatusType(currentReport.blood_pressure)"
              />
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <HealthMetricCard
                title="血氧饱和度"
                :value="currentReport.spo2 || 0"
                unit="%"
                icon="Odometer"
                :status="getSpo2Status(currentReport.spo2)"
                :status-type="getSpo2StatusType(currentReport.spo2)"
              />
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <HealthMetricCard
                title="呼吸频率"
                :value="currentReport.breathing_rate || 0"
                unit="次/分"
                icon="Wind"
                :status="getBreathingRateStatus(currentReport.breathing_rate)"
                :status-type="getBreathingRateStatusType(currentReport.breathing_rate)"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 风险评估 -->
        <div class="risk-assessment">
          <h3 class="section-title">风险评估</h3>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="risk-card">
                <h4>心脏风险</h4>
                <el-tag :type="getRiskStatusType(currentReport.cardiac_risk)" size="large">
                  {{ currentReport.cardiac_risk || '未知' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="risk-card">
                <h4>脑血管风险</h4>
                <el-tag :type="getRiskStatusType(currentReport.brain_risk)" size="large">
                  {{ currentReport.brain_risk || '未知' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="risk-card">
                <h4>房颤风险</h4>
                <el-tag :type="getRiskStatusType(currentReport.afib)" size="large">
                  {{ currentReport.afib || '未知' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :lg="6">
              <div class="risk-card">
                <h4>心律不齐</h4>
                <el-tag :type="getRiskStatusType(currentReport.arrhythmia)" size="large">
                  {{ currentReport.arrhythmia || '未知' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- BVP波形图 -->
        <div v-if="bvpData" class="bvp-waveform">
          <h3 class="section-title">BVP波形分析</h3>
          <LineChart
            title="血容量脉搏波形"
            subtitle="反映心血管系统的功能状态"
            :data="bvpData.bvp"
            :x-axis-data="bvpData.timestamps"
            :height="300"
            color="#409eff"
            :smooth="true"
            :show-area="true"
            y-axis-name="幅值"
            x-axis-name="时间(s)"
          />
        </div>

        <!-- 身体指标 -->
        <div class="body-metrics">
          <h3 class="section-title">身体指标</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="metric-item">
                <span class="metric-label">身高</span>
                <span class="metric-value">{{ currentReport.height || '--' }} cm</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <span class="metric-label">体重</span>
                <span class="metric-value">{{ currentReport.weight || '--' }} kg</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <span class="metric-label">BMI</span>
                <span class="metric-value">{{ currentReport.bmi || '--' }}</span>
                <el-tag 
                  v-if="currentReport.bmi" 
                  :type="getBMIStatusType(currentReport.bmi)" 
                  size="small"
                >
                  {{ getBMIStatus(currentReport.bmi) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 其他指标 -->
        <div v-if="currentReport.hemoglobin || currentReport.anemia" class="other-metrics">
          <h3 class="section-title">其他指标</h3>
          <el-row :gutter="20">
            <el-col v-if="currentReport.hemoglobin" :span="12">
              <div class="metric-item">
                <span class="metric-label">血红蛋白</span>
                <span class="metric-value">{{ currentReport.hemoglobin }} g/L</span>
              </div>
            </el-col>
            <el-col v-if="currentReport.anemia" :span="12">
              <div class="metric-item">
                <span class="metric-label">贫血状态</span>
                <el-tag :type="currentReport.anemia === '否' ? 'success' : 'warning'" size="small">
                  {{ currentReport.anemia }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 信号质量 -->
        <div v-if="currentReport.signal_quality" class="signal-quality">
          <h3 class="section-title">信号质量</h3>
          <div class="quality-indicator">
            <el-progress 
              :percentage="Math.round(currentReport.signal_quality * 100)" 
              :stroke-width="12"
              :color="getQualityColor(currentReport.signal_quality)"
            />
            <p class="quality-text">
              信号质量：{{ getQualityText(currentReport.signal_quality) }}
            </p>
          </div>
        </div>

        <!-- 健康建议 -->
        <div class="health-suggestions">
          <h3 class="section-title">健康建议</h3>
          <div class="suggestions-content">
            <el-alert
              :title="getHealthSuggestionTitle(currentReport.risk_assessment)"
              :type="getSuggestionAlertType(currentReport.risk_assessment)"
              :description="getHealthSuggestions(currentReport)"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>

      <!-- 报告列表 -->
      <div v-else class="report-list">
        <h3 class="section-title">健康报告列表</h3>
        <!-- 这里可以显示报告列表 -->
        <EmptyState
          icon="Document"
          title="暂无健康报告"
          description="开始健康检测后，报告将显示在这里"
          action-text="开始检测"
          @action="goToScan"
        />
      </div>
  </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useHealthStore } from '@/stores/health'
import { useFamilyStore } from '@/stores/family'
import PageContainer from '@/components/Layout/PageContainer.vue'
import LoadingSpinner from '@/components/Common/LoadingSpinner.vue'
import EmptyState from '@/components/Common/EmptyState.vue'
import HealthMetricCard from '@/components/Common/HealthMetricCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import HealthAnalysisResult from '@/components/HealthAnalysisResult.vue'
import {
  formatDateTime,
  getHeartRateCategory,
  getBloodPressureCategory,
  getBMICategory
} from '@/utils/helpers'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Download, TrendCharts, Monitor, Odometer, Wind, Refresh } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const healthStore = useHealthStore()
const familyStore = useFamilyStore()

// 当前报告数据
const currentReport = ref(null)

// 报告列表相关数据
const reports = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const isLoading = ref(false)
const selectedMember = ref('self')
const selectedReportId = ref(null)
const familyMembers = ref([])

// 计算属性
const emptyDescription = computed(() => {
  if (selectedMember.value === 'self') {
    return '您还没有健康检测记录'
  } else {
    const member = familyMembers.value.find(m => m.fuid === selectedMember.value)
    return `${member?.name || '该成员'}还没有健康检测记录`
  }
})

const formattedReportData = computed(() => {
  if (!currentReport.value) return null

  return {
    basic_info: {
      name: currentReport.value.member_name || currentReport.value.name,
      create_time: currentReport.value.create_time,
      report_id: currentReport.value.report_id
    },
    metrics: {
      heart_rate: currentReport.value.heart_rate,
      blood_pressure: currentReport.value.blood_pressure,
      spo2: currentReport.value.spo2,
      breathing_rate: currentReport.value.breathing_rate
    },
    risk_assessment: {
      cardiac_risk: currentReport.value.cardiac_risk,
      brain_risk: currentReport.value.brain_risk,
      afib: currentReport.value.afib
    },
    signal_quality: currentReport.value.signal_quality
  }
})
const bvpData = ref(null)

// 计算属性
const pageTitle = computed(() => {
  if (currentReport.value) {
    return `${currentReport.value.name}的健康报告`
  }
  return '健康报告'
})

const pageDescription = computed(() => {
  if (currentReport.value) {
    return `检测时间：${formatDateTime(currentReport.value.create_time)}`
  }
  return '查看详细的健康检测报告'
})

const breadcrumbItems = computed(() => {
  const items = [
    { title: '首页', path: '/home' },
    { title: '健康报告', path: '/report' }
  ]

  if (currentReport.value) {
    items.push({
      title: currentReport.value.name,
      path: route.path
    })
  }

  return items
})

// 获取心率状态
const getHeartRateStatus = (heartRate) => {
  if (!heartRate) return '未知'
  const category = getHeartRateCategory(heartRate)
  return category.text
}

const getHeartRateStatusType = (heartRate) => {
  if (!heartRate) return 'info'
  const category = getHeartRateCategory(heartRate)
  if (category.text === '正常') return 'success'
  if (category.text === '偏快' || category.text === '偏慢') return 'warning'
  return 'danger'
}

// 获取血压显示和状态
const getBloodPressureDisplay = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return '--/--'
  }
  return `${bloodPressure.systolic}/${bloodPressure.diastolic}`
}

const getBloodPressureStatus = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return '未知'
  }
  const category = getBloodPressureCategory(bloodPressure.systolic, bloodPressure.diastolic)
  return category.text
}

const getBloodPressureStatusType = (bloodPressure) => {
  if (!bloodPressure || !bloodPressure.systolic || !bloodPressure.diastolic) {
    return 'info'
  }
  const category = getBloodPressureCategory(bloodPressure.systolic, bloodPressure.diastolic)
  if (category.text === '正常') return 'success'
  if (category.text === '正常高值' || category.text === '轻度高血压') return 'warning'
  return 'danger'
}

// 获取血氧状态
const getSpo2Status = (spo2) => {
  if (!spo2) return '未知'
  if (spo2 >= 95) return '正常'
  if (spo2 >= 90) return '偏低'
  return '过低'
}

const getSpo2StatusType = (spo2) => {
  if (!spo2) return 'info'
  if (spo2 >= 95) return 'success'
  if (spo2 >= 90) return 'warning'
  return 'danger'
}

// 获取呼吸频率状态
const getBreathingRateStatus = (rate) => {
  if (!rate) return '未知'
  if (rate >= 12 && rate <= 20) return '正常'
  if (rate < 12) return '偏慢'
  return '偏快'
}

const getBreathingRateStatusType = (rate) => {
  if (!rate) return 'info'
  if (rate >= 12 && rate <= 20) return 'success'
  return 'warning'
}

// 获取风险状态类型
const getRiskStatusType = (risk) => {
  if (!risk) return 'info'
  const riskLower = risk.toLowerCase()
  if (riskLower.includes('低') || riskLower.includes('low')) return 'success'
  if (riskLower.includes('中') || riskLower.includes('medium')) return 'warning'
  if (riskLower.includes('高') || riskLower.includes('high')) return 'danger'
  return 'info'
}

// 获取BMI状态
const getBMIStatus = (bmi) => {
  if (!bmi) return '未知'
  const category = getBMICategory(bmi)
  return category.text
}

const getBMIStatusType = (bmi) => {
  if (!bmi) return 'info'
  const category = getBMICategory(bmi)
  if (category.text === '正常') return 'success'
  if (category.text === '偏瘦' || category.text === '超重') return 'warning'
  return 'danger'
}

// 获取信号质量相关
const getQualityColor = (quality) => {
  if (quality >= 0.8) return '#67c23a'
  if (quality >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const getQualityText = (quality) => {
  if (quality >= 0.8) return '优秀'
  if (quality >= 0.6) return '良好'
  if (quality >= 0.4) return '一般'
  return '较差'
}

// 获取健康建议
const getHealthSuggestionTitle = (riskAssessment) => {
  if (!riskAssessment) return '健康建议'
  const risk = riskAssessment.toLowerCase()
  if (risk.includes('低') || risk.includes('low')) return '健康状况良好'
  if (risk.includes('中') || risk.includes('medium')) return '需要关注'
  if (risk.includes('高') || risk.includes('high')) return '建议就医'
  return '健康建议'
}

const getSuggestionAlertType = (riskAssessment) => {
  if (!riskAssessment) return 'info'
  const risk = riskAssessment.toLowerCase()
  if (risk.includes('低') || risk.includes('low')) return 'success'
  if (risk.includes('中') || risk.includes('medium')) return 'warning'
  if (risk.includes('高') || risk.includes('high')) return 'error'
  return 'info'
}

const getHealthSuggestions = (report) => {
  const suggestions = []

  if (report.risk_assessment) {
    const risk = report.risk_assessment.toLowerCase()
    if (risk.includes('高') || risk.includes('high')) {
      suggestions.push('建议尽快咨询专业医生，进行进一步检查。')
    } else if (risk.includes('中') || risk.includes('medium')) {
      suggestions.push('建议定期监测健康状况，注意生活方式调整。')
    } else {
      suggestions.push('继续保持良好的生活习惯，定期进行健康检测。')
    }
  }

  if (report.heart_rate) {
    const category = getHeartRateCategory(report.heart_rate)
    if (category.text !== '正常') {
      suggestions.push('心率异常，建议适当调整运动强度和作息时间。')
    }
  }

  if (report.bmi && report.bmi > 24) {
    suggestions.push('BMI偏高，建议控制饮食，增加运动量。')
  }

  return suggestions.join(' ')
}

// 报告列表相关方法
const loadReports = async () => {
  isLoading.value = true
  try {
    const uid = userStore.user?.uid
    if (!uid) return

    let apiUrl
    if (selectedMember.value === 'self') {
      apiUrl = `/api/v1/home/<USER>/${uid}`
    } else {
      apiUrl = `/api/v1/home/<USER>/${uid}/${selectedMember.value}`
    }

    const response = await fetch(`${apiUrl}?limit=${pageSize.value}&offset=${(currentPage.value - 1) * pageSize.value}`, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      reports.value = data.reports || []
      total.value = data.total || 0
    } else {
      throw new Error('获取报告列表失败')
    }
  } catch (error) {
    console.error('加载报告列表失败:', error)
    ElMessage.error('加载报告列表失败')
  } finally {
    isLoading.value = false
  }
}

const loadFamilyMembers = async () => {
  try {
    await familyStore.fetchFamilyMembers(userStore.user?.uid)
    familyMembers.value = familyStore.familyMembers.filter(member => member.relationship !== '本人')
  } catch (error) {
    console.error('加载家庭成员失败:', error)
  }
}

const viewReportDetail = (report) => {
  selectedReportId.value = report.report_id
  currentReport.value = report
}

const backToList = () => {
  selectedReportId.value = null
  currentReport.value = null
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadReports()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadReports()
}

const getReportStatus = (report) => {
  if (report.cardiac_risk === '高风险' || report.brain_risk === '高风险') {
    return '高风险'
  } else if (report.cardiac_risk === '中风险' || report.brain_risk === '中风险') {
    return '中风险'
  } else {
    return '正常'
  }
}

const getReportStatusType = (report) => {
  const status = getReportStatus(report)
  switch (status) {
    case '高风险': return 'danger'
    case '中风险': return 'warning'
    default: return 'success'
  }
}

// 页面操作
const goBack = () => {
  router.go(-1)
}

const goToScan = () => {
  router.push('/scan')
}

const exportReport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSaveReport = () => {
  ElMessage.success('报告保存成功')
}

const handleShareReport = () => {
  ElMessage.info('分享功能开发中...')
}

// 加载报告数据
const loadReportData = async () => {
  const reportId = route.params.reportId
  const fuid = route.query.fuid

  try {
    if (reportId) {
      // 加载特定报告
      currentReport.value = await healthStore.fetchReportDetail(reportId)

      // 加载BVP波形数据
      if (currentReport.value?.report_id) {
        try {
          bvpData.value = await healthStore.getBvpWaveform(currentReport.value.report_id)
          // 处理BVP数据格式
          if (bvpData.value?.bvp && typeof bvpData.value.bvp === 'string') {
            bvpData.value.bvp = JSON.parse(bvpData.value.bvp)
          }
          if (bvpData.value?.timestamps && typeof bvpData.value.timestamps === 'string') {
            bvpData.value.timestamps = JSON.parse(bvpData.value.timestamps)
          }
        } catch (error) {
          console.error('加载BVP数据失败:', error)
        }
      }
    } else if (fuid) {
      // 加载家庭成员的最新报告
      const homeData = await healthStore.fetchHomeData(userStore.userId, fuid)
      if (homeData?.health_report) {
        currentReport.value = homeData.health_report
        bvpData.value = homeData.bvp_waveform
      }
    } else {
      // 加载用户本人的最新报告
      const homeData = await healthStore.fetchHomeData(userStore.userId)
      if (homeData?.health_report) {
        currentReport.value = homeData.health_report
        bvpData.value = homeData.bvp_waveform
      }
    }
  } catch (error) {
    console.error('加载报告数据失败:', error)
    ElMessage.error('加载报告数据失败')
  }
}

// 页面初始化
onMounted(() => {
  const reportId = route.params.reportId

  if (reportId) {
    // 如果有reportId，加载特定报告详情
    loadReportData()
  } else {
    // 否则加载报告列表
    loadFamilyMembers()
    loadReports()
  }
})
</script>

<style scoped>
.report-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 报告列表样式 */
.filter-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.report-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
}

.report-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.report-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f5f7fa;
}

.report-card-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.report-summary {
  margin-bottom: 15px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.report-actions {
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.detail-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.loading-container {
  padding: 40px;
}

.report-header {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.report-info h2 {
  margin: 0 0 16px;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.report-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.section-title {
  margin: 0 0 20px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.health-metrics,
.risk-assessment,
.bvp-waveform,
.body-metrics,
.other-metrics,
.signal-quality,
.health-suggestions {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.risk-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.risk-card h4 {
  margin: 0 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 12px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.metric-value {
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.quality-indicator {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.quality-text {
  margin-top: 16px;
  font-size: 14px;
  color: #606266;
}

.suggestions-content {
  margin-top: 16px;
}

.report-list {
  background: #fff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-content {
    gap: 20px;
  }

  .report-header,
  .health-metrics,
  .risk-assessment,
  .bvp-waveform,
  .body-metrics,
  .other-metrics,
  .signal-quality,
  .health-suggestions {
    padding: 20px;
  }

  .report-info h2 {
    font-size: 20px;
  }

  .report-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .metric-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .risk-card {
    padding: 16px;
  }
}
</style>
