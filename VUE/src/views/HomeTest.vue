<template>
  <MainLayout>
    <PageContainer title="测试首页" description="这是一个简化的测试页面">
      <template #extra>
        <el-button type="primary">
          测试按钮
        </el-button>
      </template>

      <div class="test-content">
        <h3>测试页面内容</h3>
        <p>如果你能看到这段文字，说明基本的页面结构工作正常。</p>
        
        <div style="background: #f5f7fa; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h4>基本信息测试</h4>
          <p>当前时间: {{ new Date().toLocaleString() }}</p>
          <p>用户状态: {{ userStore.user ? '已登录' : '未登录' }}</p>
          <p>用户名: {{ userStore.user?.email || '未知' }}</p>
        </div>
        
        <div style="background: #e1f5fe; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h4>健康数据测试</h4>
          <p>健康数据加载状态: {{ healthStore.isLoading ? '加载中' : '已完成' }}</p>
          <p>首页数据: {{ healthStore.homeData ? '有数据' : '无数据' }}</p>
        </div>
        
        <el-button type="primary" @click="testFunction">测试功能</el-button>
      </div>
    </PageContainer>
  </MainLayout>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useHealthStore } from '@/stores/health'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/Layout/MainLayout.vue'
import PageContainer from '@/components/Layout/PageContainer.vue'

const userStore = useUserStore()
const healthStore = useHealthStore()

const testFunction = () => {
  ElMessage.success('测试功能正常工作！')
}
</script>

<style scoped>
.test-content {
  padding: 20px;
}

.test-content h3 {
  color: #303133;
  margin-bottom: 20px;
}

.test-content p {
  color: #606266;
  line-height: 1.6;
}
</style>
