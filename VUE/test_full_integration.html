<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue前端完整集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        button.success {
            background: #28a745;
        }
        button.danger {
            background: #dc3545;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background: #28a745;
        }
        .status-offline {
            background: #dc3545;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .test-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .test-summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Vue前端完整集成测试</h1>
        <p>这个测试将验证Vue前端应用与FastAPI后端的完整对接功能</p>
        
        <!-- 测试进度 -->
        <div class="test-section">
            <h2>📊 测试进度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progress-text">准备开始测试...</div>
        </div>

        <!-- 快速操作 -->
        <div class="test-section">
            <h2>🚀 快速操作</h2>
            <button onclick="runAllTests()" class="success">运行完整测试</button>
            <button onclick="openVueApp()">打开Vue应用</button>
            <button onclick="clearResults()">清除结果</button>
            <button onclick="exportResults()">导出结果</button>
        </div>

        <!-- 测试结果网格 -->
        <div class="test-grid" id="test-grid">
            <!-- 测试卡片将动态生成 -->
        </div>

        <!-- 详细结果 -->
        <div class="test-section">
            <h2>📋 详细测试结果</h2>
            <div id="detailed-results"></div>
        </div>

        <!-- 测试总结 -->
        <div class="test-summary" id="test-summary" style="display: none;">
            <h2>🎯 测试总结</h2>
            <div id="summary-content"></div>
        </div>
    </div>

    <script>
        // 测试配置
        const TEST_CONFIG = {
            baseURL: 'http://localhost:3001',
            apiURL: '/api/v1',
            testAccount: {
                email: '<EMAIL>',
                password: 'string'
            }
        };

        // 测试状态
        let testState = {
            currentTest: 0,
            totalTests: 0,
            results: {},
            authToken: null,
            currentUser: null,
            startTime: null,
            endTime: null
        };

        // 测试定义
        const TESTS = [
            {
                id: 'backend-health',
                name: '后端健康检查',
                description: '验证FastAPI后端服务是否正常运行',
                category: 'backend',
                test: testBackendHealth
            },
            {
                id: 'user-login',
                name: '用户登录',
                description: '测试用户登录功能和token获取',
                category: 'auth',
                test: testUserLogin
            },
            {
                id: 'family-list',
                name: '家庭成员列表',
                description: '获取家庭成员列表数据',
                category: 'family',
                test: testFamilyList
            },
            {
                id: 'family-add',
                name: '添加家庭成员',
                description: '测试添加新家庭成员功能',
                category: 'family',
                test: testAddFamily
            },
            {
                id: 'home-data',
                name: '首页数据',
                description: '获取首页数据接口',
                category: 'data',
                test: testHomeData
            },
            {
                id: 'vue-pages',
                name: 'Vue页面访问',
                description: '测试所有Vue页面是否可以正常访问',
                category: 'frontend',
                test: testVuePages
            },
            {
                id: 'responsive-design',
                name: '响应式设计',
                description: '测试移动端和桌面端适配',
                category: 'ui',
                test: testResponsiveDesign
            }
        ];

        // 初始化
        window.onload = () => {
            initializeTests();
            checkExistingAuth();
        };

        function initializeTests() {
            testState.totalTests = TESTS.length;
            generateTestCards();
        }

        function generateTestCards() {
            const grid = document.getElementById('test-grid');
            grid.innerHTML = TESTS.map(test => `
                <div class="test-card" id="card-${test.id}">
                    <h3>
                        <span class="status-indicator status-offline" id="status-${test.id}"></span>
                        ${test.name}
                    </h3>
                    <p>${test.description}</p>
                    <div class="test-actions">
                        <button onclick="runSingleTest('${test.id}')">单独测试</button>
                        <span class="test-category">${test.category}</span>
                    </div>
                    <div id="result-${test.id}" class="test-result" style="display: none;"></div>
                </div>
            `).join('');
        }

        function checkExistingAuth() {
            const token = localStorage.getItem('health_detection_token');
            const user = localStorage.getItem('health_detection_user');
            
            if (token && user) {
                testState.authToken = token;
                testState.currentUser = JSON.parse(user);
                updateProgress(10, '发现已保存的登录信息');
            }
        }

        function updateProgress(percentage, text) {
            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = text;
        }

        function updateTestStatus(testId, status, result) {
            const statusEl = document.getElementById(`status-${testId}`);
            const resultEl = document.getElementById(`result-${testId}`);
            
            statusEl.className = `status-indicator status-${status}`;
            
            if (result) {
                resultEl.style.display = 'block';
                resultEl.className = `test-result ${status === 'online' ? 'success' : 'error'}`;
                resultEl.innerHTML = result;
            }
            
            testState.results[testId] = { status, result, timestamp: new Date() };
        }

        async function runAllTests() {
            testState.startTime = new Date();
            testState.currentTest = 0;
            
            updateProgress(0, '开始完整测试...');
            
            for (const test of TESTS) {
                testState.currentTest++;
                const progress = (testState.currentTest / testState.totalTests) * 100;
                
                updateProgress(progress, `正在测试: ${test.name}`);
                
                try {
                    await test.test();
                    updateTestStatus(test.id, 'online', '✅ 测试通过');
                } catch (error) {
                    updateTestStatus(test.id, 'offline', `❌ 测试失败: ${error.message}`);
                }
                
                // 添加延迟以便观察进度
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            testState.endTime = new Date();
            updateProgress(100, '所有测试完成');
            generateSummary();
        }

        async function runSingleTest(testId) {
            const test = TESTS.find(t => t.id === testId);
            if (!test) return;
            
            updateProgress(50, `测试: ${test.name}`);
            
            try {
                await test.test();
                updateTestStatus(testId, 'online', '✅ 测试通过');
                updateProgress(100, `${test.name} 测试完成`);
            } catch (error) {
                updateTestStatus(testId, 'offline', `❌ 测试失败: ${error.message}`);
                updateProgress(100, `${test.name} 测试失败`);
            }
        }

        // 具体测试函数
        async function testBackendHealth() {
            const response = await fetch('/api/v1/health/hello');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            if (data.status !== 'healthy') {
                throw new Error('后端服务状态异常');
            }
        }

        async function testUserLogin() {
            const response = await fetch('/api/v1/users/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(TEST_CONFIG.testAccount)
            });
            
            if (!response.ok) {
                throw new Error(`登录失败: HTTP ${response.status}`);
            }
            
            const data = await response.json();
            if (!data.token) {
                throw new Error('未获取到token');
            }
            
            testState.authToken = data.token;
            testState.currentUser = data;
            
            // 保存到localStorage
            localStorage.setItem('health_detection_token', data.token);
            localStorage.setItem('health_detection_user', JSON.stringify(data));
        }

        async function testFamilyList() {
            if (!testState.authToken) {
                throw new Error('需要先登录');
            }
            
            const response = await fetch(`/api/v1/users/${testState.currentUser.uid}/familylist`, {
                headers: { 'Authorization': `Bearer ${testState.authToken}` }
            });
            
            if (!response.ok) {
                throw new Error(`获取家庭成员失败: HTTP ${response.status}`);
            }
            
            const data = await response.json();
            if (!Array.isArray(data)) {
                throw new Error('返回数据格式错误');
            }
        }

        async function testAddFamily() {
            if (!testState.authToken) {
                throw new Error('需要先登录');
            }
            
            const testMember = {
                relationship: "其他",
                name: "集成测试成员" + Date.now(),
                gender: "男",
                height: 175,
                weight: 70,
                birth_year: 1990,
                avatar_url: "test.png",
                uid: testState.currentUser.uid
            };
            
            const response = await fetch('/api/v1/users/addfamily', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${testState.authToken}`
                },
                body: JSON.stringify(testMember)
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`添加家庭成员失败: ${errorData.detail || response.status}`);
            }
        }

        async function testHomeData() {
            if (!testState.authToken) {
                throw new Error('需要先登录');
            }
            
            const response = await fetch(`/api/v1/home/<USER>
                headers: { 'Authorization': `Bearer ${testState.authToken}` }
            });
            
            if (!response.ok) {
                throw new Error(`获取首页数据失败: HTTP ${response.status}`);
            }
            
            const data = await response.json();
            if (!data.metadata) {
                throw new Error('首页数据格式错误');
            }
        }

        async function testVuePages() {
            const pages = ['/', '/login', '/register', '/family', '/scan', '/report'];
            const results = [];
            
            for (const page of pages) {
                try {
                    const response = await fetch(TEST_CONFIG.baseURL + page);
                    if (response.ok) {
                        results.push(`✅ ${page}`);
                    } else {
                        results.push(`❌ ${page} (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${page} (${error.message})`);
                }
            }
            
            if (results.some(r => r.includes('❌'))) {
                throw new Error('部分页面访问失败');
            }
        }

        async function testResponsiveDesign() {
            // 这是一个模拟测试，实际应该检查CSS媒体查询等
            const viewports = [
                { width: 1920, height: 1080, name: '桌面端' },
                { width: 768, height: 1024, name: '平板端' },
                { width: 375, height: 667, name: '移动端' }
            ];
            
            // 模拟检查响应式设计
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        function generateSummary() {
            const summaryEl = document.getElementById('test-summary');
            const contentEl = document.getElementById('summary-content');
            
            const totalTests = Object.keys(testState.results).length;
            const passedTests = Object.values(testState.results).filter(r => r.status === 'online').length;
            const failedTests = totalTests - passedTests;
            
            const duration = testState.endTime - testState.startTime;
            
            contentEl.innerHTML = `
                <div class="summary-stats">
                    <h3>测试统计</h3>
                    <p>总测试数: ${totalTests}</p>
                    <p>通过: ${passedTests}</p>
                    <p>失败: ${failedTests}</p>
                    <p>成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%</p>
                    <p>耗时: ${(duration / 1000).toFixed(1)}秒</p>
                </div>
                
                <div class="summary-recommendations">
                    <h3>建议</h3>
                    ${passedTests === totalTests ? 
                        '<p class="test-result success">🎉 所有测试通过！Vue前端应用已完全对接FastAPI后端。</p>' :
                        '<p class="test-result warning">⚠️ 部分测试失败，请检查失败的测试项并修复相关问题。</p>'
                    }
                </div>
            `;
            
            summaryEl.style.display = 'block';
        }

        function openVueApp() {
            window.open(TEST_CONFIG.baseURL, '_blank');
        }

        function clearResults() {
            testState.results = {};
            document.querySelectorAll('.status-indicator').forEach(el => {
                el.className = 'status-indicator status-offline';
            });
            document.querySelectorAll('.test-result').forEach(el => {
                el.style.display = 'none';
            });
            document.getElementById('test-summary').style.display = 'none';
            updateProgress(0, '结果已清除');
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                config: TEST_CONFIG,
                results: testState.results,
                summary: {
                    total: Object.keys(testState.results).length,
                    passed: Object.values(testState.results).filter(r => r.status === 'online').length,
                    duration: testState.endTime - testState.startTime
                }
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `vue-integration-test-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
