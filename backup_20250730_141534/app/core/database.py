from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from app.core.config import settings
from app.core.log_config import get_logger

logger = get_logger('db_error', 'logs/db_error.log')

SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}"
    f"@{settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DB}"
)

try:
    engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_pre_ping=True)
except Exception as e:
    logger.error(f"数据库连接失败: {e}")
    raise

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 直接获取数据库会话（推荐用于Cython编译环境）
def get_db_session():
    """直接获取数据库会话对象"""
    return SessionLocal()

def close_db_session(db):
    """关闭数据库会话"""
    try:
        db.close()
    except Exception as e:
        logger.error(f"关闭数据库会话失败: {e}")

# 兼容性数据库管理器类（推荐用于复杂场景）
class DatabaseManager:
    """数据库会话管理器 - Cython兼容版本"""
    
    def __init__(self):
        self.db = None
    
    def __enter__(self):
        """进入上下文管理器"""
        self.db = get_db_session()
        return self.db
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        if self.db:
            close_db_session(self.db)
            self.db = None

# 创建数据库会话的便捷函数（推荐用于with语句）
def create_db_session():
    """创建数据库会话上下文管理器"""
    return DatabaseManager()

# 保留原有函数以维持向后兼容性，但不推荐在Cython环境中使用
def get_db():
    """
    数据库依赖注入函数（生成器版本）
    注意：在Cython编译环境中可能存在兼容性问题
    推荐使用 get_db_session() + close_db_session() 或 create_db_session()
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_for_api():
    """
    专门为API路由提供的数据库会话获取函数
    注意：在Cython编译环境中可能存在兼容性问题
    推荐使用 get_db_session() + close_db_session() 或 create_db_session()
    """
    db = get_db_session()
    try:
        yield db
    finally:
        close_db_session(db)
