import os
from dotenv import load_dotenv
from app.core.log_config import get_logger

logger = get_logger('config_error', 'logs/config_error.log')

class Settings:
    def __init__(self):
        # 加载.env文件
        try:
            load_dotenv()
        except Exception as e:
            logger.error(f"加载.env文件失败: {e}")

        # 需要的环境变量
        required_vars = [
            "MYSQL_HOST",
            "MYSQL_PORT", 
            "MYSQL_USER",
            "MYSQL_PASSWORD",
            "MYSQL_DB",
            "SECRET_KEY",
            "ALGORITHM",
            "ACCESS_TOKEN_EXPIRE_MINUTES",
            "OSS_ACCESS_KEY_ID",
            "OSS_ACCESS_KEY_SECRET",
            "OSS_REGION",
            "OSS_BUCKET",
            "OSS_ENDPOINT",
            "SMTP_SERVER",
            "SMTP_PORT",
            "SMTP_USERNAME", 
            "SMTP_PASSWORD",
            "FROM_EMAIL"
        ]

        for var in required_vars:
            value = os.environ.get(var)
            if not value:
                logger.error(f"环境变量 {var} 未设置，且 .env 文件中也不存在！")
                raise ValueError(f"环境变量 {var} 未设置，且 .env 文件中也不存在！")
            setattr(self, var, value)
        
        # 可选的环境变量（有默认值）
        self.ENABLE_DOCS = os.environ.get("ENABLE_DOCS", "true").lower() == "true"

settings = Settings()