from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, BigInteger
from sqlalchemy.sql import func
from app.core.database import Base

class HealthReport(Base):
    __tablename__ = "health_report"
    
    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment='主键')
    report_id = Column(String(36), unique=True, nullable=False, index=True, comment='唯一报告ID（UUID）')
    uid = Column(Integer, nullable=False, index=True, comment='用户ID')
    fuid = Column(Integer, nullable=True, index=True, comment='家庭成员ID')
    name = Column(String(255), comment='姓名')
    gender = Column(String(8), comment='性别')
    age = Column(Integer, comment='年龄')
    height = Column(Float, comment='身高(cm)')
    weight = Column(Float, comment='体重(kg)')
    bmi = Column(Float, comment='BMI')
    heart_rate = Column(Float, comment='心率')
    pulse_rate = Column(Float, comment='脉搏率')
    blood_pressure = Column(JSON, comment='血压（如120/80）')
    spo2 = Column(Float, comment='血氧饱和度')
    breathing_rate = Column(Float, comment='呼吸频率')
    cardiac_risk = Column(String(16), comment='心脏风险')
    brain_risk = Column(String(16), comment='脑风险')
    afib = Column(String(16), comment='房颤风险')
    arrhythmia = Column(String(16), comment='心率不齐风险')
    anemia = Column(String(8), comment='是否贫血')
    hemoglobin = Column(Float, comment='血红蛋白')
    risk_assessment = Column(String(32), comment='风险评估')
    signal_quality = Column(Float, comment='信号质量')
    hrv_time_domain = Column(JSON, comment='HRV时域指标')
    hrv_frequency_domain = Column(JSON, comment='HRV频域指标')
    hrv_nonlinear = Column(JSON, comment='HRV非线性指标')
    hrv = Column(JSON, comment='HRV完整结构')
    extra = Column(JSON, comment='原始返回的其它冗余字段')
    create_time = Column(DateTime, server_default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
