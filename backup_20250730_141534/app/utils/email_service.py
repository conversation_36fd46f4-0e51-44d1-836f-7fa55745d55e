import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app.core.config import settings
from app.core.log_config import get_logger

logger = get_logger('email', 'logs/email.log')

class EmailService:
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = int(settings.SMTP_PORT)
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL
        
        # 添加初始化日志
        logger.info(f"邮件服务初始化 - SMTP服务器: {self.smtp_server}:{self.smtp_port}")
        logger.info(f"发送邮箱: {self.from_email}")
    
    def send_verification_code(self, to_email: str, code: str) -> bool:
        """发送验证码邮件"""
        try:
            logger.info(f"开始发送验证码邮件到: {to_email}")
            
            # 检查配置
            if not all([self.smtp_server, self.smtp_username, self.smtp_password, self.from_email]):
                logger.error("邮件配置不完整")
                return False
            
            # 创建邮件内容
            subject = "密码重置验证码"
            body = f"""您好！

您正在申请重置密码，验证码为：{code}

验证码有效期为10分钟，请及时使用。
如果这不是您的操作，请忽略此邮件。

此邮件由系统自动发送，请勿回复。"""
            
            # 创建邮件对象
            msg = MIMEText(body, 'plain', 'utf-8')
            msg['From'] = self.from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            logger.info(f"连接SMTP服务器: {self.smtp_server}:{self.smtp_port}")
            
            # 尝试SSL连接（端口465）
            if self.smtp_port == 465:
                with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port) as server:
                    logger.info(f"登录邮箱: {self.smtp_username}")
                    server.login(self.smtp_username, self.smtp_password)
                    
                    logger.info("发送邮件中...")
                    server.sendmail(self.from_email, [to_email], msg.as_string())
            else:
                # 尝试TLS连接（端口587）
                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    server.starttls()
                    logger.info(f"登录邮箱: {self.smtp_username}")
                    server.login(self.smtp_username, self.smtp_password)
                    
                    logger.info("发送邮件中...")
                    server.sendmail(self.from_email, [to_email], msg.as_string())
            
            logger.info(f"验证码邮件发送成功: {to_email}")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP认证失败: {to_email}, 错误: {str(e)}")
            return False
        except smtplib.SMTPConnectError as e:
            logger.error(f"SMTP连接失败: {to_email}, 错误: {str(e)}")
            return False
        except smtplib.SMTPException as e:
            logger.error(f"SMTP错误: {to_email}, 错误: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"验证码邮件发送失败: {to_email}, 错误: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

# 全局邮件服务实例
email_service = EmailService()
