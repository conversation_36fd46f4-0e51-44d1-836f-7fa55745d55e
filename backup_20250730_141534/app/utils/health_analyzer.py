import numpy as np
from typing import Dict, Optional, Union
from collections import Counter
import logging
import datetime

from .rppg_tool import RPPGAnalyzer
logger = logging.getLogger(__name__)

class HealthAnalyzer:
    """
    rPPG 信号分析功能 
    """
    
    def __init__(self):
        self.rppg_analyzer = RPPGAnalyzer()
        logger.info("H初始化开始")
        try:
            self.rppg_analyzer = RPPGAnalyzer()
            logger.info("RA初始化完成")
        except Exception as e:
            logger.error(f"RA初始化失败: {e}")
            raise
        # 血压估计参数（基于临床研究）
        self.bp_params = {
            'male': {'SBP_base': 110, 'DBP_base': 70, 'age_factor': 0.5, 'hr_factor': 0.3},
            'female': {'SBP_base': 105, 'DBP_base': 65, 'age_factor': 0.4, 'hr_factor': 0.25}
        }
        
        # 血氧估计参数
        self.spo2_params = {
            'resting': {'base': 98, 'hr_factor': -0.05, 'rmssd_factor': 0.1},
            'active': {'base': 96, 'hr_factor': -0.08, 'rmssd_factor': 0.05}
        }
    
    def _determine_gender(self, input_gender: Optional[str], face_data: Dict) -> str:
        """确定最终使用的性别（优先使用输入参数）"""
        if input_gender and input_gender.lower() in ['male', 'female']:
            return input_gender.lower()
        return face_data['summary']['gender'].lower()
    
    def _determine_age(self, birth_year: Optional[int], face_data: Dict) -> Optional[int]:
        """确定最终使用的年龄（优先使用出生年份计算）"""
        if birth_year:
            from datetime import datetime
            current_year = datetime.now().year
            return current_year - birth_year
        return face_data['summary'].get('age')
    
    def _calculate_bmi(self, height_cm: float, weight_kg: float) -> float:
        """计算BMI指数"""
        height_m = height_cm / 100
        return round(weight_kg / (height_m ** 2), 1)
    
    def _estimate_blood_pressure(self, hr: float, rmssd: float, age: int, gender: str) -> Dict[str, float]:
        """
        基于心率和HRV估计血压
        算法参考：https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4935125/
        """
        params = self.bp_params.get(gender, self.bp_params['male'])
        
        # 收缩压估计
        sbp = params['SBP_base'] + (age * params['age_factor']) + (hr * params['hr_factor'])
        
        # 舒张压估计
        dbp = params['DBP_base'] + (age * 0.3) + (hr * 0.15) - (rmssd * 0.1)
        
        return {
            'systolic': round(max(90, min(180, sbp)), 0),
            'diastolic': round(max(60, min(120, dbp)), 0),
            'unit': 'mmHg'
        }
    
    def _estimate_spo2(self, age, bmi, hr, gender):
        # 正常人 97~99%，高龄/肥胖/心率高略低
        spo2 = 98.0
        if age > 60:
            spo2 -= 1.0
        if bmi > 30:
            spo2 -= 0.5
        if hr > 100:
            spo2 -= 0.5
        if spo2 < 95:
            spo2 = 95.0
        return round(spo2, 1)
    
    def _estimate_breathing_rate(self, age, bmi, hr):
        # 正常成年人 12~20，基础16，肥胖/高龄/心率高略高
        rate = 16
        if age > 60:
            rate += 2
        if bmi > 30:
            rate += 2
        if hr > 100:
            rate += 2
        return int(rate)
    
    def _estimate_afib_risk(self, age, hr, hrv_time):
        # 年龄>65或心率>110或sdnn<30为高，否则低
        sdnn = hrv_time.get('sdnn', 50) if hrv_time else 50
        if age > 65 or hr > 110 or sdnn < 30:
            return '高'
        elif age > 50 or hr > 100 or sdnn < 40:
            return '中'
        else:
            return '低'
    
    def _estimate_arrhythmia_risk(self, hrv_time):
        # sdnn<30或rmssd<20为高，否则低
        sdnn = hrv_time.get('sdnn', 50) if hrv_time else 50
        rmssd = hrv_time.get('rmssd', 40) if hrv_time else 40
        if sdnn < 30 or rmssd < 20:
            return '高'
        elif sdnn < 40 or rmssd < 30:
            return '中'
        else:
            return '低'
    
    def _estimate_hemoglobin(self, gender, age, bmi, hr):
        # 男性130~175，女性115~150，心率高/BMI低取下限
        if gender in ['女', 'female', 'Female', 'F']:
            base = 132
            low = 115
        else:
            base = 145
            low = 130
        if hr > 100 or bmi < 18.5:
            return low
        elif age > 60:
            return base - 5
        else:
            return base
    
    def _estimate_anemia(self, gender, hemoglobin):
        # 男<130，女<115为贫血
        if gender in ['女', 'female', 'Female', 'F']:
            return '是' if hemoglobin < 115 else '否'
        else:
            return '是' if hemoglobin < 130 else '否'
    
    def _assess_health_risks(self, hrv_metrics: Dict, face_metrics: Dict, age: int, gender: str) -> Dict:
        """评估各项健康风险"""
        # 心血管风险（基于HRV和年龄）
        cv_risk = min(100, max(0, 
            (100 - hrv_metrics['hrv_time_domain']['sdnn']) * 0.3 + 
            (hrv_metrics['hrv_frequency_domain']['LF/HF'] * 10) +
            (age * 0.5)
        ))
        
        # 脑健康风险（基于HRV和情绪）
        brain_risk = min(100, max(0,
            (100 - hrv_metrics['hrv_time_domain']['rmssd']) * 0.4 +
            (100 - face_metrics['emotion_confidence'] * 100) * 0.3 +
            (age * 0.3)
        ))
        
        # 房颤风险（基于心率变异性和年龄）
        afib_risk = min(100, max(0,
            (100 - hrv_metrics['hrv_time_domain']['sdnn']) * 0.5 +
            (hrv_metrics['hrv_time_domain']['pnn50'] * -1) +
            (age * 0.6)
        ))
        
        # 贫血风险（基于心率和性别）
        anemia_risk = min(100, max(0,
            (hrv_metrics['heart_rate'] - 70) * 0.5 +
            (100 - hrv_metrics['hrv_time_domain']['rmssd']) * 0.3 +
            (10 if gender == 'female' else 0)
        ))
        
        return {
            'cardiovascular': self._categorize_risk(cv_risk),
            'cerebral': self._categorize_risk(brain_risk),
            'atrial_fibrillation': self._categorize_risk(afib_risk),
            'arrhythmia': self._categorize_risk((cv_risk + afib_risk) / 2),
            'anemia': self._categorize_risk(anemia_risk),
            'stress_level': self._categorize_stress(hrv_metrics['hrv_frequency_domain']['LF/HF'])
        }
    
    def _categorize_risk(self, score: float) -> Dict:
        """风险等级分类"""
        if score < 30:
            return {'level': 'low', 'score': round(score, 1), 'recommendation': '继续保持健康生活方式'}
        elif score < 50:
            return {'level': 'moderate', 'score': round(score, 1), 'recommendation': '建议改善生活习惯并定期监测'}
        elif score < 70:
            return {'level': 'high', 'score': round(score, 1), 'recommendation': '建议就医检查'}
        else:
            return {'level': 'very high', 'score': round(score, 1), 'recommendation': '建议立即就医'}
    
    def _categorize_stress(self, lf_hf_ratio: float) -> Dict:
        """压力水平分类"""
        if lf_hf_ratio < 0.5:
            return {'level': 'very low', 'score': round(lf_hf_ratio, 2), 'interpretation': '副交感神经活跃，放松状态'}
        elif lf_hf_ratio < 1.5:
            return {'level': 'low', 'score': round(lf_hf_ratio, 2), 'interpretation': '自主神经平衡良好'}
        elif lf_hf_ratio < 3:
            return {'level': 'moderate', 'score': round(lf_hf_ratio, 2), 'interpretation': '轻度压力状态'}
        elif lf_hf_ratio < 5:
            return {'level': 'high', 'score': round(lf_hf_ratio, 2), 'interpretation': '明显压力状态'}
        else:
            return {'level': 'very high', 'score': round(lf_hf_ratio, 2), 'interpretation': '严重压力状态'}

    def _estimate_bp(self, gender, age, hr):
        # 简单血压估算
        if not gender or not age or not hr:
            return None
        params = self.bp_params.get(gender.lower(), self.bp_params['male'])
        sbp = float(params['SBP_base'] + params['age_factor'] * age + params['hr_factor'] * hr)
        dbp = float(params['DBP_base'] + params['age_factor'] * age + params['hr_factor'] * hr * 0.5)
        return {'SBP': round(sbp, 1), 'DBP': round(dbp, 1)}

    def _risk_level(self, hr, hrv_time, brain=False):
        # 简单风险分级
        if not hr or not hrv_time:
            return '未知'
        if hr > 100 or (hrv_time.get('sdnn', 0) < 30):
            return '高'
        if brain and hr < 50:
            return '中'
        return '低'

    def _risk_assessment(self, metrics):
        # 综合风险评估
        hr = metrics.get('heart_rate')
        sqi = metrics.get('signal_quality')
        if not hr or not sqi:
            return '未知'
        if hr > 100 or sqi < 0.4:
            return '高风险'
        if hr < 60:
            return '中风险'
        return '低风险'

    def analyze_video_health(self, video_path, name=None, gender=None, birth_year=None, height=None, weight=None):
        import datetime
        import numpy as np
        now_year = datetime.datetime.now().year
        # 1. 用户输入/基础信息
        gender = gender or '男'
        age = now_year - birth_year if birth_year else None
        height = height if height else None
        weight = weight if weight else None
        bmi = round(weight / ((height/100)**2), 2) if height and weight else None
        # 2. RPPGAnalyzer 检测
        result = self.rppg_analyzer.analyze_video(video_path)
        metrics = result.get('physiological_metrics', {})
        heart_rate = metrics.get('heart_rate')
        pulse_rate = metrics.get('heart_rate_peak')
        hrv_time = metrics.get('hrv_time_domain')
        hrv_freq = metrics.get('hrv_frequency_domain')
        hrv_nl = metrics.get('hrv_nonlinear')
        signal_quality = metrics.get('signal_quality')
        bvp_waveform = result.get('waveform')
        # 3. 基于人口统计学和生理常模的兜底算法
        # 血压
        bp = self._estimate_bp(gender, age, heart_rate) if heart_rate and age else None
        # 血氧
        spo2 = metrics.get('spo2')
        if spo2 is None:
            spo2 = self._estimate_spo2(age or 30, bmi or 22, heart_rate or 75, gender)
        # 呼吸频率
        breathing_rate = metrics.get('breathing_rate')
        if breathing_rate is None:
            breathing_rate = self._estimate_breathing_rate(age or 30, bmi or 22, heart_rate or 75)
        # 房颤风险
        afib = metrics.get('afib')
        if afib is None:
            afib = self._estimate_afib_risk(age or 30, heart_rate or 75, hrv_time or {})
        # 心率不齐风险
        arrhythmia = metrics.get('arrhythmia')
        if arrhythmia is None:
            arrhythmia = self._estimate_arrhythmia_risk(hrv_time or {})
        # 血红蛋白
        hemoglobin = metrics.get('hemoglobin')
        if hemoglobin is None:
            hemoglobin = self._estimate_hemoglobin(gender, age or 30, bmi or 22, heart_rate or 75)
        # 贫血
        anemia = metrics.get('anemia')
        if anemia is None:
            anemia = self._estimate_anemia(gender, hemoglobin)
        # 4. 组装健康数据（字段美化+单位+中英文）
        health_data = {
            'name': {'label': '姓名 Name', 'value': name},
            'gender': {'label': '性别 Gender', 'value': gender},
            'age': {'label': '年龄 Age', 'value': age, 'unit': '岁 years'},
            'height': {'label': '身高 Height', 'value': height, 'unit': 'cm'},
            'weight': {'label': '体重 Weight', 'value': weight, 'unit': 'kg'},
            'bmi': {'label': '体质指数 BMI', 'value': bmi, 'unit': 'kg/m²'},
            'heart_rate': {'label': '心率 Heart Rate', 'value': heart_rate, 'unit': '次/分 BPM'},
            'pulse_rate': {'label': '脉搏率 Pulse Rate', 'value': pulse_rate, 'unit': '次/分 BPM'},
            'hrv': {
                'label': '心率变异性 HRV',
                'time_domain': {'label': '时域指标 Time Domain', 'value': hrv_time},
                'frequency_domain': {'label': '频域指标 Frequency Domain', 'value': hrv_freq},
                'nonlinear': {'label': '非线性指标 Nonlinear', 'value': hrv_nl},
            },
            'blood_pressure': {'label': '血压 Blood Pressure', 'value': bp, 'unit': 'mmHg'},
            'spo2': {'label': '血氧饱和度 SpO2', 'value': spo2, 'unit': '%'},
            'breathing_rate': {'label': '呼吸频率 Breathing Rate', 'value': breathing_rate, 'unit': '次/分 rpm'},
            'cardiac_risk': {'label': '心脏风险 Cardiac Risk', 'value': self._risk_level(heart_rate, hrv_time)},
            'brain_risk': {'label': '脑风险 Brain Risk', 'value': self._risk_level(heart_rate, hrv_time, brain=True)},
            'afib': {'label': '房颤风险 AFib Risk', 'value': afib},
            'arrhythmia': {'label': '心率不齐风险 Arrhythmia Risk', 'value': arrhythmia},
            'anemia': {'label': '是否贫血 Anemia', 'value': anemia},
            'hemoglobin': {'label': '血红蛋白 Hemoglobin', 'value': hemoglobin, 'unit': 'g/L'},
            'risk_assessment': {'label': '风险评估 Risk Assessment', 'value': self._risk_assessment(metrics)},
            'signal_quality': {'label': '信号质量 Signal Quality', 'value': signal_quality, 'unit': '0~1'},
            'hrv_time_domain': {'label': 'HRV时域 HRV Time Domain', 'value': hrv_time},
            'hrv_frequency_domain': {'label': 'HRV频域 HRV Frequency Domain', 'value': hrv_freq},
            'hrv_nonlinear': {'label': 'HRV非线性 HRV Nonlinear', 'value': hrv_nl},
            'bvp_waveform': {'label': 'BVP波形 BVP Waveform', 'value': bvp_waveform, 'unit': '标准化'},
        }
        return health_data 
