import os
from typing import Dict, Tuple, Optional, Generator, Any
import numpy as np
import time

import rppg as xqwerty

class RPPGAnalyzer:
    """
    open-rppg 工具箱封装类
    功能：
    - 封装单视频文件处理和实时视频流处理
    - 支持所有预训练模型动态切换
    - 返回标准化生理信号数据
    
    初始化参数：
    :param model_name: str 预训练模型名称，可通过supported_models查看可选值
    """
    
    def __init__(self, model_name: str = 'ME-chunk.rlap'):
        self.supported_models = xqwerty.supported_models
        if model_name not in self.supported_models:
            raise ValueError(f"Unsupported model. Available: {self.supported_models}")
        
        self.model = xqwerty.Model(model_name)
        self.current_model = model_name
    
    def analyze_video(self, video_path: str) -> Dict:
        """
        处理单个视频文件并返回完整分析结果
        
        返回数据结构说明：
        {
            'metadata': {                     # 元数据信息
                'model': str,                # 使用的模型名称
                'video_duration': float,     # 视频总时长(秒)
                'processing_time': float     # 实际处理耗时(秒)
            },
            'physiological_metrics': {       # 生理指标数据
                'heart_rate': float,         # 基于FFT的平均心率(BPM)
                'heart_rate_peak': float,    # 基于峰值检测的心率(BPM)
                'signal_quality': float,     # 信号质量指数(0-1, 越高越好)
                'breathing_rate': float,     # 估计的呼吸频率(Hz)
                
                # 时域HRV指标
                'hrv_time_domain': {         
                    'ibi': float,            # 平均心跳间隔(毫秒)
                    'sdnn': float,           # NN间期标准差(反映整体变异)
                    'rmssd': float,          # 相邻NN间期差值的均方根(反映短期变异)
                    'pnn20': float,          # 相邻间隔>20ms的比例(0-1)
                    'pnn50': float,          # 相邻间隔>50ms的比例(0-1)
                    'hr_mad': float          # 心率中位数绝对偏差
                },
                
                # 频域HRV指标
                'hrv_frequency_domain': {    
                    'VLF': float,            # 极低频功率(0-0.04Hz, 反映体温调节等)
                    'TP': float,             # 总功率(0-0.4Hz)
                    'LF': float,             # 低频功率(0.04-0.15Hz, 反映交感神经活动)
                    'HF': float,             # 高频功率(0.15-0.4Hz, 反映副交感神经活动)
                    'LF/HF': float           # 交感-副交感平衡指标
                },
                
                # 非线性HRV指标
                'hrv_nonlinear': {           
                    'sd1': float,            # Poincaré图短轴(瞬时变异)
                    'sd2': float,            # Poincaré图长轴(长期变异)
                    's': float,              # Poincaré椭圆面积
                    'sd1/sd2': float         # 短期/长期变异比
                }
            },
            'waveform': {                    # 波形数据
                'bvp': list[float],          # 处理后的BVP波形(标准化单位)
                'timestamps': list[float],   # 每个采样点对应的时间戳(秒)
                'sampling_rate': float       # 实际采样率(Hz)
            }
        }
        """
        result = self.model.process_video(video_path)
        bvp, timestamps = self.model.bvp()
        
        return {
            'metadata': {
                'model': self.current_model,
                'video_duration': self.model.now,
                'processing_time': result.get('latency', 0)
            },
            'physiological_metrics': {
                'heart_rate': result['hr'],
                'heart_rate_peak': result['hrv'].get('bpm', None),
                'signal_quality': result['SQI'],
                'breathing_rate': result.get('breathingrate', None),
                'hrv_time_domain': {
                    'ibi': result['hrv'].get('ibi', None),
                    'sdnn': result['hrv'].get('sdnn', None),
                    'rmssd': result['hrv'].get('rmssd', None),
                    'pnn20': result['hrv'].get('pnn20', None),
                    'pnn50': result['hrv'].get('pnn50', None),
                    'hr_mad': result['hrv'].get('hr_mad', None)
                },
                'hrv_frequency_domain': {
                    'VLF': result['hrv'].get('VLF', None),
                    'TP': result['hrv'].get('TP', None),
                    'LF': result['hrv'].get('LF', None),
                    'HF': result['hrv'].get('HF', None),
                    'LF/HF': result['hrv'].get('LF/HF', None)
                },
                'hrv_nonlinear': {
                    'sd1': result['hrv'].get('sd1', None),
                    'sd2': result['hrv'].get('sd2', None),
                    's': result['hrv'].get('s', None),
                    'sd1/sd2': result['hrv'].get('sd1/sd2', None)
                }
            },
            'waveform': {
                'bvp': bvp.tolist(),
                'timestamps': timestamps.tolist(),
                'sampling_rate': self._calculate_sampling_rate(timestamps)
            }
        }
    
    def realtime_analysis(self, 
                         camera_index: int = 0,
                         window_size: int = 15) -> Generator[Dict, None, None]:
        """
        实时视频流处理(生成器模式)
        
        返回数据结构说明：
        {
            'metadata': {                   # 元数据信息
                'model': str,               # 使用的模型名称
                'window_size': int,         # 分析窗口大小(秒)
                'current_time': float       # 当前处理时间点(秒)
            },
            'physiological_metrics': {      # 实时生理指标(仅含窗口期数据)
                'heart_rate': float,        # 当前窗口平均心率
                'heart_rate_peak': float,   # 基于峰值检测的心率
                'signal_quality': float,    # 当前信号质量
                'breathing_rate': float,    # 估计呼吸频率(可能为None)
                
                # 时域HRV指标(短期分析可能不含完整指标)
                'hrv_time_domain': {        
                    'ibi': float,           # 平均心跳间隔
                    'sdnn': float,          # NN间期标准差
                    'rmssd': float,         # 相邻NN间期差值的均方根
                    'pnn20': float,         # 相邻间隔>20ms的比例
                    'pnn50': float,         # 相邻间隔>50ms的比例
                    'hr_mad': float         # 心率中位数绝对偏差
                },
                
                # 频域HRV指标(仅当窗口足够大时存在)
                'hrv_frequency_domain': {
                    'LF/HF': float          # 交感-副交感平衡指标
                } | None
            },
            'waveform': {                   # 波形数据
                'bvp': list[float],         # 窗口期BVP波形
                'timestamps': list[float],  # 对应时间戳
                'sampling_rate': float      # 实际采样率
            },
            'video_frame': np.ndarray | None # 可选视频帧数据(HWC格式BGR图像)
        }
        """
        with self.model.video_capture(camera_index):
            while True:
                hr_result = self.model.hr(start=-window_size)
                
                if not hr_result:
                    time.sleep(0.1)
                    continue
                
                bvp, timestamps = self.model.bvp(start=-window_size)
                
                yield {
                    'metadata': {
                        'model': self.current_model,
                        'window_size': window_size,
                        'current_time': self.model.now
                    },
                    'physiological_metrics': {
                        'heart_rate': hr_result['hr'],
                        'heart_rate_peak': hr_result['hrv'].get('bpm', None),
                        'signal_quality': hr_result['SQI'],
                        'breathing_rate': hr_result.get('breathingrate', None),
                        'hrv_time_domain': {
                            'ibi': hr_result['hrv'].get('ibi', None),
                            'sdnn': hr_result['hrv'].get('sdnn', None),
                            'rmssd': hr_result['hrv'].get('rmssd', None),
                            'pnn20': hr_result['hrv'].get('pnn20', None),
                            'pnn50': hr_result['hrv'].get('pnn50', None),
                            'hr_mad': hr_result['hrv'].get('hr_mad', None)
                        },
                        'hrv_frequency_domain': {
                            'LF/HF': hr_result['hrv'].get('LF/HF', None)
                        } if 'LF' in hr_result['hrv'] else None
                    },
                    'waveform': {
                        'bvp': bvp.tolist(),
                        'timestamps': timestamps.tolist(),
                        'sampling_rate': self._calculate_sampling_rate(timestamps)
                    },
                    'video_frame': next(self.model.preview)[0] if hasattr(self.model, 'preview') else None
                }
                
                time.sleep(1)  # 控制输出频率
    
    def _calculate_sampling_rate(self, timestamps: np.ndarray) -> float:
        """计算实际采样率(Hz)"""
        if len(timestamps) < 2:
            return 0.0
        return float(1 / np.mean(np.diff(timestamps)))
    
    @property
    def available_models(self) -> list:
        """获取所有支持的模型名称列表"""
        return self.supported_models
    
    def change_model(self, model_name: str):
        """
        动态切换预训练模型
        :param model_name: 模型名称(必须存在于supported_models中)
        """
        if model_name not in self.supported_models:
            raise ValueError(f"Unsupported model. Available: {self.supported_models}")
        self.model = xqwerty.Model(model_name)
        self.current_model = model_name 